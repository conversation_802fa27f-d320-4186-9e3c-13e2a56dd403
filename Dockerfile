FROM imeepos/node:lts-slim
RUN mkdir /imeepos
RUN mkdir /imeepos/logs
COPY ./package.json /imeepos/package.json
COPY ./templates /imeepos/templates
COPY ./static /imeepos/static
COPY ./tsconfig.json /imeepos/tsconfig.json
COPY ./processes.json /imeepos/processes.json
COPY ./package-lock.json /imeepos/package-lock.json
COPY ./pnpm-lock.yaml /imeepos/pnpm-lock.yaml
COPY ./dist /imeepos/dist
COPY ./src /imeepos/src

WORKDIR /imeepos
RUN npm i -g pnpm --registry=https://registry.npmmirror.com
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=false 
ENV PUPPETEER_DOWNLOAD_HOST=https://registry.npmmirror.com/-/binary
RUN pnpm i 
EXPOSE 8081

VOLUME "/imeepos/config"

CMD [ "./node_modules/pm2/bin/pm2-runtime","start","dist/master.js"]
