FROM node:latest

RUN MKDIR /imeepos

ENV WORKDIR=/imeepos
ENV NPM_CONFIG_REGISTRY=http://localhost:4873


RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories

# Installs latest Chromium (100) package.
RUN apk add --no-cache chromium nss freetype harfbuzz ca-certificates ttf-freefont yarn
RUN apk --no-cache add openssl ca-certificates g++ gcc libgcc libstdc++ linux-headers make python3 git 
RUN apk add --no-cache patch

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true 
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

RUN npm set registry http://localhost:4873/

WORKDIR ${WORKDIR}
COPY package.json ${WORKDIR}/
COPY pnpm-lock.yaml ${WORKDIR}/