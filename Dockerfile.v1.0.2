FROM node

# RUN apk update && apk add --no-cache --virtual .fetch-deps python3 make g++ gcc


RUN mkdir /imeepos
COPY ./imeepos /imeepos
# RUN pnpm i puppeteer

VOLUME /imeepos
WORKDIR /imeepos
RUN npm i -g pnpm --reigistry=https://registry.npmmirror.com
RUN pnpm i --prod --registry=https://registry.npmmirror.com

EXPOSE 8081/tcp

CMD ["/bin/bash"]

# CMD ["./node_modules/.bin/pm2","start", "process.json"]


