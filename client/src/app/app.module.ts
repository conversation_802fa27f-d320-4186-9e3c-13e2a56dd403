import { APP_INITIALIZER, Injector, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { HttpClientModule } from '@angular/common/http'
import { AppComponent } from './app.component';
import { FormsModule } from '@angular/forms'
import { RouterModule } from '@angular/router'
import { NzLayoutModule } from 'ng-zorro-antd/layout'
import { NzIconModule, NzIconService } from 'ng-zorro-antd/icon';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzModalModule } from 'ng-zorro-antd/modal';

import { PlatformListModule } from './components/platform-list/platform-list.module';
import { EventListModule } from './components/event-list/event-list.module';
import { MenuListModule } from './components/menu-list/menu-list.module';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { AdminLayoutComponent } from './layout/admin/admin.component';
import { UserGuard } from './services/user-guard';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzListModule } from 'ng-zorro-antd/list';

import { registerLocaleData } from '@angular/common';
import zh from '@angular/common/locales/zh';
registerLocaleData(zh);

@NgModule({
  declarations: [
    AppComponent,
    AdminLayoutComponent
  ],
  imports: [
    BrowserModule,
    HttpClientModule,
    BrowserAnimationsModule,
    FormsModule,
    NzBadgeModule,
    NzIconModule,
    NzListModule,
    NzPopoverModule,
    RouterModule.forRoot([{
      path: '',
      pathMatch: 'full',
      redirectTo: 'home'
    }, {
      path: 'big',
      loadChildren: () => import('./pages/big-page/big-page.module').then(res => res.BigPageModule)
    }, {
      path: '',
      component: AdminLayoutComponent,
      canActivate: [
        UserGuard
      ],
      children: [
        {
          path: 'account/search',
          loadChildren: () => import('./pages/account/account.module').then(res => res.AccountModule)
        },
        {
          path: 'task',
          loadChildren: () => import('./pages/task-page/task-page.module').then(res => res.TaskPageModule)
        },
        {
          path: 'bind-user',
          loadChildren: () => import('./pages/bind-user/bind-user.module').then(res => res.BindUserModule)
        },
        {
          path: 'gonggao',
          loadChildren: () => import('./pages/gonggao/gonggao.module').then(res => res.GonggaoModule)
        },
        {
          path: 'search',
          loadChildren: () => import('./pages/search-page/search-page.module').then(res => res.SearchPageModule)
        },
        {
          path: 'home',
          loadChildren: () => import('./pages/home-page/home-page.module').then(res => res.HomePageModule)
        }, {
          path: 'rest',
          loadChildren: () => import('./pages/rest-page/rest-page.module').then(res => res.RestPageModule)
        }, {
          path: 'trend-topic',
          loadChildren: () => import('./pages/trend-topic/trend-topic.module').then(res => res.TrendTopicModule)
        },
        {
          path: 'doc',
          loadChildren: () => import('./pages/doc-page/doc-page.module').then(res => res.DocPageModule)
        },
        {
          path: 'crawling',
          loadChildren: () => import('./pages/weibo-crawling/weibo-crawling.module').then(res => res.WeiboCrawlingModule)
        },
        {
          path: 'nlp',
          loadChildren: () => import('./pages/nlp/nlp.module').then(res => res.NlpModule)
        },
        {
          path: 'big-detail',
          loadChildren: () => import('./pages/big-detail/big-detail.module').then(res => res.BigDetailModule)
        },
      ]
    }, {
      path: 'loginAdmin',
      loadChildren: () => import('./pages/login-page/login-page.module').then(res => res.LoginPageModule)
    }]),
    NzLayoutModule,
    NzIconModule.forRoot([]),
    NzButtonModule,
    PlatformListModule,
    NzCardModule,
    EventListModule,
    NzTagModule,
    NzModalModule,
    NzMessageModule,
    NzInputModule,
    NzSelectModule,
    NzMenuModule,
    MenuListModule,
  ],
  providers: [{
    provide: APP_INITIALIZER,
    useFactory: (injector: Injector) => {
      const icon = injector.get(NzIconService)
      return async () => {
        icon.fetchFromIconfont({
          scriptUrl: './assets/iconfont/iconfont.js'
        })
      }
    },
    multi: true,
    deps: [Injector]
  }],
  bootstrap: [AppComponent]
})
export class AppModule { }
