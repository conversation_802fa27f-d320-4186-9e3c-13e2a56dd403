import { Directive, ElementRef, Input } from "@angular/core";
import { RegisteredSeriesOption, EChartsOption, EChartsType } from 'echarts'
import * as echarts from 'echarts'
type Values<T> = T[keyof T];

@Directive({
    selector: '[echart-gauge]'
})
export class GaugeDirective {
    _val: any[] = []

    @Input()
    title: string = `pie`

    @Input()
    set data(val: any[]) {
        if (val && val.length > 0) {
            this._val = val;
            this.chart && this.chart.setOption(this.option);
        }
    }

    get serie(): Values<RegisteredSeriesOption> {
        return {
            name: 'Pressure',
            type: 'gauge',
            detail: {
                formatter: (value: number)=>{
                    if(value > 72){
                        return '正向'
                    }else if(value < 40){
                        return '负向'
                    }else{
                        return '中立'
                    }
                }
            },
            data: this._val
        }
    }

    get option(): EChartsOption {
        return {
            backgroundColor: '#fff',
            series: [
                this.serie
            ]
        }
    }
    chart!: EChartsType;
    constructor(private ele: ElementRef) { }
    ngOnDestroy(): void { }
    ngOnInit(): void {
        this.chart = echarts.init(this.ele.nativeElement);
        console.log(this.option)
        this.option && this.chart.setOption(this.option);
    }

}