import { Directive, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef, Input } from '@angular/core'
import * as echarts from 'echarts';
import { EChartsOption } from 'echarts'

@Directive({
    selector: '[echart-line]',
})
export class LineDirective implements OnInit, OnD<PERSON>roy {
    @Input()
    smooth: boolean = false
    @Input()
    boundaryGap: boolean = false
    constructor(private ele: ElementRef){}
    ngOnDestroy(): void {

    }
    ngOnInit(): void {
        var chartDom = this.ele.nativeElement
        var myChart = echarts.init(chartDom);
        var option: EChartsOption;
        option = {
            xAxis: {
                type: 'category',
                data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                boundaryGap: this.boundaryGap
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    data: [150, 230, 224, 218, 135, 147, 260],
                    type: 'line',
                    smooth: this.smooth,
                    areaStyle: {}
                },
                {
                    data: [100, 50, 300, 224, 180, 147, 145],
                    type: 'line',
                    smooth: this.smooth,
                    areaStyle: {}
                }
            ]
        };
        option && myChart.setOption(option);
    }
}