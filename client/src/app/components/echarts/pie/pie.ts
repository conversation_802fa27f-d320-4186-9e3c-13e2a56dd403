import { Directive, OnInit, <PERSON><PERSON><PERSON><PERSON>, ElementRef, Input } from "@angular/core";
import * as echarts from 'echarts';
import { EChartsOption, RegisteredSeriesOption, EChartsType } from 'echarts'
type Values<T> = T[keyof T];

@Directive({
    selector: '[echart-pie]'
})
export class PieDirective implements OnInit, OnDestroy {
    _val: any[] = []

    @Input()
    title: string = `pie`

    @Input()
    set data(val: any[]) {
        this._val = val;
        this.chart && this.chart.setOption(this.option);
    }

    get serie(): Values<RegisteredSeriesOption> {
        return {
            name: this.title,
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
            },
            label: {
                show: false,
                position: 'center'
            },
            emphasis: {
                label: {
                    show: true,
                    fontSize: '40',
                    fontWeight: 'bold'
                }
            },
            labelLine: {
                show: false,
            },
            data: this._val
        }
    }

    get option(): EChartsOption {
        return {
            tooltip: {
                trigger: 'item'
            },
            backgroundColor: '#fff',
            legend: {
                top: '5%',
                left: 'center'
            },
            series: [
                this.serie
            ]
        }
    }
    chart!: EChartsType;
    constructor(private ele: ElementRef) { }
    ngOnDestroy(): void { }
    ngOnInit(): void {
        this.chart = echarts.init(this.ele.nativeElement);
        this.option && this.chart.setOption(this.option);
    }
}
