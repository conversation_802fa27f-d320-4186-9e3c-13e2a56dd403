import { Directive, ElementRef, Input } from "@angular/core";
import { RegisteredSeriesOption, EChartsOption, EChartsType } from 'echarts'
import * as echarts from 'echarts';
import "echarts-wordcloud"

declare module 'echarts/types/dist/echarts' {
    interface RegisteredSeriesOption {
        wordCloud: any
    }
}
type Values<T> = T[keyof T];

@Directive({
    selector: '[word-cloud]'
})
export class WordCloudDirective {
    _val: any[] = []

    @Input()
    title: string = `pie`

    @Input()
    mask: string = ``

    @Input()
    set data(val: any[]) {
        this._val = val;
        this.chart && this.chart.setOption(this.option);
    }

    get serie(): Values<RegisteredSeriesOption> {
        let maskImage: any = new Image();
        if(this.mask && this.mask.length>0){
            maskImage.src = this.mask
        }else{
            maskImage = undefined
        }
        return {
            type: 'wordCloud',
            sizeRange: [14, 48],
            rotationRange: [0, 0],
            gridSize: 0,
            shape: 'pentagon',
            maskImage: undefined,
            drawOutOfBound: false,
            layoutAnimation: true,
            keepAspect: true,
            textStyle: {
                fontWeight: 'bold',
                color: function () {
                    return 'rgb(' + [
                        Math.round(Math.random() * 80) + 50,
                        Math.round(Math.random() * 100),
                        Math.round(Math.random() * 100) + 50
                    ].join(',') + ')';
                }
            },
            emphasis: {
                textStyle: {
                    color: '#528'
                }
            },
            // Data is an array. Each array item must have name and value property.
            data: this._val
        }
    }

    get option(): EChartsOption {
        return {
            tooltip: {
                show: false
            },
            legend: {
                top: '0%',
                left: 'center'
            },
            series: [
                this.serie
            ]
        }
    }
    chart!: EChartsType;
    constructor(private ele: ElementRef) { }
    ngOnDestroy(): void { }
    ngOnInit(): void {
        this.chart = echarts.init(this.ele.nativeElement);
        this.option && this.chart.setOption(this.option);
    }
}
