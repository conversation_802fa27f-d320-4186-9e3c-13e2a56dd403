<nz-input-group nzSearch [nzAddOnBefore]="addOnBeforeTemplate" nzSize="large" [nzAddOnAfter]="nzAddOnAfterTemplate">
    <input nz-input [nzAutocomplete]="auto" [(ngModel)]="keyword" placeholder="请输入关键字后点击平台搜索" />
    <nz-autocomplete [nzDataSource]="options" nzBackfill #auto></nz-autocomplete>
</nz-input-group>

<ng-template #nzAddOnAfterTemplate>
    <button nz-button nzType="primary" nzSize="large" nzSearch (click)="search()">
        <i nz-icon nzType="search"></i>
    </button>
</ng-template>

<ng-template #addOnBeforeTemplate>
    <nz-select style="min-width:180px;" (ngModelChange)="ngModelChange($event)" nzShowSearch nzServerSearch (nzOnSearch)="onSearch($event)" [(ngModel)]="eid"
        nzPlaceHolder="请选择事件">
        <nz-option [nzLabel]="item.title" [nzValue]="item.id" *ngFor="let item of list"></nz-option>
    </nz-select>
</ng-template>
