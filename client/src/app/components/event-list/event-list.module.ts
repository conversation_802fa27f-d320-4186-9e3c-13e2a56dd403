import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";

import { EventListComponent } from "./event-list";
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzIconModule } from "ng-zorro-antd/icon";
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';

@NgModule({
    declarations: [
        EventListComponent
    ],
    exports: [
        EventListComponent
    ],
    imports: [
        CommonModule,
        NzButtonModule,
        NzIconModule,
        NzBadgeModule,
        NzModalModule,
        NzInputModule,
        NzFormModule,
        NzSelectModule,
        NzAlertModule,
        ReactiveFormsModule,
        FormsModule,
        NzAutocompleteModule
    ]
})
export class EventListModule { }