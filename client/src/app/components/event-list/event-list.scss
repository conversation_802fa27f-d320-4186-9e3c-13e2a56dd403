:host {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.event-item {
  padding: 5px 12px;
  width: 300px;
  margin: 10px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  cursor: pointer;
  border: 1px solid #efefef;
  &:hover {
    opacity: .8;
  }
  .event-title {
    display: inline-block;
    font-size: 18px;
    font-weight: 500;
    .title {
      padding-right: 15px;
    }
  }
  .event-address {
    display: inline-block;
    font-size: 12px;
    color: gray;
    margin-left: 15px;
    font-weight: 400;
  }
  .event-detail {
    margin-top: 10px;
    font-size: 14px;
    font-family: fangsong;
    height: 45px;
  }
  .event-add {
    font-size: 30px;
    text-align: center;
  }
}
