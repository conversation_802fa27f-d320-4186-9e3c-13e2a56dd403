import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { NzMessageService } from "ng-zorro-antd/message";
import { Subject, takeUntil } from "rxjs";
import { Sdk } from "../../services/sdk";

@Component({
    selector: 'event-list',
    templateUrl: `./event-list.html`,
    styleUrls: ['./event-list.scss']
})
export class EventListComponent implements OnInit, OnDestroy {
    get sdk() {
        return this.injector.get(Sdk)
    }
    get msgService() {
        return this.injector.get(NzMessageService)
    }
    private destory$: Subject<void> = new Subject();

    list: any[] = [];
    users: any[] = [];
    msg: any;
    constructor(private injector: Injector) { }

    loadEvent(params: any) {
        this.sdk.post('@nger/rest/wb_event/find', params).pipe(
            takeUntil(this.destory$)
        ).subscribe({
            next: (res: any) => {
                this.list = res.list;
            }
        })
    }

    onSearch(keyword: any) {
        if (keyword.length > 0) {
            this.loadEvent({
                title: `like:%${keyword}%`
            });
        }
    }

    init() {
        this.loadEvent({});
    }

    ngOnDestroy(): void {
        this.destory$.next();
        this.destory$.complete();
    }
    ngOnInit(): void {
        this.init();
    }
    get fb() {
        return this.injector.get(FormBuilder)
    }
    searchForm!: FormGroup;

    options: string[] = [];

    search() {
        const values = {
            eid: this.eid,
            keyword: this.keyword
        }
        this.sdk.post('@nger/weibo/topic', values).pipe(
            takeUntil(this.destory$)
        ).subscribe({
            next: (res: any) => {
                if (res.code === 2000) {
                    this.msgService.success(res.msg || '恭喜您，操作成功')
                } else {
                    this.msgService.error(res.msg)
                }
            }
        })
    }

    ngModelChange(e: any) {
        const event = this.list.find(it => it.id === e);
        if (event) this.options = event.keywords;
    }
    eid!: number;
    type!: string;
    keyword!: string;
}
