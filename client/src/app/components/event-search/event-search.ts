import { Component, EventEmitter, Injector, Output } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd/message";
import { Subject, takeUntil } from "rxjs";
import { Sdk } from "../../services/sdk";

@Component({
    selector: 'event-search',
    templateUrl: './event-search.html',
    styleUrls: ['./event-search.scss']
})
export class EventSearchComponent { 
    get sdk() {
        return this.injector.get(Sdk)
    }
    get msgService() {
        return this.injector.get(NzMessageService)
    }
    private destory$: Subject<void> = new Subject();
    list: any[] = [];
    eid!: any;

    @Output()
    onSelect: EventEmitter<any> = new EventEmitter()
    constructor(private injector: Injector){}
    ngModelChange(e: any) {
        const event = this.list.find(it => it.id === e);
        this.onSelect.emit(event)
    }
    onSearch(keyword: any) {
        if (keyword.length > 0) {
            this.loadEvent({
                title: `like:%${keyword}%`
            });
        }
    }
    loadEvent(params: any) {
        this.sdk.post('@nger/rest/wb_event/find', params).pipe(
            takeUntil(this.destory$)
        ).subscribe({
            next: (res: any) => {
                this.list = res.list;
            }
        })
    }

    init() {
        this.loadEvent({});
    }

    ngOnDestroy(): void {
        this.destory$.next();
        this.destory$.complete();
    }
    ngOnInit(): void {
        this.init();
    }
}
