<nz-card nzTitle="微博智能爬取" [nzExtra]="extraTemplate" nzHoverable>
    <nz-card-tab>
        <div class="hits">
            <div class="list-url-hit">
                共扫描到：{{listLinks.length}}条列表任务,{{articleLinks.length}}条文章任务,{{commentUrls.length}}条评论任务,{{childCommentUrls.length}}条子评论任务,{{shareUrls.length}}条分享任务，{{likeUrls.length}}条点赞任务
            </div>
        </div>
        <nz-tabset [(nzSelectedIndex)]="showIndex">
            <nz-tab nzTitle="参数设置" [nzDisabled]="starting">
                <form nz-form>
                    <nz-form-item>
                        <nz-input-group>
                            <input type="text" nz-input [(ngModel)]="keyword" (ngModelChange)="onKeywordChange($event)"
                                name="keyword" placeholder="请输入检索关键字">
                        </nz-input-group>
                    </nz-form-item>
                    <nz-form-item>
                        <nz-date-picker [(ngModel)]="minDate" (ngModelChange)="onMinChange($event)"
                            name="minDate"></nz-date-picker>
                    </nz-form-item>
                    <nz-form-item>
                        <nz-date-picker [(ngModel)]="maxDate" (ngModelChange)="onMaxChange($event)"
                            name="maxDate"></nz-date-picker>
                    </nz-form-item>
                </form>
            </nz-tab>
            <nz-tab [nzTitle]="listTitle">
                <ul class="list-url">
                    <li *ngFor="let link of listLinks">
                        <a target="_blank" [href]="link.url" [class.activate]="link.status === 1">{{link.url}}</a>
                    </li>
                </ul>
            </nz-tab>
            <nz-tab [nzTitle]="articleTitle">
                <ul class="list-article">
                    <li *ngFor="let link of articleLinks">
                        <a target="_blank" [href]="link.url" [class.activate]="link.status === 1">{{link.url}}</a>
                    </li>
                </ul>
            </nz-tab>
            <nz-tab [nzTitle]="commentTitle">
                <ul class="list-article">
                    <li *ngFor="let link of commentUrls">
                        <a target="_blank" [href]="link.url" [class.activate]="link.status === 1">{{link.url}}</a>
                    </li>
                </ul>
            </nz-tab>
            <nz-tab [nzTitle]="childCommentTitle">
                <ul class="list-article">
                    <li *ngFor="let link of childCommentUrls">
                        <a target="_blank" [href]="link.url" [class.activate]="link.status === 1">{{link.url}}</a>
                    </li>
                </ul>
            </nz-tab>
            <nz-tab [nzTitle]="goodTitle">
                <ul class="list-article">
                    <li *ngFor="let link of likeUrls">
                        <a target="_blank" [href]="link.url" [class.activate]="link.status === 1">{{link.url}}</a>
                    </li>
                </ul>
            </nz-tab>
            <nz-tab [nzTitle]="shareTitle">
                <ul class="list-article">
                    <li *ngFor="let link of shareUrls">
                        <a target="_blank" [href]="link.url" [class.activate]="link.status === 1">{{link.url}}</a>
                    </li>
                </ul>
            </nz-tab>
            <nz-tab [nzTitle]="nlpTitle">
                <ul class="list-article">
                    <li *ngFor="let link of nlpUrls">
                        <a target="_blank" [href]="link.url" [class.activate]="link.status === 1">{{link.url}}</a>
                    </li>
                </ul>
            </nz-tab>
            <nz-tab nzTitle="最终结果">
                <ul class="list-article">
                    <li *ngFor="let link of articles">
                        <a target="_blank" [href]="link.from" [class.activate]="link.status === 1"
                            [innerHTML]="link.data.text"></a>
                        <span>评论：{{link.comment_count}}</span>
                        <span>点赞：{{link.like_count}}</span>
                        <span>分享：{{link.share_count}}</span>
                    </li>
                </ul>
            </nz-tab>
        </nz-tabset>
    </nz-card-tab>
    <ng-template #listTitle>
        <span nz-icon nzType="loading" *ngIf="starting && selectedIndex===1"></span>
        列表任务
    </ng-template>
    <ng-template #articleTitle>
        <span nz-icon nzType="loading" *ngIf="starting && selectedIndex===2"></span>
        文章任务
    </ng-template>
    <ng-template #commentTitle>
        评论任务
    </ng-template>
    <ng-template #childCommentTitle>
        子评论任务
    </ng-template>
    <ng-template #goodTitle>
        点赞任务
    </ng-template>
    <ng-template #shareTitle>
        转发任务
    </ng-template>
    <ng-template #nlpTitle>
        <span nz-icon nzType="loading" *ngIf="starting && selectedIndex===6"></span>
        自然语言处理
    </ng-template>
    <ng-template #extraTemplate>
        <button nz-button nzType="default" (click)="restart()">
            <span nz-icon nzType="restart-line"></span>
            重启
        </button>
        <button nz-button nzType="default" (click)="backend()">
            <span nz-icon nzType="angle-left"></span>
            后退
        </button>
        <button nz-button nzType="default" *ngIf="starting" (click)="stop()">
            <span nz-icon nzType="stop"></span>
            停止
        </button>
        <button nz-button nzType="default" (click)="start()" *ngIf="!starting">
            <span nz-icon nzType="start"></span>
            开始
        </button>
        <button nz-button nzType="default" (click)="next()">
            <span nz-icon nzType="angle-right"></span>
            前进
        </button>
        <button nz-button nzType="default" (click)="exportToCsv()">
            <span nz-icon nzType="download"></span>
            导出结果
        </button>
        <button nz-button nzType="default" (click)="upload()">
            <span nz-icon nzType="upload"></span>
            上报数据库
        </button>
    </ng-template>
</nz-card>
