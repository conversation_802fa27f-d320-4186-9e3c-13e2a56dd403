import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { NzButtonModule } from "ng-zorro-antd/button";
import { NzCardModule } from "ng-zorro-antd/card";
import { NzDatePickerModule } from "ng-zorro-antd/date-picker";
import { NzFormModule } from "ng-zorro-antd/form";
import { NzGridModule } from "ng-zorro-antd/grid";
import { NzIconModule } from "ng-zorro-antd/icon";
import { NzInputModule } from "ng-zorro-antd/input";
import { NzTabsModule } from "ng-zorro-antd/tabs";
import { KeywordCrawlingPage } from "./keyword-crawling";
import {} from '@delon/abc'

@NgModule({
    declarations: [
        KeywordCrawlingPage
    ],
    imports: [
        FormsModule,
        NzIconModule,
        NzButtonModule,
        NzCardModule,
        NzGridModule,
        NzTabsModule,
        NzFormModule,
        NzInputModule,
        NzDatePickerModule,
        CommonModule,
    ]
})
export class KeywordCrawlingPageModule { }
