import { HttpClient } from "@angular/common/http";
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { XlsxService } from "@delon/abc/xlsx";
import { NzMessageService } from "ng-zorro-antd/message";
import { map, of, Subject, switchMap, takeUntil, concat, takeLast, tap } from "rxjs";
import { Sdk } from "src/app/services/sdk";

@Component({
    selector: `keyword-crawling`,
    templateUrl: './keyword-crawling.html',
    styleUrls: ['./keyword-crawling.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class KeywordCrawlingPage implements OnDestroy, OnInit {
    keyword: string = `茅台`;
    maxDate!: Date;
    minDate!: Date;
    step: number = 24;

    listLinks: { status: number, url: string }[] = [];
    articleLinks: { status: number, url: string }[] = [];
    commentUrls: { status: number, url: string, uid: string, aid: string }[] = [];
    childCommentUrls: { status: number, url: string, uid: string, aid: string, pid: string }[] = [];
    likeUrls: { status: number, url: string, aid: string }[] = [];
    shareUrls: { status: number, url: string, aid: string }[] = [];
    nlpUrls: { status: number, url: string, text: string[] }[] = [];

    articles: any[] = [];

    showIndex: number = 0;
    selectedIndex: number = 0;

    destory: Subject<void> = new Subject();
    event: Subject<string> = new Subject();
    stop$: Subject<void> = new Subject();
    start$: Subject<void> = new Subject();

    constructor(private http: HttpClient, private sdk: Sdk, private cd: ChangeDetectorRef, private msg: NzMessageService, private xls: XlsxService) {
        this.maxDate = new Date()
        this.minDate = new Date(new Date().getTime() - 24 * 0.05 * 60 * 60 * 1000)
    }
    ngOnInit(): void {
        this.selectedIndex = parseInt(this.get(`selectedIndex`) || '0')
        this.listLinks = this.get('listLinks') || []
        this.articleLinks = this.get('articleLinks') || []
        this.articles = this.get('articles') || []
        this.commentUrls = this.get('commentUrls') || []
        this.shareUrls = this.get('shareUrls') || []
        this.likeUrls = this.get('likeUrls') || []
        this.nlpUrls = this.get('nlpUrls') || []
        this.childCommentUrls = this.get('childCommentUrls') || [];
        this.showIndex = this.selectedIndex;
    }
    ngOnDestroy(): void {
        this.destory.next();
        this.destory.complete();
    }
    starting: boolean = false;
    /**
     * 开始
     */
    start() {
        this.starting = true;
        this.event.pipe(
            takeUntil(this.stop$),
            takeUntil(this.destory),
            switchMap((event: string) => {
                console.log(`event`, event)
                switch (event) {
                    case 'crawlingListUrls':
                        return this.crawlingListUrls()
                    case 'crawlingArticleUrls':
                        return this.crawlingArticleUrls();
                    case 'crawlingDetailUrls':
                        return this.crawlingDetailUrls();
                    case 'crawlingCommentUrls':
                        return this.crawlingCommentUrls();
                    case 'crawlingChildCommentUrls':
                        return this.crawlingChildCommentUrls();
                    case 'crawlingLikeUrls':
                        return this.crawlingLikeUrls();
                    case 'crawlingShareUrls':
                        return this.crawlingShareUrls();
                    case 'crawlingNlpUrls':
                        return this.crawlingNlpUrls();
                }
                return of();
            })
        ).subscribe()
        switch (this.selectedIndex) {
            case 0:
                return this.step1();
            case 1:
                return this.crawlingArticleUrls().subscribe();
            case 2:
                return this.crawlingDetailUrls().subscribe();
            case 3:
                return this.crawlingCommentUrls().subscribe();
            case 4:
                return this.crawlingChildCommentUrls().subscribe();
            case 5:
                return this.crawlingLikeUrls().subscribe();
            case 6:
                return this.crawlingShareUrls().subscribe();
            case 7:
                return this.crawlingNlpUrls().subscribe();
        }
    }
    crawlingChildCommentUrls() {
        if (this.childCommentUrls.length > 0) {
            return concat(...this.childCommentUrls.map(url => this.crawlingChildCommentUrl(url.url, url.aid, url.pid))).pipe(
                takeUntil(this.destory),
                takeUntil(this.stop$),
                takeLast(1),
                tap(() => {
                    this.selectedIndex += 1;
                    this.showIndex = this.selectedIndex;
                    this.save(`selectedIndex`, this.selectedIndex)
                    this.event.next(`crawlingLikeUrls`)
                    this.cd.detectChanges();
                })
            )
        }
        return of();
    }
    crawlingChildCommentUrl(url: string, aid: string, pid: string) {
        return this.sdk.get(`@nger/weibo/crawling/weiboChildComment?url=${encodeURIComponent(url)}&keyword=${this.keyword}`).pipe(takeUntil(this.destory), map((res: any) => {
            const articleIndex = this.articles.findIndex(article => article.aid === aid)
            if (articleIndex > -1) {
                const comments = Reflect.get(this.articles[articleIndex], 'comments')
                const commentIndex = comments.findIndex((comment: any) => `${comment.id}` === `${pid}`)
                if (commentIndex > -1) {
                    Reflect.set(comments[commentIndex], `children`, res.data)
                    console.log(this.articles)
                    this.save('', this.articles)
                }
            }
            console.log(`crawlingChildCommentUrl`, { res })
        }))
    }
    crawlingCommentUrls() {
        return concat(...this.commentUrls.map(url => this.crawlingCommentUrl(url.url, url.uid, url.aid))).pipe(
            takeUntil(this.destory),
            takeUntil(this.stop$),
            takeLast(1),
            tap(() => {
                this.selectedIndex += 1;
                this.showIndex = this.selectedIndex;
                this.save(`selectedIndex`, this.selectedIndex)
                this.event.next(`crawlingChildCommentUrls`)
                this.cd.detectChanges();
            })
        )
    }
    crawlingCommentUrl(url: string, uid: string, aid: string) {
        return this.sdk.get(`@nger/weibo/crawling/weiboComment?url=${encodeURIComponent(url)}&keyword=${this.keyword}`).pipe(takeUntil(this.destory), map((res: any) => {
            if (res.data) {
                const articleIndex = this.articles.findIndex(article => article.aid === aid)
                if (articleIndex > -1) {
                    Reflect.set(this.articles[articleIndex], 'comments', res.data)
                    Reflect.set(this.articles[articleIndex], 'comment_count', res.total_number)
                    this.save('articles', this.articles)
                }
                res.data.map((item: any) => {
                    this.childCommentUrls.push({
                        url: this.createChildCommentUrl(item, uid, 0),
                        uid: uid,
                        aid: aid,
                        pid: item.id,
                        status: 0
                    });
                });
                this.save('childCommentUrls', this.childCommentUrls)
                this.cd.detectChanges();
            }
            return { url, comments: res.data }
        }))
    }
    createChildCommentUrl(item: any, uid: string, max_id: number = 0) {
        const url = `https://weibo.com/ajax/statuses/buildComments`
        const query: any = {}
        query.is_reload = 1;
        query.id = item.id
        query.is_show_bulletin = 2;
        query.is_mix = 1;
        query.fetch_level = 1;
        query.count = 100000;
        query.uid = uid;
        if (max_id) {
            query.flow = 0;
            query.max_id = max_id;
        } else {
            query.max_id = 0
        }
        const _url = this.createUrl(url, query)
        return _url;
    }
    crawlingLikeUrls() {
        return concat(...this.likeUrls.map(url => this.crawlingLikeUrl(url.url, url.aid))).pipe(
            takeUntil(this.destory),
            takeUntil(this.stop$),
            takeLast(1),
            tap(() => {
                this.selectedIndex += 1;
                this.showIndex = this.selectedIndex;
                this.save(`selectedIndex`, this.selectedIndex)
                this.event.next(`crawlingShareUrls`)
                this.cd.detectChanges();
            })
        )
    }
    crawlingLikeUrl(url: string, aid: string) {
        return this.sdk.get(`@nger/weibo/crawling/weiboLike?url=${encodeURIComponent(url)}&keyword=${this.keyword}`).pipe(takeUntil(this.destory), map((res: any) => {
            if (res.data) {
                const articleIndex = this.articles.findIndex(article => article.aid === aid)
                if (articleIndex > -1) {
                    Reflect.set(this.articles[articleIndex], 'likes', res.data)
                    Reflect.set(this.articles[articleIndex], 'like_count', res.total_number)
                    this.save('articles', this.articles)
                }
            }
        }))
    }
    crawlingShareUrls() {
        return concat(...this.shareUrls.map(url => this.crawlingShareUrl(url.url, url.aid))).pipe(
            takeUntil(this.destory),
            takeUntil(this.stop$),
            takeLast(1),
            tap(() => {
                this.selectedIndex += 1;
                this.showIndex = this.selectedIndex;
                this.save(`selectedIndex`, this.selectedIndex)
                this.event.next(`crawlingNlpUrls`)
                this.cd.detectChanges();
            })
        )
    }
    crawlingShareUrl(url: string, aid: string) {
        return this.sdk.get(`@nger/weibo/crawling/weiboShare?url=${encodeURIComponent(url)}&keyword=${this.keyword}`).pipe(takeUntil(this.destory), map((res: any) => {
            if (res.data) {
                const articleIndex = this.articles.findIndex(article => article.aid === aid)
                if (articleIndex > -1) {
                    Reflect.set(this.articles[articleIndex], 'shares', res.data)
                    Reflect.set(this.articles[articleIndex], 'share_count', res.total_number)

                    this.save('articles', this.articles)
                }
            }
        }))
    }
    crawlingNlpUrls() {
        return concat(...this.nlpUrls.map(url => this.crawlingNlpUrl(url.url, url.text))).pipe(
            takeUntil(this.destory),
            takeUntil(this.stop$)
        )
    }
    crawlingNlpUrl(url: string, text: string[]) {
        return this.sdk.post(`@nger/weibo/crawling/weiboNlp?url=${encodeURIComponent(url)}&keyword=${this.keyword}`, { text }).pipe(takeUntil(this.destory))
    }
    test() {
    }
    step1() {
        const step = this.step;
        let startDate = new Date(this.minDate);
        const maxEndDate = new Date(this.maxDate);
        let urls: string[] = [];
        let endDate = new Date(startDate.getTime() + step * 60 * 60 * 1000);
        const keyword = encodeURIComponent(this.keyword)
        while (endDate < maxEndDate) {
            const url = `https://s.weibo.com/weibo?q=${keyword}&typeall=1&suball=1&timescope=custom:${this.formatDate(startDate)}:${this.formatDate(endDate)}&Refer=g`;
            urls.push(url)
            startDate = endDate;
            endDate = new Date(startDate.getTime() + step * 60 * 60 * 1000);
        }
        const url = `https://s.weibo.com/weibo?q=${keyword}&typeall=1&suball=1&timescope=custom:${this.formatDate(startDate)}:${this.formatDate(maxEndDate)}&Refer=g`;
        urls.push(url)
        this.listLinks = urls.map(url => ({
            status: 0,
            url
        }));
        this.selectedIndex = 1;
        this.showIndex = this.selectedIndex;
        this.save(`selectedIndex`, this.selectedIndex)
        this.event.next('crawlingListUrls');
    }
    // 停止
    stop() {
        console.log(this.selectedIndex)
        this.selectedIndex -= 1;
        this.showIndex = this.selectedIndex;
        this.starting = false;
        this.stop$.next();
        this.cd.detectChanges();
    }
    // restart
    restart() {
        this.listLinks = [];
        this.articleLinks = [];
        this.articles = [];
        this.childCommentUrls = [];
        this.commentUrls = [];
        this.shareUrls = [];
        this.likeUrls = [];
        this.nlpUrls = [];
        this.stop();
        this.selectedIndex = 0;
        this.showIndex = 0;
        this.start();
        this.cd.detectChanges();
    }
    backend() {
        this.selectedIndex -= 1;
        this.showIndex = this.selectedIndex;
        this.stop();
        this.cd.detectChanges();
    }
    next() {
        this.selectedIndex += 1;
        this.showIndex = this.selectedIndex;
        this.stop();
        this.cd.detectChanges();
    }
    private getNlpUrl() {
        return this.sdk.createUrl(`@nger/weibo/crawling/nlp`)
    }
    private getShareUrl(id: number, page: number, psize: number) {
        return `https://weibo.com/ajax/statuses/repostTimeline?id=${id}&page=${page}&moduleID=feed&count=${psize}`
    }
    private getLikeUrl(id: number, page: number, psize: number) {
        return `https://weibo.com/ajax/statuses/likeShow?id=${id}&attitude_type=0&attitude_enable=1&page=${page}&count=${psize}`
    }
    private getComponentUrl(id: number, uid: number, max_id: number, page: number, psize: number) {
        const url = `https://weibo.com/ajax/statuses/buildComments`
        const query: any = {};
        query.is_reload = 1;
        query.id = id;
        query.count = psize;
        query.uid = uid;
        query.is_show_bulletin = 2;
        if (max_id) {
            query.flow = 0;
            query.is_mix = 0;
            query.max_id = max_id
        } else {
            query.is_mix = 0;
        }
        const _url = this.createUrl(url, query)
        return _url;
    }
    private createUrl(url: string, query: any) {
        const str = Object.keys(query).map(key => {
            return `${key}=${query[key]}`
        }).join('&')
        return `${url}?${str}`
    }
    // https://weibo.com/ajax/statuses/show?id=${mid}
    private toAjaxDetailUrl(url: string) {
        const _url = new URL(url)
        const [, uid, mid] = _url.pathname.split('/');
        return `https://weibo.com/ajax/statuses/show?id=${mid}`
    }

    private crawlingListUrls() {
        return concat(...this.listLinks.map(link => this.crawlingListUrl(link.url))).pipe(
            takeUntil(this.destory),
            takeUntil(this.stop$),
            map((list: any) => {
                if (Array.isArray(list)) {
                    this.listLinks.push(...list.map((url: any) => ({
                        status: 0,
                        url
                    })));
                    this.save('listLinks', this.listLinks);
                    this.cd.detectChanges();
                } else {
                    this.msg.error(list.message)
                }
            }),
            takeLast(1),
            tap(() => {
                this.selectedIndex += 1;
                this.showIndex = this.selectedIndex;
                this.save(`selectedIndex`, this.selectedIndex)
                this.event.next(`crawlingArticleUrls`)
                this.cd.detectChanges();
            })
        )
    }
    onMinChange(end: Date) { }
    onMaxChange(end: Date) { }
    onKeywordChange(keyword: string) { }
    private crawlingListUrl(url: string) {
        return this.sdk.get(`@nger/weibo/crawling/weiboListUrl?url=${encodeURIComponent(url)}&keyword=${this.keyword}`).pipe()
    }
    private crawlingArticleUrls() {
        return concat(...[...this.listLinks].map(url => {
            return this.crawlingArticleUrl(url.url).pipe(
                tap(() => {
                    const listIndex = this.listLinks.findIndex(it => it.url === url.url)
                    this.listLinks[listIndex].status = 1;
                })
            )
        })).pipe(
            takeUntil(this.stop$),
            takeUntil(this.destory),
            map((list: any) => {
                if (Array.isArray(list)) {
                    this.articleLinks.push(...list.map((url: any) => ({
                        status: 0,
                        url
                    })));
                    this.save('articleLinks', this.articleLinks);
                    this.cd.detectChanges();
                } else {
                    this.msg.error(list.message)
                }
            }),
            takeLast(1),
            tap(() => {
                this.selectedIndex += 1;
                this.showIndex = this.selectedIndex;
                this.save(`selectedIndex`, this.selectedIndex)
                this.event.next(`crawlingDetailUrls`)
                this.cd.detectChanges();
            })
        );
    }
    private save(key: string, value: any) {
        if (typeof value === 'object') {
            localStorage.setItem(this.getKey(key), JSON.stringify(value))
        } else {
            localStorage.setItem(this.getKey(key), value)
        }
    }
    private get(key: string) {
        const value = localStorage.getItem(this.getKey(key));
        if (value) {
            try {
                return JSON.parse(value)
            } catch (e) {
                return value;
            }
        }
    }
    private getKey(key: string) {
        return `${this.keyword}:${key}`
    }
    private crawlingArticleUrl(url: string) {
        return this.sdk.get(`@nger/weibo/crawling/weiboDetailUrl?url=${encodeURIComponent(url)}&keyword=${this.keyword}`).pipe(takeUntil(this.stop$),
            takeUntil(this.destory),)
    }
    private crawlingDetailUrls() {
        return concat(...this.articleLinks.map(url => this.crawlingDetailUrl(url.url))).pipe(
            takeUntil(this.stop$),
            takeUntil(this.destory),
            takeLast(1),
            tap(() => {
                console.log(`crawlingDetailUrls finish`)
                this.selectedIndex += 1;
                this.showIndex = this.selectedIndex;
                this.save(`selectedIndex`, this.selectedIndex)
                this.event.next(`crawlingCommentUrls`)
                this.cd.detectChanges();
            }));
    }
    private crawlingDetailUrl(url: string) {
        const ajaxUrl = this.toAjaxDetailUrl(url);
        return this.sdk.get(`@nger/weibo/crawling/weiboDetail?url=${ajaxUrl}`).pipe(
            takeUntil(this.stop$),
            takeUntil(this.destory),
            switchMap((res: any) => {
                if (res.success) {
                    const data = res.data;
                    // get comments
                    const { user: account, ok, error_code, message, ...content } = data;
                    if (ok == 1) {
                        const news: any = {}
                        news.platform = `weibo`;
                        news.from = url;
                        content.id = `${content.id}`
                        news.data = content;
                        news.user = account;
                        news.aid = content.id
                        news.uid = account.id;
                        news.post_at = new Date(content.created_at);

                        news.readablity = 0;
                        news.word_num = 0;
                        news.cut = {};
                        news.keywords = [];
                        news.negative_probs = 0;
                        news.positive_probs = 1;
                        news.sentiment_key = 'positive';
                        news.sentiment_label = 1;
                        // 分享情感
                        news.shares = [];
                        news.share_count = 0;

                        news.share_cut = [];
                        news.share_emotion = [];
                        news.share_readablity_avg = 0;
                        news.share_word_num_avg = 0;

                        news.share_positive_avg = 0;
                        news.share_negative_avg = 0;
                        news.share_negative_count = 0;
                        news.share_positive_count = 0;


                        news.comments = [];
                        news.comment_count = 0;

                        news.comment_cut = [];
                        news.comment_emotion = [];
                        news.comment_readablity_avg = 0
                        news.comment_positive_avg = 0
                        news.comment_negative_avg = 0
                        news.comment_negative_count = 0;
                        news.comment_positive_count = 0;
                        news.comment_word_num_avg = 0;

                        news.likes = [];
                        news.like_count = 0;

                        if (this.keyword) {
                            news.keywords.push(this.keyword);
                        }
                        const psize = 200;
                        const page = 1;
                        this.commentUrls.push({
                            status: 0,
                            url: this.getComponentUrl(news.aid, news.uid, 0, page, 100000),
                            uid: news.uid,
                            aid: news.aid
                        });
                        this.likeUrls.push({
                            status: 0,
                            url: this.getLikeUrl(news.aid, page, 100000),
                            aid: news.aid
                        });
                        this.shareUrls.push({
                            status: 0,
                            url: this.getShareUrl(news.aid, page, 100000),
                            aid: news.aid
                        });
                        this.nlpUrls.push({
                            status: 0,
                            url: this.getNlpUrl(),
                            text: [content.text]
                        })
                        this.articles.push(news)
                        this.save('commentUrls', this.commentUrls)
                        this.save('likeUrls', this.likeUrls)
                        this.save('shareUrls', this.shareUrls)
                        this.save('articles', this.articles)
                        this.save('nlpUrls', this.nlpUrls)
                        this.selectedIndex = 3;
                        this.showIndex = this.selectedIndex;
                        // get url
                        const articleIndex = this.articleLinks.findIndex(it => it.url === url)
                        this.articleLinks[articleIndex].status = 1;
                        this.save('articleLinks', this.articleLinks)
                        this.cd.detectChanges();
                    }
                }
                return of({});
            })
        );
    }
    private formatDate(startDate: Date) {
        return this.format(startDate, 'yyyy-MM-dd-hh')
    }
    private format(date: Date, fmt: string) {  // author: meizz 
        const o: any = {
            "M+": date.getMonth() + 1,  // 月份 
            "d+": date.getDate(),  // 日 
            "h+": date.getHours(),  // 小时 
            "m+": date.getMinutes(),  // 分 
            "s+": date.getSeconds(),  // 秒 
            "q+": Math.floor((date.getMonth() + 3) / 3),  // 季度 
            "S": date.getMilliseconds()  // 毫秒 
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (let k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }

    exportToCsv() {
        this.xls.export({
            sheets: [{
                name: 'A1',
                data: this.articles.map(it => {
                    return [
                        it.from, it.user.screen_name, it.data.created_at, it.data.source, it.data.region_name, it.data.text
                    ]
                })
            }],
            format: 'xlsx',
            filename: 'htxc-10.xlsx'
        })
    }

    upload() { }
}
