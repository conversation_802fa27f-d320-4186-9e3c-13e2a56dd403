import { Component, Injector, On<PERSON><PERSON>roy, OnInit, Output , EventEmitter} from '@angular/core'
import { Subject } from 'rxjs'
import { Sdk } from '../..//services/sdk'


@Component({
    selector: 'keyword-page',
    styleUrls: ['./keyword-page.scss'],
    templateUrl: './keyword-page.html'
})
export class KeywordPage implements OnInit, OnDestroy {

    tags: any[] = []

    @Output() onSelect: EventEmitter<any> = new EventEmitter()

    get sdk() {
        return this.injector.get(Sdk)
    }

    constructor(private injector: Injector) { }

    destory$: Subject<void> = new Subject();
    ngOnDestroy(): void {
        this.destory$.next()
        this.destory$.complete()
    }

    ngOnInit(): void {
        this.sdk.post('@nger/rest/spider_keyword/all', {}).subscribe((res: any) => {
            this.tags = res.list;
        })
    }

    sliceTagName(tag: any) {
        return tag.value
    }

    handleClose(tag: any) {
        this.sdk.post('@nger/rest/spider_keyword/delete', { id: tag.id }).subscribe(res => {
            console.log(res)
        })
    }
    selectTag(tag: any){
        this.onSelect.emit(tag)
    }

}
