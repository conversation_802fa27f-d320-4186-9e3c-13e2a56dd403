import { NgModule } from "@angular/core";
import { NzButtonModule } from "ng-zorro-antd/button";
import { NzMessageModule } from "ng-zorro-antd/message";
import { NzModalModule } from "ng-zorro-antd/modal";
import { NzAvatarModule } from "ng-zorro-antd/avatar";

import { WeiboLoginComponent } from "./weibo/weibo";
import { CommonModule } from "@angular/common";
import { ZhihuLoginComponent } from "./zhihu/zhihu";
import { ToutiaoLoginComponent } from "./toutiao/toutiao";
import { DouyinLoginComponent } from "./douyin/douyin";
import { BaiduLoginComponent } from "./baidu/baidu";
import { QqLoginComponent } from "./qq/qq";
import { WechatLoginComponent } from "./wechat/wechat";

@NgModule({
    imports: [
        NzModalModule,
        NzMessageModule,
        NzButtonModule,
        NzAvatarModule,
        CommonModule
    ],
    declarations: [
        WeiboLoginComponent,
        ZhihuLoginComponent,
        ToutiaoLoginComponent,
        DouyinLoginComponent,
        BaiduLoginComponent,
        QqLoginComponent,
        WechatLoginComponent
    ],
    exports: [
        WeiboLoginComponent,
        ZhihuLoginComponent,
        ToutiaoLoginComponent,
        DouyinLoginComponent,
        BaiduLoginComponent,
        QqLoginComponent,
        WechatLoginComponent
    ]
})
export class LoginsModule{}