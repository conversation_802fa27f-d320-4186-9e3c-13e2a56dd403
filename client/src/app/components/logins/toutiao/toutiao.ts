import { Compo<PERSON>, On<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Injector, ChangeDetectorRef, ChangeDetectionStrategy } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd/message";
import { Subject, Subscription, takeUntil } from "rxjs";
import { Sdk } from "../../../services/sdk";


@Component({
    selector: 'toutiao-login',
    templateUrl: './toutiao.html',
    styleUrls: ['./toutiao.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ToutiaoLoginComponent implements OnInit, OnDestroy {
    get msg() {
        return this.injector.get(NzMessageService)
    }
    get sdk() {
        return this.injector.get(Sdk)
    }
    get cdr() {
        return this.injector.get(ChangeDetectorRef)
    }
    constructor(private injector: Injector) { }
    destory$: Subject<void> = new Subject();
    ngOnDestroy(): void {
        this.destory$.next();
        this.destory$.complete();
    }
    ngOnInit(): void { }
    showModal: boolean = false
    weiboQrCode!: string;
    hasScanQrcode: boolean = false;
    hasLogin: boolean = false;
    loading: boolean = false;
    ws$!: Subscription
    uid?: string;
    nickname?: string;
    cookies?: any;
    avatar?: string;
    showQrCode: boolean = false;

    getWeiboLoginQrcode() {
        if (this.loading) return;
        this.loading = true;
        this.showQrCode = true;
        this.uid = undefined
        this.nickname = undefined
        this.avatar = undefined
        this.cookies = undefined
        this.sdk.ws('@nger/weibo/bind-user/login_qrcode?platform=toutiao').pipe(
            takeUntil(this.destory$)
        ).subscribe((res: any) => {
            const { action, data } = res;
            switch (action) {
                case 'open':
                    this.msg.success('成功打开https://toutiao.com')
                    break;
                case 'get_login_btn':
                    this.msg.success('成功获取登录按钮')
                    break;
                case 'click_login_btn':
                    this.msg.success('模拟点击登录按钮成功')
                    break;
                case 'get_login_qrcode':
                    this.msg.success('成功获取登录二维码')
                    this.showModal = true;
                    this.weiboQrCode = data;
                    break;
                case 'login_success':
                    this.msg.success('恭喜您，登录成功')
                    break;
                case 'get_login_fail':
                    this.msg.error('获取登录二维码失败,请重试');
                    this.cancel()
                    break;
                case 'get_scan_qrcode':
                    this.msg.success('扫描成功')
                    this.hasScanQrcode = true;
                    break;
                case 'get_userinfo':
                    const { uid, name, avatar, cookies } = data;
                    this.uid = uid
                    this.nickname = name;
                    this.avatar = avatar;
                    this.cookies = cookies;
                    this.hasLogin = true;
                    this.showQrCode = false;
                    this.msg.success('恭喜您成功获取用户信息');
                    break;
                default:
                    this.msg.error('未知错误,请重试')
                    break;
            }
            this.cdr.detectChanges()
        })
    }

    ok() {
        if (!this.hasScanQrcode) {
            this.msg.error('请用微博app扫码登录')
            return;
        }
        if (!this.hasLogin) {
            this.msg.error('请在微博app中确认登录')
            return;
        }
        this.showModal = false;
        this.loading = false;
        this.cdr.detectChanges()
        this.ws$ && this.ws$.unsubscribe()
        // save user
        const user = {
            uid: this.uid,
            username: this.nickname,
            cookies: this.cookies,
            avatar: this.avatar,
            platform: 'toutiao',
            status: 2
        }
        this.sdk.post('@nger/weibo/bind-user/update_or_add_user', user).pipe(
            takeUntil(this.destory$)
        ).subscribe(res => {
            console.log(res)
        })
    }

    cancel() {
        this.showModal = false;
        this.loading = false;
        if (this.ws$) {
            this.ws$.unsubscribe()
        }
        this.cdr.detectChanges()
    }
}
