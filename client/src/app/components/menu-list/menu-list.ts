import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { Subject, takeUntil } from "rxjs";
import { Sdk } from "../../services/sdk";
import { IMenu, MenuSdk } from "../../services/menu";
import { Router } from "@angular/router";

@Component({
    selector: 'menu-list',
    templateUrl: './menu-list.html',
    styleUrls: ['./menu-list.scss']
})
export class MenuListComponent implements OnInit, OnDestroy {
    menuList: IMenu[] = [];
    private destory$: Subject<void> = new Subject();
    systemMenus: IMenu[] = [];
    constructor(private injector: Injector) { }
    get menuSdk() {
        return this.injector.get(MenuSdk)
    }
    get sdk() {
        return this.injector.get(Sdk)
    }
    get router() {
        return this.injector.get(Router)
    }
    select(menu: any) {
        this.sdk.goto(menu.link)
    }
    ngOnDestroy(): void {
        this.destory$.next();
        this.destory$.complete();
    }
    ngOnInit(): void {
        const user = this.sdk.getUser()
        if (user && (user.uid === 1 || user.uid === 2)) {
            this.systemMenus.push({
                title: '用户管理',
                icon: 'icon-denglu',
                link: '@nger/rest/?name=sys_user'
            }, {
                title: '导出日志',
                icon: 'icon-Information-crawling',
                link: '@nger/rest/?name=spilder_export_log'
            }, {
                title: '系统更新',
                icon: 'icon-gengxin',
                link: '@nger/install/admin'
            })
        }
        this.menuSdk.getMenuList().pipe(
            takeUntil(this.destory$)
        ).subscribe({
            next: (menuList) => this.menuList = menuList
        })
    }
    goto(path: string) {
        this.router.navigateByUrl(path)
    }
}
