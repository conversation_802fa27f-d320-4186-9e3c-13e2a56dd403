import { HttpClient } from "@angular/common/http";
import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { map, Observable, of, Subject, switchMap, takeUntil } from "rxjs";
import { Sdk } from "../../services/sdk";
import { IPlatform, PlatformSdk } from "../../services/platform";
import { NzMessageService } from 'ng-zorro-antd/message'
@Component({
    selector: 'platform-list',
    styleUrls: ['./platform-list.scss'],
    templateUrl: './platform-list.html'
})
export class PlatformListComponent implements OnInit, OnDestroy {
    private destory$: Subject<void> = new Subject();
    get platform() {
        return this.injector.get(PlatformSdk);
    }
    get msg() {
        return this.injector.get(NzMessageService);
    }
    platformList: IPlatform[] = [];
    constructor(private injector: Injector) { }
    /**
     * 初始化
     */
    init() {
        this.platform.getPlatformList().pipe(
            takeUntil(this.destory$),
        ).subscribe({
            next: (platformList) => {
                this.platformList = platformList
            }
        })
    }
    showLoginModal: boolean = false;
    tipText: string = ``;
    src: string = ``;
    loginSuccess: boolean = false;
    isScanQrcode: boolean = false;
    closeLoginModal() {
        this.showLoginModal = false;
    }
    type!: string;
    select(p: IPlatform) {
        this.type = p.name;
        this.loginWb(this.type).subscribe()
    }
    get http() {
        return this.injector.get(HttpClient)
    }
    get sdk() {
        return this.injector.get(Sdk)
    }
    checkLoginSuccess(type: string): Observable<boolean> {
        return this.sdk.get(`@nger/weibo/loginSuccess?type=${type}`).pipe(
            takeUntil(this.destory$),
            switchMap((r: any) => {
                if (r.code === 200) {
                    this.loginSuccess = true;
                    if (r.msg) {
                        this.msg.success(r.msg)
                    }
                    return of(this.loginSuccess);
                } else {
                    return this.checkLoginSuccess(type);
                }
            })
        )
    }
    hasScanQrcode(type: string): Observable<boolean> {
        return this.sdk.get(`@nger/weibo/hasScanQrcode?type=${type}`).pipe(
            switchMap((r: any) => {
                // 获取失败
                if (r.code !== 200) {
                    if (r.msg) {
                        this.msg.error(r.msg)
                    }
                    return this.hasScanQrcode(type)
                } else {
                    this.isScanQrcode = true;
                    this.tipText = r.data;
                    if (r.msg) {
                        this.msg.success(r.msg)
                    }
                    return this.checkLoginSuccess(type)
                }
            })
        )
    }
    loginWb(type: string): Observable<boolean> {
        return this.sdk.get(`@nger/weibo/login?type=${type}`).pipe(
            takeUntil(this.destory$),
            switchMap((r: any) => {
                if (r.code === 200) {
                    const data = r.data;
                    if (data) {
                        this.src = data.src;
                        this.loginSuccess = !!data.status;
                    } else {
                        this.loginSuccess = true;
                    }
                    if (r.msg) {
                        this.msg.success(r.msg)
                    } else {
                        this.msg.success(`恭喜您，操作成功`)
                    }
                    if (this.loginSuccess) {
                        this.showLoginModal = false;
                        return of(this.loginSuccess);
                    } else {
                        this.showLoginModal = true;
                        return this.hasScanQrcode(type).pipe(
                            map(r => {
                                if (r) {
                                    this.tipText = '恭喜您，登录成功!';
                                    this.showLoginModal = false;
                                } else {
                                    this.tipText = '请稍等，正在登录。。。'
                                }
                                return r;
                            })
                        );
                    }
                }
                if (r.code === -200) {
                    this.tipText = r.msg;
                    this.msg.error(r.msg);
                    return of(false);
                }
                return this.loginWb(type);
            })
        )
    }
    ngOnDestroy(): void {
        this.destory$.next();
        this.destory$.complete();
    }
    ngOnInit(): void {
        this.init();
    }
}
