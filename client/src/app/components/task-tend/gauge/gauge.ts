import { Component, ElementRef, Injector, Input, OnDestroy, OnInit, OnChanges, SimpleChanges } from "@angular/core";
import * as echarts from 'echarts';
type EChartsOption = echarts.EChartsOption
@Component({
    selector: `gauge`,
    templateUrl: './gauge.html',
    styleUrls: ['./gauge.scss']
})
export class Gauge implements OnInit, OnDestroy, OnChanges {
    @Input()
    value!: number;
    @Input()
    name!: string;
    get elementRef() {
        return this.injector.get(ElementRef)
    }
    constructor(private injector: Injector) { }
    ngOnDestroy(): void {
        this.myChart.clear();
    }
    ngOnChanges(changes: SimpleChanges): void {
        this.refresh();
    }
    myChart!: echarts.EChartsType;
    ngOnInit(): void {
        const chartDom = this.elementRef.nativeElement;
        this.myChart = echarts.init(chartDom);
        this.refresh();
    }

    refresh() {
        const option: EChartsOption = {
            tooltip: {
                formatter: '{a} <br/>{b} : {c}%'
            },
            series: [
                {
                    name: this.name,
                    type: 'gauge',
                    detail: {
                        formatter: '{value}'
                    },
                    data: [
                        {
                            value: this.value,
                            name: this.name
                        }
                    ]
                }
            ]
        }
        this.myChart && this.myChart.setOption(option);
    }
}
