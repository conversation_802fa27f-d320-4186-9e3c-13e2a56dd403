import { Component, Injector, Input, OnDestroy, OnInit, OnChanges, SimpleChanges } from "@angular/core";
@Component({
  selector: `task-tend`,
  templateUrl: './task-tend.html',
  styleUrls: ['./task-tend.scss']
})
export class TaskTend implements OnInit, OnDestroy, OnChanges {
  @Input()
  info: any;
  constructor(private injector: Injector) { }
  ngOnChanges(changes: SimpleChanges): void { }
  ngOnDestroy(): void { }
  ngOnInit(): void {}
}
