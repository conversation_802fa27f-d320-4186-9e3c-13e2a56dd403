import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnD<PERSON>roy } from "@angular/core";
import { Subject, takeUntil } from "rxjs";
import { Sdk } from "../../services/sdk";

@Component({
    selector: `admin-layout`,
    templateUrl: './admin.component.html',
    styleUrls: ['./admin.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdminLayoutComponent implements OnDestroy{
    count: number = 0;
    list: any[] = [];
    destory: Subject<void> = new Subject();
    constructor(private sdk: Sdk, private cd: ChangeDetectorRef) { }
    ngOnDestroy(): void {
        this.destory.next();
        this.destory.complete();
    }
    ngOnInit() {
        this.sdk.ws('message/mine?uid=1').subscribe({
            next: (msg: any) => {
                this.count = msg.total;
                this.list = msg.list;
                this.cd.detectChanges();
            }
        })
    }

    openMessage(item: any) {
        this.sdk.post('message/read', { sn: item.sn }).pipe(
            takeUntil(this.destory)
        ).subscribe();
    }
}