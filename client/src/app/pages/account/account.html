<form nz-form class="box-inline" [formGroup]="group" (ngSubmit)="search()">
    <div class="title">按指定账户爬取数据</div>
    <nz-form-item>
        <nz-input-group nzSearch nzSize="large" [nzAddOnAfter]="suffixButton" [nzAddOnBefore]="addOnBefore">
            <input type="text" nz-input (input)="onInput($event)" formControlName="keyword" placeholder="请输入帐号名称"
                [nzAutocomplete]="auto" />
            <nz-autocomplete [nzBackfill]="true" #auto>
                <nz-auto-option [nzValue]="item.link" [nzLabel]="item.name" *ngFor="let item of dataSource">
                    <div class="card">
                        <div class="avatar">
                            <nz-avatar [nzSrc]="item.avatar"></nz-avatar>
                        </div>
                        <div class="info">
                            <a>{{item.name}}</a>
                            <p>{{item.desc}}</p>
                            <p>
                                <span>{{item.fans}}</span>
                            </p>
                        </div>
                    </div>
                </nz-auto-option>
            </nz-autocomplete>
        </nz-input-group>
        <ng-template #suffixButton>
            <button nz-button nzType="primary" nzSize="large" nzSearch (click)="search()">
                <i nz-icon [nzIconfont]="'icon-minganguanjianzi'"></i>
                搜索
            </button>
        </ng-template>
        <ng-template #addOnBefore>
            <nz-select formControlName="platform" nzPlaceHolder="请选择平台">
                <nz-option *ngFor="let platform of platforms" [nzLabel]="platform.label" [nzValue]="platform.value">
                </nz-option>
            </nz-select>
        </ng-template>
    </nz-form-item>
    <nz-form-item>
        <nz-form-label nzFor="open_schedule">开启定时采集</nz-form-label>
        <nz-form-control>
            <nz-switch formControlName="open_schedule"></nz-switch>
        </nz-form-control>
    </nz-form-item>
    <nz-form-item *ngIf="open_schedule">
        <nz-form-label nzFor="schedule">定时采集</nz-form-label>
        <nz-form-control>
            <input nz-input type="text" formControlName="schedule" />
            <p>采集所有信息后定期更新 秒 分 时 日 月 年</p>
        </nz-form-control>
    </nz-form-item>
</form>