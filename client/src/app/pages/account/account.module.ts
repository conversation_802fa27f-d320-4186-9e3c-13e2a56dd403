import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core'
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { AccountSearchPage } from './account';


@NgModule({
    declarations: [
        AccountSearchPage
    ],
    imports: [
        RouterModule.forChild([{
            path: '',
            component: AccountSearchPage
        }]),
        NzFormModule,
        ReactiveFormsModule,
        NzInputModule,
        NzIconModule,
        NzButtonModule,
        NzSwitchModule,
        CommonModule,
        NzSelectModule,
        NzAutocompleteModule,
        NzAvatarModule
    ]
})
export class AccountModule { }
