:host {
    display: block;
    flex: 1;
    width: 100%;
    padding: 0 50px;
}

.title {
    font-size: 42px;
    margin: 135px 74px 40px 0;
    text-align: center;
}

.search_keyword {
    width: 700px;
    height: 48px;
    border: 1px solid #999;
    box-shadow: none;
    font-size: 16px;
    color: #333;
    border-radius: 8px;
    padding-left: 15px;
    padding-right: 50px;
}

.search {
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-items: center;
    align-items: center;
}

.search-input {
    display: flex;

    input {
        flex: 1;
    }

    i {
        width: 45px;
        height: 45px;
        font-size: 45px;
        margin-left: 20px;
        cursor: pointer;
    }
}

.card {
    padding: 5px 0px;
    overflow: hidden;
    color: #808080;
    display: flex;
    cursor: pointer;
    border-top: 1px solid #F9F9F9;

    .avatar {
        width: 50px;
        height: 50px;
        float: left;
        margin-right: 8px;
        position: relative;

        nz-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }
    }

    .info {
        flex: 1;

        .name {
            font-size: 12px;
            font-family: PingFangSC-Semibold;
            line-height: 18px;
            font-weight: 600;
        }

        p {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            word-wrap: normal;
            font-size: 12px;
            margin: 1px 0;
            line-height: 16px;
            color: #939393;

            span {
                padding-right: 12px;
                margin-right: 12px;
                letter-spacing: 0.4px;
                border: none;
            }
        }

    }

    .btn {
        display: flex;
        align-items: center;
    }
}