import { Component, Injector } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { NzMessageService } from "ng-zorro-antd/message";
import { catchError, debounceTime, filter, tap, switchMap, of, takeUntil, Subject } from "rxjs";
import { Sdk } from "../../services/sdk";
@Component({
    selector: 'account-page',
    templateUrl: './account.html',
    styleUrls: ['./account.scss']
})
export class AccountSearchPage {
    input$: Subject<string> = new Subject();
    dataSource: any[] = [];
    destory$: Subject<void> = new Subject();
    loading: boolean = false;
    platforms: any[] = [{
        label: '黑猫',
        value: 'heimao',
    }, {
        label: '腾讯新闻',
        value: 'qq-new',
    }, {
        label: '头条',
        value: 'toutiao',
    }, {
        label: '微信',
        value: 'wechat'
    }, {
        label: '微博',
        value: 'weibo',
    }, {
        label: '知乎',
        value: 'zhihu',
    }, {
        label: '抖音',
        value: 'douyin',
    }, {
        label: '百度',
        value: 'baidu'
    }];
    group!: FormGroup;

    constructor(private fb: FormBuilder, private injector: Injector) {
        this.group = this.fb.group({
            keyword: [''],
            open_schedule: [false],
            schedule: [],
            platform: ['weibo']
        })
    }

    onInput(e: Event) {
        const element = e.target as HTMLInputElement
        const value = element.value
        this.input$.next(value);
    }

    get open_schedule() {
        return !!this.group.get('open_schedule')?.value
    }

    ngOnDestroy(): void {
        this.destory$.next()
        this.destory$.complete()
    }
    ngOnInit(): void {
        this.input$.pipe(
            takeUntil(this.destory$),
            debounceTime(500),
            switchMap(key => {
                const value = this.group.value;
                return this.sdk.post('@nger/task/account', { ...value }).pipe(
                    catchError(e => of(null)),
                    filter(it => !!it)
                )
            }),
            tap((res: any) => {
                this.dataSource = res
            })
        ).subscribe()
    }
    get sdk() {
        return this.injector.get(Sdk)
    }
    get msg() {
        return this.injector.get(NzMessageService)
    }
    
    search() {
        if (this.loading) {
            return;
        }
        this.loading = true;
        const value = this.group.value;
        if (!value.keyword) {
            this.msg.error('请输入帐号名称')
        }
        this.sdk.post('@nger/task/send', { type: 'account', payload: value }).pipe(
            takeUntil(this.destory$)
        ).subscribe((res) => {
            this.loading = false;
            this.group.get('keyword')?.patchValue('')
            this.msg.success('恭喜您，成功创建检索任务')
        })
    }

    onSelect(tag: any) {
        this.group.get('keyword')?.setValue(tag.value)
    }
}
