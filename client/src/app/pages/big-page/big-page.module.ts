import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { NzButtonModule } from "ng-zorro-antd/button";
import { NzInputModule } from "ng-zorro-antd/input";
import { NzSelectModule } from "ng-zorro-antd/select";
import { NzInputNumberModule } from "ng-zorro-antd/input-number";

import { BigPage } from "./big-page";
import { NzModalModule } from "ng-zorro-antd/modal";
import { TaskTendModule } from "../../components/task-tend/task-tend.module";
import { NzStatisticModule } from "ng-zorro-antd/statistic";
import { NzGridModule } from "ng-zorro-antd/grid";
import { EchartModule } from "src/app/components/echarts/echart.module";

@NgModule({
    declarations: [
        BigPage
    ],
    imports: [
        RouterModule.forChild([{
            path: '',
            component: BigPage
        }]),
        NzSelectModule,
        FormsModule,
        CommonModule,
        NzInputModule,
        NzButtonModule,
        NzInputNumberModule,
        NzModalModule,
        TaskTendModule,
        NzStatisticModule,
        NzGridModule,
        EchartModule
    ]
})
export class BigPageModule { }