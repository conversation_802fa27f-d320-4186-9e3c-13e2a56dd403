.content {
  padding: 20px 10px;
  margin: 10px 30px;
  background: #fff;
}

.h12 {
  height: 12px;
}

.net-statistic {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;

  &-item {
    flex: 1;
    text-align: center;
  }
}

.legend2 {
  p {
    span {
      color: #fff
    }
  }
}

.title-box {
  position: absolute;
  display: flex;
  left: 0px;
  right: 0px;
  top: 20px;

  .title {
    flex: 1;
    color: #fff;
    font-size: 19px;
    text-align: center;
  }
}

.title-map {
  color: #fff;
}
