import { Component, ElementRef, Injector, isDev<PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd/message";
import { map, Subject, takeUntil } from "rxjs";
import { Sdk } from "../../services/sdk";
import prettyBytes from 'pretty-bytes';
import { Python } from "../../services/python";
import { DomSanitizer } from "@angular/platform-browser";

@Component({
    selector: 'big-page',
    styleUrls: ['./big-page.scss'],
    templateUrl: './big-page.html'
})
export class BigPage implements OnInit, OnDestroy {
    destory$: Subject<void> = new Subject();
    systemInfo: any;
    countInfo: any;

    now_date!: Date;

    year!: number;
    month!: number;
    day!: number;

    get sdk() {
        return this.injector.get(Sdk)
    }
    get msg() {
        return this.injector.get(NzMessageService)
    }
    constructor(private injector: Injector, private ele: ElementRef, private python: Python, private dom: DomSanitizer) { 
        const now_date = new Date()
        this.year = now_date.getFullYear()
        this.month = now_date.getMonth()
        this.day = now_date.getDay()
    }
    ngOnInit(): void {
        this.change()
        this.sdk.ws(`@nger/system/info`).pipe(
            takeUntil(this.destory$),
            map(res => {
                this.systemInfo = res;
            })
        ).subscribe();
        this.sdk.ws(`@nger/weibo/count-info`).pipe(
            takeUntil(this.destory$),
            map(res => {
                this.countInfo = res;
            })
        ).subscribe();
    }
    ngOnDestroy(): void {
        this.destory$.next();
        this.destory$.complete();
    }
    scheduleEventId!: number;
    t1: any;
    t2: any;
    t3: any;
    t4: any;
    t5: any;
    t6: any;
    events: any[] = [];

    toSize(val: any) {
        if (val) {
            return prettyBytes(Number(val))
        }
        return `0`
    }

    schedule() {
        this.sdk.post('@nger/weibo/schedule', {
            eid: this.scheduleEventId,
            time: `${this.t1} ${this.t2} ${this.t3} ${this.t4} ${this.t5} ${this.t6}`
        }).pipe(
            takeUntil(this.destory$)
        ).subscribe({
            next: (res: any) => {
                this.msg.info(res.message)
            }
        })
    }

    searchEvents(title: string) {
        if (title) {
            this.sdk.get('@nger/rest/wb_event/find', {
                page: 1,
                psize: 10,
                title: `like:%${title}%`,
                relations: ['city', 'typeEntity', 'categoryEntity']
            }).pipe(
                takeUntil(this.destory$)
            ).subscribe({
                next: (res: any) => {
                    this.events = res.list;
                }
            })
        } else {
            this.sdk.post('@nger/rest/wb_event/find', {
                page: 1,
                psize: 10,
                relations: ['city', 'typeEntity', 'categoryEntity']
            }).pipe(
                takeUntil(this.destory$)
            ).subscribe({
                next: (res: any) => {
                    this.events = res.list;
                }
            })
        }
    }


    isOpenSchedule: boolean = false;

    showScheduleModal() {
        this.isOpenSchedule = true;
    }
    hideScheduleModal() {
        this.isOpenSchedule = false;
    }


    text: string = `大部分初学者一定会走非常多的弯路，由于对于IT环境的不了解，对于专业技术知识的匮乏，会导致初学者不知道学什么内容，以及应该学到什么程度，就会导致走很多弯路。在学习过程中遇到一个问题好几个小时无法解决，就会导致学习效率很低，所以我建议各位一定要懂得“借势”，想要进入任何一个行业，最好是有一个有经验的人带，但如果身边没有朋友是做这方面的，就要借助“网友”的力量，我毕竟干了6年的前端开发，也有非常多的资源，我就建立了一个专门交流前端方面问题的学习群，里面也有很多大公司的技术大牛。很多时候，技术大牛的几句话就会让我们醍醐灌顶，少浪费时间，如果想要多跟有经验的人学习，就点击下面加入我的前端交流群，以后有工作的内推机会都相互推荐一下，毕竟我们是关系社会。`
    preview: { word: string, flag: string, type: string }[] = []
    count: { name: string, value: number }[] = []
    keywords: string[] = []
    sentiments: {name: string, value: number}[] = []
    word_cloud: any = []
    cover: string = `assets/001.png`
    stop_words: string[] = ['有','会','是']
    readability: number = 0
    change() {
        this.python.post('/v1/cut', {
            text: this.text
        }).subscribe((res: any) => {
            this.preview = res.data
            const count = res.count;
            this.count = Object.keys(count).map(it => {
                return { name: it, value: count[it] }
            }).sort((a, b) => b.value - a.value)
            this.keywords = res.keywords.map(([key, weight]: any) => {
                return [key, `${Math.floor(weight * 100)}px`]
            })
            this.sentiments = [
                {
                    value:(res.sentiments * 100),
                    name: '情感强度'
                }
            ]
            // this.word_cloud = this.dom.bypassSecurityTrustHtml(res.word_cloud)
            const result = {}
            this.preview.map((item) => {
                if(item.flag.startsWith('n') || item.flag.startsWith('v')){
                    if(!this.stop_words.includes(item.word)){
                        const val = Reflect.get(result, item.word) || 0
                        Reflect.set(result, item.word, val + 1)
                    }
                }
            })
            this.word_cloud = Object.keys(result).map(key => {
                return {
                    name: key,
                    value: Reflect.get(result, key)
                }
            })
            this.readability = res.readability
        })
    }
}