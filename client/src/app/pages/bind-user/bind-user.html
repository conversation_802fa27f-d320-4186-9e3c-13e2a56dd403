<div class="bind-user" nz-row>
    <div nz-col>
        <weibo-login (change)="refresh()"></weibo-login>
    </div>
    <div nz-col>
        <zhihu-login></zhihu-login>
    </div>
    <div nz-col>
        <toutiao-login></toutiao-login>
    </div>
    <div nz-col>
        <qq-login></qq-login>
    </div>
    <div nz-col>
        <wechat-login></wechat-login>
    </div>
    <div nz-col>
        <douyin-login></douyin-login>
    </div>
    <div nz-col>
        <baidu-login></baidu-login>
    </div>
</div>
<div class="h22"></div>
<div nz-row [nzGutter]="8">
    <div nz-col [nzSpan]="8" *ngFor="let user of users">
        <nz-card class="user-card" [ngStyle]="getUserStyle(user)">
            <nz-card-meta [nzTitle]="user.username" [nzAvatar]="avatarTemplate" [nzDescription]="getUserPlatform(user.platform)"></nz-card-meta>
        </nz-card>
    </div>
</div>
<ng-template #avatarTemplate>
    <nz-avatar nzSrc="https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png"></nz-avatar>
</ng-template>
