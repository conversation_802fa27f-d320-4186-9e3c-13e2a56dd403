


import { NgModule } from '@angular/core'
import { RouterModule } from '@angular/router';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { BindUserPage } from './bind-user';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzTableModule } from 'ng-zorro-antd/table';

import { LoginsModule } from '../../components/logins/logins.module';
import { NzCardModule } from 'ng-zorro-antd/card';
import { CommonModule } from '@angular/common';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';

@NgModule({
    declarations: [
        BindUserPage
    ],
    imports: [
        RouterModule.forChild([{
            path: '',
            component: BindUserPage
        }]),
        NzButtonModule,
        NzGridModule,
        NzMessageModule,
        NzModalModule,
        LoginsModule,
        NzTabsModule,
        NzTableModule,
        NzCardModule,
        CommonModule,
        NzAvatarModule
    ],
    exports: [
        RouterModule
    ]
})
export class BindUserModule { }