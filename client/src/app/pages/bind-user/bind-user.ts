import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, Injector, ChangeDetectorRef, ChangeDetectionStrategy } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd/message";
import { firstValueFrom, Observable, Subject, takeUntil } from "rxjs";
import { Sdk } from "../../services/sdk";


@Component({
    selector: 'bind-user',
    templateUrl: './bind-user.html',
    styleUrls: ['./bind-user.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class BindUserPage implements OnInit, OnDestroy {
    get msg() {
        return this.injector.get(NzMessageService)
    }
    get sdk() {
        return this.injector.get(Sdk)
    }
    constructor(private injector: Injector, private cd: ChangeDetectorRef) { }
    destory$: Subject<void> = new Subject();
    ngOnDestroy(): void {
        this.destory$.next();
        this.destory$.complete();
    }
    users: any[] = []
    ngOnInit(): void {
        this.refresh();
    }
    refresh() {
        firstValueFrom(this.getUsers()).then((users: any) => {
            this.users = users.list;
            this.cd.detectChanges();
        })
    }
    getUsers(): Observable<any> {
        return this.sdk.post('@nger/rest/spider_user/all', { order: { status: 'desc' } }).pipe(
            takeUntil(this.destory$)
        )
    }
    getUserPlatform(platform: string) {
        switch (platform) {
            case 'weibo':
                return '微博'
            case 'toutiao':
                return '头条'
            case 'zhihu':
                return '知乎'
        }
        return platform;
    }
    getUserStyle(user: any) {
        if (user.status === 2) {
            return {};
        }
        return {
            "background-color": 'red'
        }
    }
}
