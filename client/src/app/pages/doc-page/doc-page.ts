import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { DomSanitizer } from '@angular/platform-browser'
import { marked } from 'marked';
import { Subject, takeUntil } from "rxjs";
import { Sdk } from "../../services/sdk";
@Component({
    selector: `doc-page`,
    styleUrls: ['./doc-page.scss'],
    templateUrl: './doc-page.html'
})
export class DocPageComponent implements OnInit, OnDestroy {


    get sdk() {
        return this.injector.get(Sdk)
    }

    private destory$: Subject<void> = new Subject();

    constructor(private injector: Injector) { }

    ngOnDestroy(): void {
        this.destory$.next();
        this.destory$.complete();
    }

    ngOnInit(): void {
        this.getDocs();
    }

    get dom() {
        return this.injector.get(DomSanitizer)
    }


    html: any;
    getDocs() {
        this.sdk.getStatic('@nger/weibo/assets/docs/readme.md', {
        }).pipe(
            takeUntil(this.destory$)
        ).subscribe({
            next: (res) => {
                const html = marked(res, {
                    gfm: true
                })
                this.html = this.dom.bypassSecurityTrustHtml(html)
            }
        })
    }
}
