<event-search (onSelect)="onSelectEvent($event)"></event-search>

<button nz-button nzType="primary" (click)="addEvent()">添加</button>

<div class="h22"></div>
<div nz-row [nz<PERSON><PERSON>]="8">
    <div nz-col [nzSpan]="12" *ngFor="let event of events">
        <nz-card [nzTitle]="event.title">
            <div class="gonggao-timeline">
                <nz-timeline [nzReverse]="true" nzMode="alternate">
                    <ng-container *ngFor="let timeline of event.timelines">
                        <nz-timeline-item [nzDot]="dotTemplate" [nzLabel]="formatTime(timeline.post_at)">
                            <p nz-typography nzEllipsis nzExpandable [nzEllipsisRows]="3">
                                {{timeline.content}}
                            </p>
                        </nz-timeline-item>
                        <nz-timeline-item nzColor="green">
                            <p nz-typography nzEllipsis nzExpandable [nzEllipsisRows]="3" >负面帖子<i>10</i>个,帖子总数<i>100</i>个,负面评论<i>10</i>个,评论总数<i>100</i>个</p>
                        </nz-timeline-item>
                    </ng-container>
                </nz-timeline>
            </div>
        </nz-card>
    </div>
</div>

<ng-template #dotTemplate>
    <span nz-icon nzType="clock-circle-o" style="font-size: 16px;"></span>
</ng-template>
