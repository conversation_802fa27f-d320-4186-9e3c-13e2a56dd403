import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { NzButtonModule } from "ng-zorro-antd/button";
import { GonggaoComponent } from "./gonggao";
import { NzTimelineModule } from 'ng-zorro-antd/timeline';
import { EventListModule } from "../../components/event-list/event-list.module";
import { EventSearchModule } from "../../components/event-search/event-search.module";
import { NzCardModule } from "ng-zorro-antd/card";
import { NzGridModule } from "ng-zorro-antd/grid";
import { CommonModule } from "@angular/common";
import { NzTypographyModule } from 'ng-zorro-antd/typography'
import { NzIconModule } from "ng-zorro-antd/icon";
@NgModule({
    imports: [
        RouterModule.forChild([{
            path: '',
            component: GonggaoComponent
        }]),
        NzButtonModule,
        CommonModule,
        NzTimelineModule,
        EventListModule,
        EventSearchModule,
        NzCardModule,
        NzGridModule,
        NzTypographyModule,
        NzIconModule
    ],
    declarations: [
        GonggaoComponent
    ]
})
export class GonggaoModule{}