import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { firstValueFrom, Observable, Subject, takeUntil } from "rxjs";
import { Sdk } from "../../services/sdk";

@Component({
    selector: 'gonggao-page',
    templateUrl: './gonggao.html',
    styleUrls: ['./gonggao.scss']
})
export class GonggaoComponent implements OnInit, OnDestroy {

    timelines: Timeline[] = []
    events: any[] = []

    get sdk() {
        return this.injector.get(Sdk)
    }
    destory$: Subject<void> = new Subject()
    constructor(private injector: Injector) { }

    ngOnDestroy(): void {
        this.destory$.next()
        this.destory$.complete()
    }
    ngOnInit(): void {
        const gonggao = localStorage.getItem('gonggao')
        if (gonggao) {
            const events: any[] = JSON.parse(gonggao)
            Promise.all(events.map(async event => {
                const timeline = await firstValueFrom(this.getGonggao(event.id))
                event.timelines = timeline.list
                return event
            })).then(events => {
                this.events = events;
            })
        }
    }

    formatTime(date: Date){
        const isDate = date instanceof Date
        if(!isDate){
            date = new Date(date)
        }
        const year = date.getFullYear()
        const month = date.getMonth()+1
        const day = date.getDay()
        return `${year}-${month}-${day}`
    }

    async addEvent() {
        if (this.selectEvent) {
            const timeline = await firstValueFrom(this.getGonggao(this.selectEvent.id))
            this.selectEvent.timelines = timeline.list
            this.events.push(this.selectEvent)
            localStorage.setItem('gonggao', JSON.stringify(this.events))
        }
        this.selectEvent = undefined
    }

    selectEvent: any;
    onSelectEvent(event: any) {
        this.selectEvent = event
    }

    getGonggao(eid: string): Observable<any> {
        return this.sdk.get('@nger/rest/spider_gonggao/find', { eid: eid }).pipe(
            takeUntil(this.destory$)
        )
    }
}

export interface Timeline {
    date: Date;
    title: string;
    author: string;
}
