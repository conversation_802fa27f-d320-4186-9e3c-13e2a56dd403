import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { NzButtonModule } from "ng-zorro-antd/button";
import { NzGridModule } from "ng-zorro-antd/grid";
import { NzInputModule } from "ng-zorro-antd/input";
import { NzSelectModule } from "ng-zorro-antd/select";
import { NzStatisticModule } from "ng-zorro-antd/statistic";
import { EchartModule } from "../../components/echarts/echart.module";
import { EventListModule } from "../../components/event-list/event-list.module";
import { HomePageComponent } from "./home-page";

@NgModule({
    declarations: [
        HomePageComponent
    ],
    imports: [
        NzGridModule,
        NzStatisticModule,
        NzButtonModule,
        EventListModule,
        NzSelectModule,
        FormsModule,
        CommonModule,
        NzInputModule,
        EchartModule,
        RouterModule.forChild([{
            path: '',
            component: HomePageComponent
        }])
    ]
})
export class HomePageModule { }