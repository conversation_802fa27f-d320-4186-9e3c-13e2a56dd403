import { Component, Injector } from "@angular/core";
import { forkJoin, map, mergeAll, Subject, switchMap, takeUntil, zipAll } from "rxjs";
import { Sdk } from "../../services/sdk";
import { XlsxService } from '@delon/abc/xlsx';
import { NzMessageService } from "ng-zorro-antd/message";

@Component({
    selector: 'home-page',
    styleUrls: ['./home-page.scss'],
    templateUrl: './home-page.html'
})
export class HomePageComponent {
    get sdk() {
        return this.injector.get(Sdk)
    }
    get msg() {
        return this.injector.get(NzMessageService)
    }
    constructor(private injector: Injector, private xlsx: XlsxService) { }
    ngOnInit(): void {
        this.init();
    }
    private destory$: Subject<void> = new Subject();
    /**
     * 初始化
     */
    event_count: number = 0;
    topic_count: number = 0;
    account_count: number = 0;
    article_count: number = 0;
    comment_count: number = 0;
    user: any;
    init() {
        this.user = this.sdk.getUser();
    }
    ngOnDestroy(): void {
        this.destory$.next();
        this.destory$.complete();
    }

    async export() {
        if (!this.eventId) {
            return;
        }
        const event = this.events.find(it => it.id === this.eventId)
        if (!event) {
            return;
        }
        const topics = this.sdk.post('@nger/rest/spilder_topic/all', {
            eid: event.id,
            relations: [
                'platformEntity',
                'user',
                'event'
            ]
        }).pipe(
            map((res: any) => res.list)
        )
        const articles = this.sdk.post('@nger/rest/spilder_topic_article/all', {
            eid: event.id,
            relations: [
                'topic',
                'event',
                'platformEntity',
                'account'
            ]
        }).pipe(
            map((res: any) => res.list)
        )
        const comments = this.sdk.post('@nger/rest/spilder_topic_article_comment/all', {
            eid: event.id,
            relations: [
                'platformEntity',
                'event',
                'topic',
                'article',
                'parent'
            ]
        }).pipe(
            map((res: any) => res.list)
        );
        forkJoin(
            {
                topics,
                articles,
                comments
            }
        ).pipe(
            takeUntil(this.destory$),
            switchMap(res => {
                const data = localStorage.getItem('current') || '';
                const user = JSON.parse(data);
                return this.sdk.post('@nger/rest/spilder_export_log/add', {
                    eid: event.id,
                    event_title: event.title,
                    uid: user.uid,
                    username: user.username
                }).pipe(
                    map((log: any) => {
                        if (log.errno === 200) {
                            return res;
                        }
                        return undefined;
                    })
                )
            })
        ).subscribe({
            next: (res) => {
                if (!res) {
                    this.msg.error(`对不起，导出失败！`)
                    return;
                }
                const { topics, articles, comments } = res;
                this.xlsx.export({
                    format: 'xlsx',
                    filename: `${event!.title}-${new Date().getTime()}.xlsx`,
                    sheets: [{
                        name: `事件`,
                        data: [
                            ['事件名称', '地域', '事件性质', '行业类型'],
                            [event.title, event.address, event.type, event.category]
                        ]
                    }, {
                        name: `话题`,
                        data: [
                            ['所属事件名称', '话题名称', '话题文本', '阅读量', '讨论量', '原创人数', '主持人ID', '发布媒体ID', '地域', '出现时间'],
                            ...topics.map((t: any) => {
                                return [
                                    t.event.title,
                                    t.title,
                                    t.summary,
                                    t.read_count,
                                    t.mention_count,
                                    t.ori_uv_Count,
                                    t.claim_name,
                                    t.user?.nickname,
                                    '全国',
                                    new Date(Number(t.create_at) * 1000)
                                ]
                            })
                        ]
                    }, {
                        name: `帖子`,
                        data: [
                            ['所属话题', '发帖ID', 'ID类型', '帖子文本', '转发', '评论', '点赞', '关联@ID', '是否原创', '地域', '发帖时间'],
                            ...articles.map((t: any) => {
                                return [
                                    t.topic?.title,
                                    t.nickname,
                                    t.account?.type || '自媒体',
                                    t.text_raw,
                                    t.reposts_count,
                                    t.comments_count,
                                    t.attitudes_count,
                                    (t.structs || []).join(','),
                                    '是',
                                    (t.region_name || '').replace('发布于 ', ''),
                                    new Date(t.create_at)
                                ]
                            })
                        ]
                    }, {
                        name: `评论`,
                        data: [
                            ['所属帖子ID', '评论ID', '评论文本', '评论时间'],
                            ...comments.filter((it: any) => !it.parent).map((t: any) => {
                                return [
                                    t.article.text_raw,
                                    t.cid,
                                    t.text,
                                    new Date(t.created_at)
                                ]
                            })
                        ]
                    }, {
                        name: `子评论`,
                        data: [
                            ['所属评论', '评论ID', '评论文本', '评论时间'],
                            ...comments.filter((it: any) => it.parent).map((t: any) => {
                                return [
                                    t.parent.text,
                                    t.cid,
                                    t.text,
                                    new Date(t.created_at)
                                ]
                            })
                        ]
                    }]
                }).then(res => {
                    this.msg.success(`恭喜您，导出成功`)
                }).catch((e: Error) => {
                    this.msg.error(e.message)
                })
            }
        })
    }

    events: any[] = [];
    eventId!: number;
    searchEvents(title: string) {
        if (title) {
            this.sdk.get('@nger/rest/wb_event/find', {
                page: 1,
                psize: 10,
                title: `like:%${title}%`,
                relations: ['city', 'typeEntity', 'categoryEntity']
            }).pipe(
                takeUntil(this.destory$)
            ).subscribe({
                next: (res: any) => {
                    this.events = res.list;
                }
            })
        } else {
            this.sdk.post('@nger/rest/wb_event/find', {
                page: 1,
                psize: 10,
                relations: ['city', 'typeEntity', 'categoryEntity']
            }).pipe(
                takeUntil(this.destory$)
            ).subscribe({
                next: (res: any) => {
                    this.events = res.list;
                }
            })
        }
    }
    
}
