import { Component, Injector } from "@angular/core";
import { Router } from "@angular/router";
import { NzMessageService } from "ng-zorro-antd/message";
import { Subject, takeUntil } from "rxjs";
import { Sdk } from "../../services/sdk";

@Component({
    selector: `login-page`,
    templateUrl: './login-page.html',
    styleUrls: ['./login-page.scss']
})
export class LoginPageComponent {
    username!: string;
    password!: string;

    private destory$: Subject<void> = new Subject();
    get sdk() {
        return this.injector.get(Sdk)
    }
    get msg() {
        return this.injector.get(NzMessageService)
    }
    get router() {
        return this.injector.get(Router)
    }
    constructor(private injector: Injector) { }
    login() {
        this.sdk.post('@nger/weibo/loginAdmin', {
            username: this.username,
            password: this.password
        }).pipe(
            takeUntil(this.destory$)
        ).subscribe({
            next: (res: any) => {
                if (res.errno !== 200) {
                    this.msg.error(res.message)
                } else {
                    this.msg.success(res.message)
                    const data = res.data;
                    localStorage.setItem('current', JSON.stringify(data));
                    this.router.navigate(['home'])
                }
            }
        })
    }
}
