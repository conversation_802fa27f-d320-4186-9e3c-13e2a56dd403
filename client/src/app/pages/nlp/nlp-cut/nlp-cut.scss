:host {
    padding: 10px 20px;
    display: block;
    overflow-y: auto;
    max-height: 100%;
    max-width: 100%;
}

.cut-form {
    display: block;
    margin-top: 20px;

    .cut-all {
        margin-left: 10px;
        margin-bottom: 10px;
    }
}

.keyword-box {
    background: #fff;
    padding: 5px 10px;

    span {
        margin-left: 5px;
    }
}

.cut-count {
    display: flex;
    max-width: 100%;
    width: 100%;
    overflow: hidden;

    div {
        flex: 1;
        overflow: hidden;
    }
}

.cut-preview {
    font-size: 14px;
    background: #fff;
    padding: 5px 10px;

    .word {
        margin-left: 5px;

        .word-type {
            font-size: 8px;
            color: #4b4b4b
        }
    }
}

.title {
    margin: 0px;
    font-size: 16px;
    line-height: 50px;
    height: 50px;
    font-weight: 600;
}

.word-m {
    color: aqua
}

.word-n {
    color: red
}

.word-d {
    color: blue
}

.word-v {
    color: greenyellow
}

.word-c {
    color: fuchsia
}

.word-p {
    color: burlywood
}

.word-r {
    color: teal
}

.word-a {
    color: saddlebrown
}

.word-f {
    color: gray
}