import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Component, OnInit, OnDestroy, Injector } from "@angular/core";
import { DomSanitizer } from "@angular/platform-browser";
import { Python } from "src/app/services/python";


@Component({
    selector: 'nlp-cut',
    templateUrl: './nlp-cut.html',
    styleUrls: ['./nlp-cut.scss']
})
export class NlpCutComponent implements OnInit, OnDestroy {
    text: string = `大部分初学者一定会走非常多的弯路，由于对于IT环境的不了解，对于专业技术知识的匮乏，会导致初学者不知道学什么内容，以及应该学到什么程度，就会导致走很多弯路。在学习过程中遇到一个问题好几个小时无法解决，就会导致学习效率很低，所以我建议各位一定要懂得“借势”，想要进入任何一个行业，最好是有一个有经验的人带，但如果身边没有朋友是做这方面的，就要借助“网友”的力量，我毕竟干了6年的前端开发，也有非常多的资源，我就建立了一个专门交流前端方面问题的学习群，里面也有很多大公司的技术大牛。很多时候，技术大牛的几句话就会让我们醍醐灌顶，少浪费时间，如果想要多跟有经验的人学习，就点击下面加入我的前端交流群，以后有工作的内推机会都相互推荐一下，毕竟我们是关系社会。`
    preview: { word: string, flag: string, type: string }[] = []
    count: { name: string, value: number }[] = []
    keywords: string[] = []
    sentiments: { name: string, value: number }[] = []
    word_cloud: any = []
    cover: string = `assets/001.png`
    stop_words: string[] = ['有', '会', '是']
    readability: number = 0
    constructor(private python: Python, private dom: DomSanitizer, private injector: Injector) { }
    change() {
        this.python.post('/v1/cut', {
            text: this.text
        }).subscribe((res: any) => {
            this.preview = res.data
            const count = res.count;
            this.count = Object.keys(count).map(it => {
                return { name: it, value: count[it] }
            }).sort((a, b) => b.value - a.value)
            this.keywords = res.keywords.map(([key, weight]: any) => {
                return [key, `${Math.floor(weight * 100)}px`]
            })
            this.sentiments = [
                {
                    value: (res.sentiments * 100),
                    name: '情感强度'
                }
            ]
            // this.word_cloud = this.dom.bypassSecurityTrustHtml(res.word_cloud)
            const result = {}
            this.preview.map((item) => {
                if (item.flag.startsWith('n') || item.flag.startsWith('v')) {
                    if (!this.stop_words.includes(item.word)) {
                        const val = Reflect.get(result, item.word) || 0
                        Reflect.set(result, item.word, val + 1)
                    }
                }
            })
            this.word_cloud = Object.keys(result).map(key => {
                return {
                    name: key,
                    value: Reflect.get(result, key)
                }
            })
            this.readability = res.readability
        })
    }

    ngOnDestroy(): void {

    }
    get http() {
        return this.injector.get(HttpClient)
    }
    ngOnInit(): void {
        const headers = new HttpHeaders()
        headers.set('Content-Type', 'application/x-www-form-urlencoded')
        this.http.post('http://localhost:8082/cut', {
            texts: ['你好']
        }, {
            headers: headers
        }).subscribe(res => console.log(res))
        this.change()
    }
}