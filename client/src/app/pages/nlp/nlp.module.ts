
import { NgModule } from '@angular/core'
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NlpCutComponent } from './nlp-cut/nlp-cut';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { CommonModule } from '@angular/common';
import { EchartModule } from '../../components/echarts/echart.module';
@NgModule({
    imports: [
        NzInputModule,
        FormsModule,
        NzSelectModule,
        NzRadioModule,
        CommonModule,
        EchartModule,
        RouterModule.forChild([{
            path: '',
            pathMatch: 'full',
            redirectTo: 'nlp-cut'
        },{
            path: 'nlp-cut',
            component: NlpCutComponent
        }])
    ],
    declarations: [NlpCutComponent],
    exports: [
        RouterModule
    ]
})
export class NlpModule { }
