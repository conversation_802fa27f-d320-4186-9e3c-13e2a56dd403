import { Component, ElementRef, Injector, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from "@angular/core";
import { DomSanitizer } from "@angular/platform-browser";
import { ActivatedRoute } from "@angular/router";
import { Subject, takeUntil } from "rxjs";


@Component({
    selector: `rest-page`,
    templateUrl: `./rest-page.html`,
    styleUrls: ['./rest-page.scss']
})
export class RestPageComponent implements OnDestroy {

    get activatedRoute() {
        return this.injector.get(ActivatedRoute)
    }

    get dom() {
        return this.injector.get(DomSanitizer)
    }

    private destory$: Subject<void> = new Subject();
    path!: any;
    constructor(private injector: Injector) {
        this.activatedRoute.queryParams.pipe(
            takeUntil(this.destory$)
        ).subscribe({
            next: (params: any) => {
                this.loading = true;
                this.path = this.dom.bypassSecurityTrustResourceUrl(params.path);
            }
        })
    }

    ngOnDestroy(): void {
        this.destory$.next();
        this.destory$.complete();
    }

    loading: boolean = false;
    onLoad(e: any) {
        this.loading = false;
    }
}
