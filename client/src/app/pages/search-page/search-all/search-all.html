<form nz-form class="box-inline" [formGroup]="group" (ngSubmit)="search()">
    <div class="title">按指定关键字爬取全网数据</div>
    <nz-form-item>
        <nz-input-group nzSearch nzSize="large" [nzAddOnAfter]="suffixButton">
            <input type="text" nz-input formControlName="keyword" placeholder="请输入关键字" />
        </nz-input-group>
        <ng-template #suffixButton>
            <button nz-button nzType="primary" nzSize="large" nzSearch (click)="search()">
                <i nz-icon [nzIconfont]="'icon-minganguanjianzi'"></i>
                搜索
            </button>
        </ng-template>
    </nz-form-item>
    <nz-form-item>
        <keyword-page #keyword (onSelect)="onSelect($event)"></keyword-page>
    </nz-form-item>
    <nz-form-item>
        <nz-form-label nzFor="open_schedule">开启定时采集</nz-form-label>
        <nz-form-control>
            <nz-switch formControlName="open_schedule"></nz-switch>
        </nz-form-control>
    </nz-form-item>
    <nz-form-item *ngIf="open_schedule">
        <nz-form-label nzFor="schedule">定时采集</nz-form-label>
        <nz-form-control>
            <input nz-input type="text" formControlName="schedule" />
            <p>采集所有信息后定期更新 秒 分 时 日 月 年</p>
        </nz-form-control>
    </nz-form-item>
</form>

<button nz-button nzType="primary" (click)="oneKeyTakeAll()">一键快速抓取</button>

<div class="h20"></div>