.h20 {
    height: 20px;
}

:host {
    display: block;
    flex: 1;
    width: 100%;
    padding: 0 50px;
}

.title {
    font-size: 42px;
    margin: 135px 74px 40px 0;
    text-align: center;
}

.search_keyword {
    width: 700px;
    height: 48px;
    border: 1px solid #999;
    box-shadow: none;
    font-size: 16px;
    color: #333;
    border-radius: 8px;
    padding-left: 15px;
    padding-right: 50px;
}

.search {
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-items: center;
    align-items: center;
}

.search-input {
    display: flex;

    input {
        flex: 1;
    }

    i {
        width: 45px;
        height: 45px;
        font-size: 45px;
        margin-left: 20px;
        cursor: pointer;
    }
}

keyword-page{
    max-height: 300px;
    overflow: auto;
    display: block;
}