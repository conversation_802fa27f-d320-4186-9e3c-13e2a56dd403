import { Component, Injector, ViewChild } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subject, takeUntil } from 'rxjs';
import { KeywordPage } from 'src/app/components/keyword-page/keyword-page';
import { Sdk } from '../../../services/sdk';

@Component({
    selector: 'search-all',
    templateUrl: './search-all.html',
    styleUrls: ['./search-all.scss']
})
export class SearchAllComponent {
    group!: FormGroup
    constructor(private fb: FormBuilder, private injector: Injector) {
        this.group = this.fb.group({
            keyword: [''],
            open_schedule: [false],
            schedule: []
        })
    }
    get open_schedule() {
        return !!this.group.get('open_schedule')?.value
    }

    destory$: Subject<void> = new Subject()
    ngOnDestroy(): void {
        this.destory$.next()
        this.destory$.complete()
    }
    ngOnInit(): void {

    }
    get sdk() {
        return this.injector.get(Sdk)
    }
    get msg() {
        return this.injector.get(NzMessageService)
    }
    loading: boolean = false;
    search() {
        if (this.loading) {
            return;
        }
        this.loading = true;
        const value = this.group.value;
        if (!value.keyword) {
            this.msg.error('请输入关键字')
        }
        const list = ['heimao', 'qq-new', 'toutiao', 'wechat', 'weibo', 'zhihu', 'douyin', 'baidu'].map(platform => {
            return {
                type: 'search',
                payload: { ...value, platform }
            }
        })
        this.sdk.post('@nger/task/publishs', { type: 'search', list }).pipe(
            takeUntil(this.destory$)
        ).subscribe((res) => {
            this.loading = false;
            this.group.get('keyword')?.patchValue('')
            this.msg.success('恭喜您，成功创建检索任务')
        })
    }

    onSelect(tag: any) {
        this.group.get('keyword')?.setValue(tag.value)
    }
    @ViewChild('keyword', {
        read: KeywordPage,
        static: true
    })
    keyword!: KeywordPage
    oneKeyTakeAll() {
        if (this.keyword) {
            const value = this.group.value;
            const list = this.keyword.tags.map(tag => {
                return ['weibo'].map(platform => {
                    return {
                        type: 'search',
                        payload: { ...value, keyword: tag.value, platform }
                    }
                })
            }).flat()
            this.sdk.post('@nger/task/publishs', { type: 'search', list }).pipe(
                takeUntil(this.destory$)
            ).subscribe((res) => {
                this.loading = false;
                this.group.get('keyword')?.patchValue('')
                this.msg.success('恭喜您，成功创建检索任务')
            })
        }
        console.log(this.keyword)
    }
}
