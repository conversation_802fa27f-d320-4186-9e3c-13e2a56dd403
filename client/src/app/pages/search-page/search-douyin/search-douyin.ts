import { Component, Injector } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subject, takeUntil } from 'rxjs';
import { Sdk } from '../../../services/sdk';

@Component({
    selector: 'search-douyin',
    templateUrl: './search-douyin.html',
    styleUrls: ['./search-douyin.scss']
})
export class SearchDouyinComponent {
    group!: FormGroup
    onSelect(tag: any){
        this.group.get('keyword')?.setValue(tag.value)
    }
    constructor(private fb: FormBuilder, private injector: Injector) {
        this.group = this.fb.group({
            keyword: [''],
            open_schedule: [false],
            schedule: [],
            platform: 'douyin'
        })
    }
    get open_schedule() {
        return !!this.group.get('open_schedule')?.value
    }

    destory$: Subject<void> = new Subject()
    ngOnDestroy(): void {
        this.destory$.next()
        this.destory$.complete()
    }
    ngOnInit(): void {

    }
    get sdk() {
        return this.injector.get(Sdk)
    }
    get msg() {
        return this.injector.get(NzMessageService)
    }
    search() {
        const value = this.group.value;
        if (!value.keyword) {
            this.msg.error('请输入关键字')
        }
        this.sdk.post('@nger/task/send', { type: 'search', payload: value }).pipe(
            takeUntil(this.destory$)
        ).subscribe(()=>{
            this.msg.success('恭喜您，成功创建检索任务')
        })
    }
}
