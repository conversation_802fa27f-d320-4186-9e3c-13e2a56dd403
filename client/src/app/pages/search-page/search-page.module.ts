import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { NzButtonModule } from "ng-zorro-antd/button";
import { NzDatePickerModule } from "ng-zorro-antd/date-picker";
import { NzFormModule } from "ng-zorro-antd/form";
import { NzIconModule } from "ng-zorro-antd/icon";
import { NzInputModule } from "ng-zorro-antd/input";
import { NzInputNumberModule } from "ng-zorro-antd/input-number";
import { NzRadioModule } from "ng-zorro-antd/radio";
import { NzSwitchModule } from "ng-zorro-antd/switch";

import { NzTimePickerModule } from "ng-zorro-antd/time-picker";
import { KeywordPageModule } from "src/app/components/keyword-page/keyword-page.module";
import { SearchAllComponent } from "./search-all/search-all";
import { SearchBaiduComponent } from "./search-baidu/search-baidu";
import { SearchDouyinComponent } from "./search-douyin/search-douyin";
import { SearchHeimaoComponent } from "./search-heimao/search-heimao";
import { SearchPageComponent } from "./search-page";
import { SearchQqNewComponent } from "./search-qq-new/search-qq-new";
import { SearchToutiaoComponent } from "./search-toutiao/search-toutiao";
import { SearchWechatComponent } from "./search-wechat/search-wechat";
import { SearchWeiboComponent } from "./search-weibo/search-weibo";
import { SearchZhihuComponent } from "./search-zhihu/search-zhihu";

@NgModule({
    declarations: [
        SearchPageComponent,
        SearchToutiaoComponent,
        SearchWeiboComponent,
        SearchZhihuComponent,
        SearchHeimaoComponent,
        SearchWechatComponent,
        SearchQqNewComponent,
        SearchAllComponent,
        SearchBaiduComponent,
        SearchDouyinComponent
    ],
    exports: [
        RouterModule
    ],
    imports: [
        CommonModule,
        NzIconModule,
        NzInputModule,
        NzRadioModule,
        NzFormModule,
        NzTimePickerModule,
        NzDatePickerModule,
        NzInputNumberModule,
        ReactiveFormsModule,
        NzSwitchModule,
        NzButtonModule,
        KeywordPageModule,
        RouterModule.forChild([{
            path: '',
            component: SearchPageComponent,
            children: [{
                path: 'all',
                component: SearchAllComponent
            }, {
                path: 'weibo',
                component: SearchWeiboComponent
            }, {
                path: 'zhihu',
                component: SearchZhihuComponent
            }, {
                path: 'toutiao',
                component: SearchToutiaoComponent
            }, {
                path: 'heimao',
                component: SearchHeimaoComponent
            }, {
                path: 'wechat',
                component: SearchWechatComponent
            }, {
                path: 'qq-new',
                component: SearchQqNewComponent
            }, {
                path: 'douyin',
                component: SearchDouyinComponent
            }, {
                path: 'baidu',
                component: SearchBaiduComponent
            }]
        }]),

    ]
})
export class SearchPageModule { }

