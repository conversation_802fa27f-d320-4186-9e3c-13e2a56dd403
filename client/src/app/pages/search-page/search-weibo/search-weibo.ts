import { Component, Injector, OnInit, OnD<PERSON>roy } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subject, takeUntil } from 'rxjs';
import { Sdk } from '../../../services/sdk';

@Component({
    selector: 'search-weibo',
    templateUrl: './search-weibo.html',
    styleUrls: ['./search-weibo.scss']
})
export class SearchWeiboComponent implements OnInit, OnDestroy {

    group!: FormGroup;

    type_options: any[] = [{
        label: '全部',
        value: 0
    }, {
        label: '热门',
        value: 1
    }, {
        label: '原创',
        value: 2
    }, {
        label: '关注人',
        value: 3
    }, {
        label: '认证用户',
        value: 4
    }, {
        label: '媒体',
        value: 5
    }, {
        label: '观点',
        value: 6
    }]

    include_options: any[] = [{
        label: '全部',
        value: 0
    }, {
        label: '含图片',
        value: 1
    }, {
        label: '含视频',
        value: 2
    }, {
        label: '含音乐',
        value: 3
    }, {
        label: '含短链',
        value: 4
    }]
    get msg() {
        return this.injector.get(NzMessageService)
    }
    get sdk() {
        return this.injector.get(Sdk)
    }
    onSelect(tag: any){
        this.group.get('keyword')?.setValue(tag.value)
    }
    constructor(private fb: FormBuilder, private injector: Injector) {
        const m = Math.floor(Math.random() * 60)
        this.group = this.fb.group({
            keyword: [''],
            type: [0],
            include: [0],
            time: [],
            open_schedule: [false],
            step: [12],
            schedule: [`* ${m} * * * *`],
            platform: ['weibo']
        })
    }

    get open_schedule() {
        return !!this.group.get('open_schedule')?.value
    }

    destory$: Subject<void> = new Subject()
    ngOnDestroy(): void {
        this.destory$.next()
        this.destory$.complete()
    }
    ngOnInit(): void {

    }

    search() {
        const value = this.group.value;
        if (!value.keyword) {
            this.msg.error('请输入关键字')
        }
        this.sdk.post('@nger/task/send', { type: 'search', payload: value }).pipe(
            takeUntil(this.destory$)
        ).subscribe(()=>{
            this.msg.success('恭喜您，成功创建检索任务')
        })
    }
}
