import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";

@Injectable({
    providedIn: 'root'
})
export class MenuSdk {
    getMenuList(): Observable<IMenu[]> {
        return of([
            {
                title: '关键字',
                icon: 'icon-minganguanji<PERSON>zi',
                link: '@nger/rest/?name=spider_keyword'
            }, 
            {
                title: '事件管理',
                icon: 'icon-shijianguanli',
                link: '@nger/rest/?name=wb_event'
            }, 
            {
                title: '事件线索',
                icon: 'icon-shijianguanli',
                link: '@nger/rest/?name=spider_gonggao'
            },
            {
                title: '城市管理',
                icon: 'icon-chengshi',
                link: '@nger/rest/?name=wb_city'
            }, 
            {
                title: '媒体管理',
                icon: 'icon-duomeiti',
                link: '@nger/rest/?name=spilder_account'
            }, 
            {
                title: '行业类型',
                icon: 'icon-leixing',
                link: '@nger/rest/?name=wb_event_category'
            }, 
            {
                title: '事件性质',
                icon: 'icon-tubiaozhi<PERSON><PERSON>ban_jigoux<PERSON>zhi',
                link: '@nger/rest/?name=wb_event_type'
            },
        ])
    }
}

export interface IMenu {
    title: string;
    icon: string;
    link: string;
}