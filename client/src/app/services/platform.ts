import { Injectable, Injector } from "@angular/core";
import { Observable, of } from "rxjs";
import { Sdk } from "./sdk";

@Injectable({
    providedIn: 'root'
})
export class PlatformSdk {

    get sdk() {
        return this.injector.get(Sdk)
    }
    constructor(private injector: Injector) { }
    getPlatformList(): Observable<IPlatform[]> {
        return of([{
            title: '登录微博',
            icon: 'icon-xinlangweibo',
            name: 'weibo',
        },
            // {
            //     title: '登录知乎',
            //     icon: 'icon-shejiaotubiao-46',
            //     name: 'zhihu',
            // },
            // {
            //     title: '登录头条',
            //     icon: 'icon-toutiaoyangshi',
            //     name: 'toutiao',
            // }
        ])
    }
}

export interface IPlatform {
    title: string;
    icon: string;
    name: string;
}
