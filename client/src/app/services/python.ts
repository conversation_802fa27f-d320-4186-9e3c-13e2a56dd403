import { HttpClient } from "@angular/common/http";
import { Injectable, isDevMode } from "@angular/core";

@Injectable({
    providedIn: 'root'
})
export class Python {
    constructor(private http: HttpClient){}
    get base() {
        if (isDevMode()) {
            return `http://localhost:8082/`
        } else {
            return `/`
        }
    }
    createUrl(path: string) {
        return `${this.base}${path}`
    }
    post(path: string, body: any) {
        return this.http.post(this.createUrl(path), body)
    }
}
