import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable, Injector, isDevMode } from "@angular/core";
import { Router } from "@angular/router";
import { Observable } from "rxjs";

@Injectable({
    providedIn: 'root'
})
export class Sdk {

    get base() {
        if (isDevMode()) {
            return `http://localhost:8081/`
        } else {
            return `/`
        }
    }

    get wsBase() {
        if (isDevMode()) {
            return `ws://localhost:8081/`
        } else {
            const host = window.location.hostname;
            const port = window.location.port;
            return `ws://${host}:${port}/`
        }
    }

    get router() {
        return this.injector.get(Router)
    }

    get http() {
        return this.injector.get(HttpClient)
    }
    constructor(private injector: Injector) { }

    get(path: string, params: any = {}) {
        return this.http.get(this.createUrl(path), {
            params
        })
    }

    getStatic(path: string, headers: any = {}) {
        return this.http.get(this.createUrl(path), {
            headers: new HttpHeaders(headers),
            responseType: 'text'
        })
    }

    getUser() {
        let data = localStorage.getItem('current') || '';
        try {
            return JSON.parse(data)
        } catch (e) {
            return {}
        }
    }

    post(path: string, body: any) {
        return this.http.post(this.createUrl(path), body)
    }

    ws(path: string) {
        return new Observable((obs) => {
            const ws = new WebSocket(this.createWsUrl(path))
            ws.onmessage = (e) => {
                const data = JSON.parse(e.data)
                obs.next(data)
            }
            ws.onerror = (e) => {
                obs.error(e)
            }
            ws.onclose = () => {
                obs.complete();
            }
            return () => {
                ws.close();
            }
        })
    }

    goto(path: string) {
        this.router.navigate(['rest'], {
            queryParams: {
                path: this.createUrl(path)
            }
        })
    }

    createWsUrl(path: string) {
        return `${this.wsBase}${path}`
    }

    createUrl(path: string) {
        return `${this.base}${path}`
    }
}