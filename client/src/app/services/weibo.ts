import { Injectable, Injector } from "@angular/core";
import { Sdk } from "./sdk";

@Injectable({
    providedIn: 'root'
})
export class WeiboSdk {
    type: string = `weibo`
    get sdk() {
        return this.injector.get(Sdk)
    }
    constructor(private injector: Injector) { }
    /**
     * 登录
     * @returns 登录二维码
     */
    login() {
        return this.sdk.get('@nger/weibo/login')
    }
    /**
     * 检查是否登录成功
     * @returns 登录后的用户信息
     */
    loginSuccess() {
        return this.sdk.get('@nger/weibo/loginSuccess')
    }
    /**
     * 退出登录
     * @returns 清除cookie
     */
    logout() {
        return this.sdk.get('@nger/weibo/logout')
    }
}
