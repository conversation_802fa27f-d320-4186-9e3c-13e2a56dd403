/* 引入常用样式 */
@import "mixin/_clearfix.scss";
@import "func/_px2em.scss";
@import "func/_px2percent.scss";
@import "mixin/_media.scss";
/* 字体引入 */
 @font-face {
  font-family: Rubik;
  src: url("../fonts/Rubik-Light.ttf");
  src: url("../fonts/rubik-regular-webfont.ttf");
  src: url("../fonts/Rubik-Medium.ttf");
}
@font-face {
  font-family: DigifaceWide;
  src: url("../fonts/digifaw.ttf");
}
/* 初始化 start*/
* {
  box-sizing: border-box;
}

b {
  font-weight: normal;
}

/* 声明字体变量 */
$fs_3840: 64px;
$fs_1920: 32px;

html,
body {
  width: 100%;
  height: 100%;
  color: #fff; 
  @include media3840 {
    font-size: $fs_3840;
  }
  @include media1920 {
    font-size: $fs_1920;
  }
}

ul,
li,
dl,
dt,
dd {
  padding: 0;
  margin: 0;

  list-style: none;
}

h1,
h2,
h3,
h4,
div {
  padding: 0;
  margin: 0;
  font-weight: normal;
}
h3{
  font-size: px2em(36,64);
  color: #fff;
}
p{
  margin: 0;
  padding: 0;
}
/* 初始化 end */

/* 公共部分样式 start*/
body {
  position: relative;
  padding: 0;
  margin: 0;
  font-family: MicrosoftYaHei;
  overflow: hidden;

  &:before {
    position: absolute;
    top: 7.6%;
    right: 0;
    width: 5.2%;
    height: 12%;
    background: url(../img/circle.png) no-repeat;
    background-size: 100%;

    content: "";
  }
}

/* 最外层盒子样式 */
.root-wrap {
  height: 100%;
  padding: 0 1.5%;
  background: url("../img/bg.png") no-repeat center top;
  background-size: 100% 100%;
}
/* 头部样式 */
header {
  position: relative;
  width: 100%;
  height: 10%;
  min-height: 90px;
  text-align: center;
  padding-top: 1%;
  margin-bottom: 1%;
  >img{
    position: absolute;
    top: 10%;
    left: 50%;
    transform: translate(-50%,0);
  }
  .gifImg {
    width: 100%;
    height: 50%;
    position: absolute;
    top: 40%;
    min-height: 50px;
    overflow: hidden;
    text-align: center;
    &:after{
      content: '';
      top: 40%;
      left: 50%;
      transform: translate(-50%, 20%);
      display: block;
      position: absolute;
      width: 36.5%;
      min-width: 700px;
      height: 100%;
      background: url(../img/header.gif) no-repeat center center;
      background-size: 100%;
    }
    .month-tip{
      margin-top: 3%;
      font-size: px2em(24,32);
      color: #fff;
    }
  }
  .title_img {
    text-align: left;
    // margin-top: -1.5%;
    img {
      width: 19.5%;
    }
  }

  h2{
    position: absolute;
    top: 33%;
    font-size: px2em(48, 64);    
  }

  h2 {
    left: 0;
  }
  &:before {
    position: absolute;
    bottom: 18%;
    left: 0;
    width: 100%;
    height: 18%;
    min-height: 18px;
    // background: url("../img/header-line.png") no-repeat;
    // background-size: 100%;

    content: "";
  }
}
/* 主体内容样式 */
.main {
  width: 100%;
  height: 84%;

  .aside-left {
    position: relative;
    width: 22%;
    height: 100%;
    h3{
      padding-top: 6.5%;
      padding-left: 13%;
    }
  }
  .aside-right {
    position: relative;
    width: 22%;
    height: 100%;
  }
}
/* 左侧边框样式 start */
.left-top {
  position: relative;
  opacity: .9;
  background: rgba(#121640,.5);

  &:after {
    position: absolute;
    right: 0;
    bottom: -10.4%;
    width: 5%;
    min-width: 20px;
    height: 13%;
    min-height: 50px;
    background: url("../img/bor1_1_xie.png") no-repeat;
    background-size: 100% 100%;

    content: "";
  }

  &:before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 106%;
    background: url("../img/bor1_1.png") no-repeat;
    background-size: 100% 100%;
    z-index: -1;

    content: "";
  }
}
.left-bottom {
  position: absolute;
  bottom: 0;
  opacity: .9;
  &:before{
    content: '';
    position: absolute;
    top: 5.6%;
    right: -2%;
    z-index: 100;
    width: 15%;
    height: 7%;
    background: url('../img/bor1_2_square.png') no-repeat;
    background-size: 100%;
    z-index: 10;
  }
  &:after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 104%;
    background: url("../img/bor1_2.png") no-repeat;
    background-size: 100% 97%;
    z-index: -1;
    content: "";
  }
}
/* 左侧边框样式 end*/

/* 中间地图样式 start */
.middle-map {
  position: relative;
  width: 55%;
  height: 100%;
  margin: 0 .5%;
  h3 {
    height: 9%;
  }
  .map-wrap {
    position: relative;
    opacity: .9;
    height: 100%;
    border-top: 4px solid #121e52;
    border-bottom: 4px solid #10144b;
    background: rgba(#121640,.5);
    background-size: 100% 100%;
    /*  数字展示牌 start */
    h3 {
      position: absolute;
      top: 4%;
      left: 50%;
      width: 100%;
      padding: 0 3%;
      -webkit-transform: translate(-50%, 0);
              transform: translate(-50%, 0);

      dl {
        width: 20%;
        padding: 0.4% 0.2% 0 0.4%;
        border-left: 2px solid #00bbec;

        dt {
          font-size: px2em(50,64);
          color: #00bbec;
          margin-bottom: 5%;
        }

        dd {
          font-family: DigifaceWide;
          font-size: px2em(80,64);
          color: #fff;
        }
      }
    }
    .title-map{
      position: absolute;
      left: 50%;
      top: 20%;
      transform: translate(-50%,0);
      font-size: px2em(36, 64);
    }
    .unit-wan,
    .unit-wanyuan,
    .unit-number,
    .unit-pc,
    .unit-type{
      &:after {
        display: inline-block;
        margin-left: 4%;
        font-size: 12px;
        color: #fff;
      }      
    }
    .unit-wan{
      &:after{
        content: '万';
      }
    }
    .unit-wanyuan{
      &:after{
        content: '万元';
      }
    }
    .unit-number{
      &:after{
        content: '条';
      }
    }
    .unit-type{
      &:after{
        content: '种';
      }
    }
    .unit-pc{
      &:after{
        content: '批次';
      }
    }
    /*  数字展示牌 end */
    #map {
      height: 100%;
    }
  }
}
/* 中间地图样式 end */

/* 右侧边框样式 start */
.right-top{
  position: relative;
  background: url("../img/bg1_1.png") no-repeat top;

  .con{
    height: 42%;
    margin: 0 5%;
    font-size: px2em(28,64);
    font-size: .4375em;
    background: #081d5d;
  }
}
/* 右侧边框样式 end */

/* 公共部分样式 end*/

