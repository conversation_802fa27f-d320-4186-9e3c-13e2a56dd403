
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">

    <style type="text/css">

        @font-face {font-family: "iconfont";
          src: url('iconfont.eot'); /* IE9*/
          src: url('iconfont.eot#iefix') format('embedded-opentype'), /* IE6-IE8 */
          url('iconfont.woff') format('woff'), /* chrome, firefox */
          url('iconfont.ttf') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
          url('iconfont.svg#iconfont') format('svg'); /* iOS 4.1- */
        }

        .iconfont {
          font-family:"iconfont" !important;
          font-size:16px;
          font-style:normal;
          -webkit-font-smoothing: antialiased;
          -webkit-text-stroke-width: 0.2px;
          -moz-osx-font-smoothing: grayscale;
        }

    </style>
</head>
<body>
    <div class="main markdown">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                <i class="icon iconfont">&#xe613;</i>
                    <div class="name">心</div>
                    <div class="code">&amp;#xe613;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe603;</i>
                    <div class="name">评论</div>
                    <div class="code">&amp;#xe603;</div>
                </li>
            
        </ul>
        <h2 id="unicode-">unicode引用</h2>
        <hr>

        <p>unicode是字体在网页端最原始的应用方式，特点是：</p>
        <ul>
        <li>兼容性最好，支持ie6+，及所有现代浏览器。</li>
        <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
        <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
        </ul>
        <blockquote>
        <p>注意：新版iconfont支持多色图标，这些多色图标在unicode模式下将不能使用，如果有需求建议使用symbol的引用方式</p>
        </blockquote>
        <p>unicode使用步骤如下：</p>
        <h3 id="-font-face">第一步：拷贝项目下面生成的font-face</h3>
        <pre><code class="lang-js hljs javascript">@font-face {
  font-family: <span class="hljs-string">'iconfont'</span>;
  src: url(<span class="hljs-string">'iconfont.eot'</span>);
  src: url(<span class="hljs-string">'iconfont.eot?#iefix'</span>) format(<span class="hljs-string">'embedded-opentype'</span>),
  url(<span class="hljs-string">'iconfont.woff'</span>) format(<span class="hljs-string">'woff'</span>),
  url(<span class="hljs-string">'iconfont.ttf'</span>) format(<span class="hljs-string">'truetype'</span>),
  url(<span class="hljs-string">'iconfont.svg#iconfont'</span>) format(<span class="hljs-string">'svg'</span>);
}
</code></pre>
        <h3 id="-iconfont-">第二步：定义使用iconfont的样式</h3>
        <pre><code class="lang-js hljs javascript">.iconfont{
  font-family:<span class="hljs-string">"iconfont"</span> !important;
  font-size:<span class="hljs-number">16</span>px;font-style:normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: <span class="hljs-number">0.2</span>px;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
        <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
        <pre><code class="lang-js hljs javascript">&lt;i <span class="hljs-class"><span class="hljs-keyword">class</span></span>=<span class="hljs-string">"iconfont"</span>&gt;&amp;#x33;<span class="xml"><span class="hljs-tag">&lt;/<span class="hljs-name">i</span>&gt;</span></span></code></pre>

        <blockquote>
        <p>"iconfont"是你项目下的font-family。可以通过编辑项目查看，默认是"iconfont"。</p>
        </blockquote>
    </div>


</body>
</html>
