"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderFactory = void 0;
const core_1 = require("@nger/core");
const typeorm_1 = require("@nger/typeorm");
const platform_1 = require("../entities/platform");
const spilder_1 = require("./spilder");
const toutiao_spilder_1 = require("./toutiao-spilder");
const weibo_spilder_1 = require("./weibo-spilder");
const zhihu_spilder_1 = require("./zhihu-spilder");
let SpilderFactory = class SpilderFactory {
    injector;
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    map = new Map();
    createing = new Set();
    constructor(injector) {
        this.injector = injector;
    }
    async createSpilder(name, nocache = false) {
        const platform = await this.db.manager.findOne(platform_1.WbPlatform, { where: { name: name } });
        if (!platform) {
            throw new Error(`平台不存在或已删除`);
        }
        switch (name) {
            case 'weibo':
                const weiboSpilder = new weibo_spilder_1.WeiboSpilder(this.injector, platform);
                await weiboSpilder.init();
                if (platform.cookies) {
                    const cookies = platform.cookies.map(c => {
                        c.url = `https://weibo.com/`;
                        return c;
                    });
                    await weiboSpilder.setCookies(cookies);
                }
                return weiboSpilder;
            case 'zhihu':
                const zhihuSpilder = new zhihu_spilder_1.ZhihuSpilder(this.injector, platform);
                await zhihuSpilder.init();
                if (platform.cookies) {
                    const cookies = platform.cookies.map(c => {
                        c.url = `https://www.zhihu.com/`;
                        return c;
                    });
                    await zhihuSpilder.setCookies(cookies);
                }
                return zhihuSpilder;
            case 'toutiao':
                const toutiaoSpilder = new toutiao_spilder_1.ToutiaoSpilder(this.injector, platform);
                // https://www.toutiao.com/
                if (platform.cookies) {
                    const cookies = platform.cookies.map(c => {
                        c.url = `https://www.toutiao.com/`;
                        return c;
                    });
                    await toutiaoSpilder.setCookies(cookies);
                }
                return toutiaoSpilder;
            default:
                throw new spilder_1.SpilderError(-200, `暂不支持${platform.title}`);
        }
    }
    async create(name, nocache = false) {
        if (this.map.has(name)) {
            const spilder = this.map.get(name);
            return spilder;
        }
        const spilder = this.createSpilder(name, nocache);
        this.map.set(name, spilder);
        return spilder;
    }
    async createNoCookie(name, nocoche = false) {
        if (this.map.has(name)) {
            const spilder = this.map.get(name);
            return spilder;
        }
        const spilder = this.createSpilder(name, nocoche);
        this.map.set(name, spilder);
        return spilder;
    }
};
exports.SpilderFactory = SpilderFactory;
exports.SpilderFactory = SpilderFactory = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], SpilderFactory);
