"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderError = exports.Spilder = void 0;
const core_1 = require("@nger/core");
const typeorm_1 = require("@nger/typeorm");
const tokens_1 = require("../tokens");
class Spilder {
    injector;
    platform;
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    _cookies;
    get cookies() {
        if (this._cookies) {
            return this._cookies;
        }
        this._cookies = this.platform.cookies ? this.platform.cookies.map(c => `${c.name}=${c.value}`).join(';') : '';
        return this._cookies;
    }
    set cookies(cookies) {
        this._cookies = cookies;
    }
    delay(time) {
        const d = (0, core_1.defer)();
        setTimeout(() => {
            d.resolve();
        }, time);
        return d;
    }
    constructor(injector, platform) {
        this.injector = injector;
        this.platform = platform;
    }
    page;
    browser;
    get puppeteerPool() {
        return this.injector.get(tokens_1.PUPPETEER_POOL);
    }
    async init() {
        if (!this.page) {
            const browser = await this.puppeteerPool.acquire();
            this.browser = browser;
            this.page = await browser.newPage();
        }
    }
}
exports.Spilder = Spilder;
class SpilderError extends Error {
    code;
    constructor(code, msg) {
        super(msg);
        this.code = code;
    }
}
exports.SpilderError = SpilderError;
