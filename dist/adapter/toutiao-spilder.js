"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToutiaoSpilder = void 0;
const axios_1 = __importDefault(require("axios"));
const spilder_1 = require("./spilder");
class ToutiaoSpilder extends spilder_1.Spilder {
    getRealtimeArticleLinks(keyword) {
        throw new spilder_1.SpilderError(-200, "暂不支持头条");
    }
    loadCommentCount(id, uid) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    loadChildCommentCount(id, uid) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    createTopicArticleLongTextDetail(id) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    createAccount(uid) {
        throw new spilder_1.SpilderError(-200, `暂不支持知乎`);
    }
    loadChildComments(id, uid) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    loadMoreChildComments(id, uid, max_id, total) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    loadMoreComments(id, uid, max_id, total) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    loadComments(id, uid) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    createTopicLinks(link) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    createTopicDetail(link) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    createTopicArticlePageLinks(link) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    createTopicArticleLinks(page) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    createTopicArticleDetail(link) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    createTopicPageLinks(keyword) {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    loginSuccess() {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    checkScanStatus() {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
    async setCookies(cookies) {
        if (this.page) {
            await this.page.setCookie(...cookies);
        }
    }
    async isLogin() {
        const cookies = this.platform.cookies;
        return axios_1.default.get(`https://www.zhihu.com/api/v4/me?include=visits_count%2Cdraft_count`, {
            headers: {
                cookies: this.cookies
            }
        }).then(res => res.data).then(data => {
            return !!data;
        }).catch(e => {
            return false;
        });
    }
    createLoginQrCode() {
        throw new spilder_1.SpilderError(-200, `暂不支持头条`);
    }
}
exports.ToutiaoSpilder = ToutiaoSpilder;
