"use strict";
// import { Injectable } from '@nger/core';
// import Cid from 'cids';
// import multihash, { HashName } from 'multihashing-async';
// @Injectable()
// export class CidService {
//     async create(str: string | Uint8Array, alg: HashName = 'sha1', length?: number) {
//         let bytes = str as Uint8Array;
//         if (typeof str === 'string') {
//             bytes = new TextEncoder().encode(str);
//         }
//         const hash = await multihash(bytes, alg, length)
//         return new Cid(1, 'raw', hash, 'base64')
//     }
// }
