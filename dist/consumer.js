#!/usr/bin/env node
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const dotenv_1 = require("dotenv");
const path_1 = require("path");
(0, dotenv_1.config)({
    path: (0, path_1.join)(__dirname, '../config/product.env')
});
require("reflect-metadata");
process.env.TZ = 'Asia/Shanghai';
const core_1 = require("@nger/core");
const framework_1 = require("./framework");
const task_1 = require("./framework/tasks/task");
const typeorm_1 = require("@nger/typeorm");
const spider_user_1 = require("./entities/spider_user");
const stream_1 = require("stream");
const news_1 = require("./entities/news");
const tokens_1 = require("./tokens");
const message_1 = require("./entities/message");
const amqp_1 = require("./v2/amqp");
const message_2 = require("./v2/message");
const entities_1 = require("@nger/entities");
const crawling_url_1 = require("./entities/crawling_url");
const keyword_1 = require("./entities/keyword");
stream_1.EventEmitter.setMaxListeners(1000);
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, core_1.Module)({
        imports: [
            framework_1.RabbitmqModule.forRoot(),
            framework_1.TasksModule.forRoot(),
            framework_1.RedisModule,
            typeorm_1.TypeormModule.forEnv(),
            typeorm_1.TypeormModule.forFeature([
                spider_user_1.SpiderUser,
                news_1.News,
                message_1.SysMessageEntity,
                entities_1.User,
                crawling_url_1.CrawlingUrl,
                keyword_1.Keyword
            ])
        ],
        providers: [
            ...tokens_1.coreProviders,
            {
                provide: amqp_1.Amqp,
                useFactory: () => {
                    const options = {
                        hostname: process.env['MQ_HOST'] || '***************',
                        port: parseInt(process.env['MQ_PORT'] || '5672'),
                        username: process.env['MQ_USER'] || 'imeepos',
                        password: process.env['MQ_PASS'] || '123qwe',
                        protocol: 'amqp'
                    };
                    return new amqp_1.Amqp(options);
                },
                deps: []
            },
            {
                provide: message_2.SystemMessage,
                useFactory: (injector) => {
                    const db = injector.get(typeorm_1.Db);
                    const amqp = injector.get(amqp_1.Amqp);
                    return new message_2.SystemMessage(amqp, db);
                },
                deps: [core_1.Injector]
            }
        ]
    })
], AppModule);
(0, core_1.platformCore)([{
        provide: core_1.APP_ROOT,
        useValue: process.cwd()
    }]).bootstrap(AppModule)
    .then(async (injector) => {
    const tasks = injector.getMulti(task_1.Task);
    const redis = await injector.get(framework_1.REDIS);
    const db = injector.get(typeorm_1.Db);
    const list = await db.query(`select n.from, platform from sys_news as n`);
    await Promise.all(list.map(async (item) => {
        const _url = new URL(item.from);
        const id = _url.searchParams.get('id');
        await redis.set(`${item.platform}.${id}`, 1);
    }));
    await Promise.all(tasks.map(task => {
        return Promise.all([
            task.consumer().catch(e => { }),
            // task.dead().catch(e=>{})
        ]);
    }));
    console.info('恭喜您主consumer启动成功');
});
