"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BindUserController = void 0;
const core_1 = require("@nger/core");
const rxjs_1 = require("rxjs");
const http_1 = require("@nger/http");
const zhihu_login_1 = require("../services/zhihu.login");
const http_2 = require("@nger/http");
const toutiao_login_1 = require("../services/toutiao.login");
const qq_new_login_1 = require("../services/qq-new.login");
const typeorm_1 = require("@nger/typeorm");
const spider_user_1 = require("../entities/spider_user");
const tokens_1 = require("../tokens");
const redis_1 = require("../services/redis");
let BindUserController = class BindUserController {
    injector;
    get zhihu() {
        return this.injector.get(zhihu_login_1.ZhihuLoginService);
    }
    get toutiao() {
        return this.injector.get(toutiao_login_1.ToutiaoLoginService);
    }
    get qqNew() {
        return this.injector.get(qq_new_login_1.QqNewLoginService);
    }
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    constructor(injector) {
        this.injector = injector;
    }
    async updateOrAddUser(user) {
        const r = this.db.getRepository(spider_user_1.SpiderUser);
        const item = await r.findOneBy({ username: user.username, platform: user.platform });
        if (item) {
            await r.update(item.id, {
                cookies: user.cookies,
                status: user.status,
            });
        }
        else {
            await r.insert({
                cookies: user.cookies,
                status: user.status,
                platform: user.platform,
                username: user.username
            });
        }
        return user;
    }
    async login_qrcode(ctx) {
        const login_state = new rxjs_1.Subject();
        const req = ctx.request;
        const query = req.query;
        const platform = query.get('platform');
        const reqid = Date.now();
        switch (platform) {
            case 'weibo':
                this.open_weibo(`${reqid}`, login_state);
                break;
            case 'zhihu':
                this.zhihu.login(login_state);
                break;
            case 'toutiao':
                this.toutiao.login(login_state);
                break;
            case 'qq-new':
                this.qqNew.login(login_state);
                break;
            case 'wechat':
                break;
            case 'douyin':
                break;
            case 'baidu':
                break;
        }
        return login_state.asObservable();
    }
    delay(time) {
        const d = (0, core_1.defer)();
        setTimeout(() => {
            d.resolve();
        }, time);
        return d;
    }
    get puppeteerPool() {
        return this.injector.get(tokens_1.PUPPETEER_POOL);
    }
    async open_weibo(reqId, login_state) {
        let page = null;
        let browser = null;
        try {
            browser = await this.puppeteerPool.acquire();
            const onResponse = async (r) => {
                const url = r.url();
                if (url.startsWith(`https://v2.qr.weibo.cn/inf/gen`)) {
                    const image = await r.buffer();
                    await (0, redis_1.lock)(reqId, image.toString('base64'));
                    login_state.next({
                        action: 'get_login_qrcode',
                        data: `data:image/png;base64,${image.toString('base64')}`
                    });
                    return;
                }
                if (url.startsWith('https://weibo.com/ajax/side/cards/sideInterested')) {
                    const data = await r.json();
                    if (data.ok === 1) {
                        const uid = data.data.uid;
                        await (0, redis_1.unLock)(reqId);
                        await (0, redis_1.lock)(`uid.${reqId}`, uid);
                    }
                }
                if (url.startsWith(`https://weibo.com/ajax/profile/info`)) {
                    const info = await r.json();
                    if (info.ok === 1) {
                        await (0, redis_1.lock)(`userInfo.${reqId}`, JSON.stringify(info.data.user));
                    }
                }
            };
            browser.on('*', async (...args) => {
                if (args && args.length > 0) {
                    const [type, payload] = args;
                    if (type === 'targetcreated') {
                        const target = payload;
                        const type = target.type();
                        if (type === 'page') {
                            const page = await (target).page();
                            page && page.on('*', (...args) => {
                                if (args && args.length > 0) {
                                    const [type, payload] = args;
                                    if (type === 'response') {
                                        onResponse(payload);
                                    }
                                }
                            });
                        }
                    }
                }
            });
            const url = `https://s.weibo.com`;
            const ctx = browser.defaultBrowserContext();
            await ctx.overridePermissions(url, ['geolocation']);
            page = await browser.newPage();
            page.on('*', (...args) => {
                if (args && args.length > 0) {
                    const [type, payload] = args;
                    if (type === 'response') {
                        onResponse(payload);
                    }
                }
            });
            await page.setJavaScriptEnabled(true);
            await page.setViewport({
                width: 1920,
                height: 768
            });
            await page.goto(url, {
                waitUntil: ['networkidle2']
            });
            await this.delay(20);
            const href1 = page.url();
            login_state.next({
                action: 'open',
                data: href1
            });
            const loginBtn = await page.waitForSelector('div.woo-box-flex.woo-tab-nav > a:nth-child(5)').catch(e => {
                return null;
            });
            if (!loginBtn) {
                login_state.next({
                    action: 'get_login_fail',
                    data: href1
                });
                login_state.complete();
                return;
            }
            const href = page.url();
            login_state.next({
                action: 'get_login_btn',
                data: href
            });
            await loginBtn.click();
            login_state.next({
                action: 'click_login_btn',
                data: href
            });
            await (0, redis_1.waitFor)(reqId);
            console.log(`登陆二维码获取成功`);
            login_state.next({
                action: 'get_scan_qrcode',
                data: href
            });
            // 等待扫码登陆
            const uid = await (0, redis_1.waitFor)(`uid.${reqId}`);
            console.log(`确认扫码成功${uid}`);
            await (0, redis_1.delay)(1000);
            const cookies = await page.cookies();
            login_state.next({
                action: 'login_success',
                data: url,
                cookies
            });
            await page.goto(`https://weibo.com/u/${uid}`, { waitUntil: 'networkidle2' });
            // 获取用户信息
            const userInfo = await (0, redis_1.waitFor)(`userInfo.${reqId}`);
            await (0, redis_1.unLock)(`userInfo.${reqId}`);
            login_state.next({
                action: 'get_userinfo',
                data: {
                    uid,
                    cookies,
                    name: userInfo.screen_name,
                    avatar: userInfo.avatar_hd,
                    gender: userInfo.gender
                }
            });
        }
        catch (e) {
            login_state.next({
                action: 'get_login_fail',
                data: e.message
            });
            login_state.error(e);
        }
        finally {
            login_state.complete();
            if (page) {
                await page.close();
            }
            if (browser) {
                await this.puppeteerPool.destroy(browser);
            }
        }
    }
};
exports.BindUserController = BindUserController;
__decorate([
    (0, http_1.Post)('update_or_add_user'),
    __param(0, (0, http_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], BindUserController.prototype, "updateOrAddUser", null);
__decorate([
    (0, core_1.Get)('login_qrcode'),
    __param(0, (0, core_1.Inject)(http_2.CONTEXT)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], BindUserController.prototype, "login_qrcode", null);
exports.BindUserController = BindUserController = __decorate([
    (0, core_1.Controller)('@nger/weibo/bind-user'),
    __metadata("design:paramtypes", [core_1.Injector])
], BindUserController);
