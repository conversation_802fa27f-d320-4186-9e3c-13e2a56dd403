"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BindUserController = void 0;
const core_1 = require("@nger/core");
const rxjs_1 = require("rxjs");
const http_1 = require("@nger/http");
const zhihu_login_1 = require("../services/zhihu.login");
const http_2 = require("@nger/http");
const toutiao_login_1 = require("../services/toutiao.login");
const qq_new_login_1 = require("../services/qq-new.login");
const typeorm_1 = require("@nger/typeorm");
const spider_user_1 = require("../entities/spider_user");
const tokens_1 = require("../tokens");
let BindUserController = class BindUserController {
    injector;
    get zhihu() {
        return this.injector.get(zhihu_login_1.ZhihuLoginService);
    }
    get toutiao() {
        return this.injector.get(toutiao_login_1.ToutiaoLoginService);
    }
    get qqNew() {
        return this.injector.get(qq_new_login_1.QqNewLoginService);
    }
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    constructor(injector) {
        this.injector = injector;
    }
    async updateOrAddUser(user) {
        const r = this.db.getRepository(spider_user_1.SpiderUser);
        const item = await r.findOneBy({ username: user.username, platform: user.platform });
        if (item) {
            await r.update(item.id, {
                cookies: user.cookies,
                status: user.status,
            });
        }
        else {
            await r.insert({
                cookies: user.cookies,
                status: user.status,
                platform: user.platform,
                username: user.username
            });
        }
        return user;
    }
    async login_qrcode(ctx) {
        const login_state = new rxjs_1.Subject();
        const req = ctx.request;
        const query = req.query;
        const platform = query.get('platform');
        switch (platform) {
            case 'weibo':
                this.open_weibo(login_state);
                break;
            case 'zhihu':
                this.zhihu.login(login_state);
                break;
            case 'toutiao':
                this.toutiao.login(login_state);
                break;
            case 'qq-new':
                this.qqNew.login(login_state);
                break;
            case 'wechat':
                break;
            case 'douyin':
                break;
            case 'baidu':
                break;
        }
        return login_state.asObservable();
    }
    delay(time) {
        const d = (0, core_1.defer)();
        setTimeout(() => {
            d.resolve();
        }, time);
        return d;
    }
    get puppeteerPool() {
        return this.injector.get(tokens_1.PUPPETEER_POOL);
    }
    async open_weibo(login_state) {
        const browser = await this.puppeteerPool.acquire();
        const url = `https://s.weibo.com`;
        const ctx = browser.defaultBrowserContext();
        await ctx.overridePermissions(url, ['geolocation']);
        const page = await browser.newPage();
        await page.setJavaScriptEnabled(true);
        await page.setViewport({
            width: 1920,
            height: 768
        });
        await page.goto(url, {
            waitUntil: ['networkidle2']
        });
        await this.delay(20);
        const href = page.url();
        login_state.next({
            action: 'open',
            data: href
        });
        try {
            const loginCardBtnJpU1 = await page.waitForSelector('.LoginBtn_btn_10QRY').catch(e => {
                return null;
            });
            login_state.next({
                action: 'get_login_btn',
                data: href
            });
            if (loginCardBtnJpU1) {
                await loginCardBtnJpU1.click();
                login_state.next({
                    action: 'click_login_btn',
                    data: href
                });
                await page.waitForNavigation();
                const img = await page.waitForSelector('div.LoginPop_mabox_3Lyr6 > img').catch(e => {
                    return null;
                });
                if (!img) {
                    login_state.next({
                        action: 'get_login_fail',
                        data: href
                    });
                    login_state.complete();
                }
                else {
                    let loginImage = await page.$eval('div.LoginPop_mabox_3Lyr6 > img', (img) => img.src);
                    if (loginImage.startsWith('https://weibo.com/newlogin')) {
                        login_state.next({
                            action: 'get_login_fail',
                            data: loginImage
                        });
                        login_state.complete();
                        return;
                    }
                    login_state.next({
                        action: 'get_login_qrcode',
                        data: loginImage
                    });
                    // 检查是否扫码
                    const tip = await page.waitForSelector('span.woo-tip-text > div.LoginPop_t1_2fuX8', { timeout: 1000 * 60 });
                    if (tip) {
                        const txt = await page.$eval('span.woo-tip-text > div.LoginPop_t1_2fuX8', (div) => {
                            return div.innerText;
                        });
                        login_state.next({
                            action: 'get_scan_qrcode',
                            data: txt
                        });
                        // 扫描成功
                        await page.waitForNavigation();
                        const url = page.url();
                        if (url.startsWith('https://weibo.com/u/')) {
                            // user
                        }
                        // 获取用户信息
                        const cookies = await page.cookies('https://weibo.com/');
                        login_state.next({
                            action: 'login_success',
                            data: url,
                            cookies
                        });
                        const content = await page.content();
                        if (url.startsWith('https://s.weibo.com')) {
                            const re2 = /\$CONFIG\[\'(.*?)\'\]\s*=\s*\'(.*?)\'/g;
                            const user = {};
                            for (let item of content.matchAll(re2)) {
                                const [, key, value] = item;
                                Reflect.set(user, key, value);
                            }
                            login_state.next({
                                action: 'get_userinfo',
                                data: {
                                    uid: user.uid,
                                    cookies,
                                    name: user.nick,
                                    avatar: user.avatar_large,
                                    gender: user.sex
                                }
                            });
                        }
                        else {
                            const re = /window\.\$CONFIG\s*=\s*(.*)\s*;\s*}\s*catch\(e\)/g;
                            const result = re.exec(content);
                            if (result && result.length > 0) {
                                const res = result[1];
                                try {
                                    const CONFIG = JSON.parse(res.trim());
                                    const user = CONFIG?.user;
                                    if (user) {
                                        const uid = user.id;
                                        const name = user.screen_name;
                                        const avatar = user.avatar_large;
                                        login_state.next({
                                            action: 'get_userinfo',
                                            data: {
                                                uid,
                                                cookies,
                                                name,
                                                avatar,
                                            }
                                        });
                                    }
                                }
                                catch (e) { }
                            }
                        }
                    }
                    login_state.complete();
                }
            }
        }
        catch (e) {
            login_state.next({
                action: 'get_login_fail',
                data: e.message
            });
            login_state.error(e);
        }
        finally {
            await page.close();
            await this.puppeteerPool.destroy(browser);
            login_state.complete();
        }
    }
};
exports.BindUserController = BindUserController;
__decorate([
    (0, http_1.Post)('update_or_add_user'),
    __param(0, (0, http_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], BindUserController.prototype, "updateOrAddUser", null);
__decorate([
    (0, core_1.Get)('login_qrcode'),
    __param(0, (0, core_1.Inject)(http_2.CONTEXT)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], BindUserController.prototype, "login_qrcode", null);
exports.BindUserController = BindUserController = __decorate([
    (0, core_1.Controller)('@nger/weibo/bind-user'),
    __metadata("design:paramtypes", [core_1.Injector])
], BindUserController);
