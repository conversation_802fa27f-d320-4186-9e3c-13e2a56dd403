"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CountController = void 0;
const core_1 = require("@nger/core");
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const spilder_topic_article_1 = require("../entities/spilder-topic-article");
let CountController = class CountController {
    injector;
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    get manager() {
        return this.injector.get(rabbitmq_1.TaskManager);
    }
    constructor(injector) {
        this.injector = injector;
    }
    line() {
        const article = this.db.manager.getRepository(spilder_topic_article_1.SpilderTopicArticle);
        const sql = `select count(1) from spilder_topic_article group by eid`;
    }
};
exports.CountController = CountController;
__decorate([
    (0, core_1.Get)('line'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CountController.prototype, "line", null);
exports.CountController = CountController = __decorate([
    (0, core_1.Controller)('@nger/weibo/count'),
    __metadata("design:paramtypes", [core_1.Injector])
], CountController);
