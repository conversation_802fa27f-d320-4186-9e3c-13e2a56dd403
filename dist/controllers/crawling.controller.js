"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrawlingController = void 0;
const core_1 = require("@nger/core");
const error_1 = require("../framework/tasks/error");
const tokens_1 = require("../tokens");
const cheerio_1 = require("cheerio");
const http_1 = require("@nger/http");
const axios_1 = __importDefault(require("axios"));
let CrawlingController = class CrawlingController {
    injector;
    get weiboUserPool() {
        return this.injector.get(tokens_1.WEIBO_USER_POOL);
    }
    get pupperPool() {
        return this.injector.get(tokens_1.PUPPETEER_POOL);
    }
    constructor(injector) {
        this.injector = injector;
    }
    weiboNlp(url, text) { }
    weiboShare(url) {
        return this._weiboShare(url).catch(e => {
            return {
                success: false,
                message: e.message
            };
        });
    }
    async _weiboShare(url) {
        const headers = await this.getHeaders();
        return axios_1.default.get(url, {
            headers: headers
        }).then(res => res.data);
    }
    weiboLike(url) {
        return this._weiboLike(url).catch(e => {
            return {
                success: false,
                message: e.message
            };
        });
    }
    async _weiboLike(url) {
        const headers = await this.getHeaders();
        return axios_1.default.get(url, {
            headers: headers
        }).then(res => res.data);
    }
    async weiboComment(url) {
        return this._weiboComment(url).catch(e => {
            return {
                success: false,
                message: e.message
            };
        });
    }
    async _weiboComment(url) {
        const headers = await this.getHeaders();
        return axios_1.default.get(url, {
            headers: headers
        }).then(r => r.data);
    }
    async weiboChildComment(url) {
        return this._weiboChildComment(url).catch(e => {
            return {
                success: false,
                message: e.message
            };
        });
    }
    async _weiboChildComment(url) {
        const headers = await this.getHeaders();
        return axios_1.default.get(url, {
            headers: headers
        }).then(r => r.data);
    }
    async getHeaders() {
        const user = await this.weiboUserPool.acquire();
        const cookies = user.cookies;
        const headers = {
            cookie: cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; '),
            ['user-agent']: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36'
        };
        this.weiboUserPool.release(user);
        return headers;
    }
    saveArticle(article) {
        return { article };
    }
    async ajaxDetail(url) {
        const user = await this.weiboUserPool.acquire();
        if (user) {
            const cookies = user.cookies;
            const headers = {
                cookie: cookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; '),
                ['user-agent']: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36'
            };
            return axios_1.default.get(url, { headers }).then(res => {
                return { success: true, data: res.data };
            }).catch(e => {
                return {
                    success: false,
                    message: e.message
                };
            });
        }
        return {
            success: false,
            message: 'user logout'
        };
    }
    async getUser() {
        return this._getUser().catch(e => {
            return {
                success: false,
                message: e.message
            };
        });
    }
    async _getUser() {
        const user = await this.weiboUserPool.acquire();
        if (user) {
            this.weiboUserPool.release(user);
            return user;
        }
        throw new Error(`没有可用登陆用户`);
    }
    async weiboListUrl(url) {
        return this._weiboListUrl(url).catch(e => {
            return {
                success: false,
                message: e.message
            };
        });
    }
    async _getBrowser(url) {
        const user = await this.weiboUserPool.acquire();
        if (user) {
            this.weiboUserPool.release(user);
            const browser = await this.pupperPool.acquire();
            if (browser) {
                this.pupperPool.release(browser);
                return browser;
            }
        }
    }
    async _weiboListUrl(url) {
        const user = await this.weiboUserPool.acquire();
        if (user) {
            this.weiboUserPool.release(user);
            const browser = await this.pupperPool.acquire();
            if (browser) {
                this.pupperPool.release(browser);
                const defaultCtx = browser.defaultBrowserContext();
                await defaultCtx.overridePermissions(url, ['geolocation']);
                const page = await browser.newPage();
                await page.setJavaScriptEnabled(true);
                const cookies = user.cookies;
                await page.setCookie(...cookies);
                this.currentUser = user;
                await page.setViewport({
                    width: 1920,
                    height: 768
                });
                await page.goto(url, {
                    waitUntil: ['networkidle2']
                });
                await this.delay(300);
                const href = page.url();
                if (href.startsWith('https://passport')) {
                    await this.weiboUserPool.destroy(user);
                    throw new error_1.UserLogoutError();
                }
                if (href.startsWith('search_need_login')) {
                    await this.weiboUserPool.destroy(user);
                    this.currentUser = undefined;
                    throw new error_1.UserLogoutError();
                }
                const content = await page.content();
                if (content.includes('404 Page not found')) {
                    throw new error_1.Error400();
                }
                if (content.includes('抱歉，未找到')) {
                    throw new error_1.Error400();
                }
                const $ = (0, cheerio_1.load)(content);
                const element = $('.loginBtn');
                if (element.length > 0) {
                    await this.weiboUserPool.destroy(user);
                    this.currentUser = undefined;
                    throw new error_1.UserLogoutError();
                }
                let listUrls = [];
                const moreList = $('ul[node-type="feed_list_page_morelist"] > li');
                moreList.map((index, el) => {
                    const cur = el.attribs['class'] && el.attribs['class'].includes('cur');
                    if (!cur) {
                        const $ = (0, cheerio_1.load)(el);
                        const a = $('a')[0];
                        const href = a.attribs['href'];
                        const url = this.parseUrl(`https://s.weibo.com${href}`);
                        listUrls.push(url);
                    }
                });
                await page.close();
                await this.pupperPool.destroy(browser);
                return listUrls;
            }
        }
        return [];
    }
    async weiboDetailUrl(url) {
        return this._weiboDetailUrl(url).catch(e => {
            return {
                success: false,
                message: e.message
            };
        });
    }
    currentUser;
    async _weiboDetailUrl(url) {
        const user = await this.weiboUserPool.acquire();
        if (user) {
            this.weiboUserPool.release(user);
            const browser = await this.pupperPool.acquire();
            if (browser) {
                this.pupperPool.release(browser);
                const defaultCtx = browser.defaultBrowserContext();
                await defaultCtx.overridePermissions(url, ['geolocation']);
                const page = await browser.newPage();
                await page.setJavaScriptEnabled(true);
                const cookies = user.cookies;
                await page.setCookie(...cookies);
                await page.setViewport({
                    width: 1920,
                    height: 768
                });
                await page.goto(url, {
                    waitUntil: ['networkidle2']
                });
                await this.delay(300);
                const href = page.url();
                if (href.startsWith('https://passport')) {
                    await this.weiboUserPool.destroy(user);
                    throw new error_1.UserLogoutError();
                }
                if (href.startsWith('search_need_login')) {
                    await this.weiboUserPool.destroy(user);
                    throw new error_1.UserLogoutError();
                }
                const content = await page.content();
                if (content.includes('404 Page not found')) {
                    throw new error_1.Error400();
                }
                if (content.includes('抱歉，未找到')) {
                    throw new error_1.Error400();
                }
                const $ = (0, cheerio_1.load)(content);
                const element = $('.loginBtn');
                if (element.length > 0) {
                    await this.weiboUserPool.destroy(user);
                    this.currentUser = undefined;
                    throw new error_1.UserLogoutError();
                }
                // article url
                const links = $('#pl_feedlist_index .card-feed');
                console.log(links.length);
                let urls = [];
                links.map((index, item) => {
                    try {
                        const $ = (0, cheerio_1.load)(item);
                        const linkElement = $('div.content > .from > a');
                        const link = linkElement[0]?.attribs['href'];
                        const url = this.parseUrl(link);
                        url && urls.push(url);
                    }
                    catch (e) {
                        // console.error(e)
                    }
                });
                await page.close();
                await this.pupperPool.destroy(browser);
                return urls;
            }
        }
        return [];
    }
    parseUrl(url) {
        if (!url) {
            return;
        }
        if (url.startsWith('https')) {
            return url;
        }
        else {
            return `https:${url}`;
        }
    }
    delay(n) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                resolve();
            }, n);
        });
    }
};
exports.CrawlingController = CrawlingController;
__decorate([
    (0, http_1.Post)('weiboNlp'),
    __param(0, (0, http_1.Query)('url')),
    __param(1, (0, http_1.Body)('text')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Array]),
    __metadata("design:returntype", void 0)
], CrawlingController.prototype, "weiboNlp", null);
__decorate([
    (0, core_1.Get)('weiboShare'),
    __param(0, (0, http_1.Query)('url')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CrawlingController.prototype, "weiboShare", null);
__decorate([
    (0, core_1.Get)('weiboLike'),
    __param(0, (0, http_1.Query)('url')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CrawlingController.prototype, "weiboLike", null);
__decorate([
    (0, core_1.Get)('weiboComment'),
    __param(0, (0, http_1.Query)('url')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CrawlingController.prototype, "weiboComment", null);
__decorate([
    (0, core_1.Get)('weiboChildComment'),
    __param(0, (0, http_1.Query)('url')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CrawlingController.prototype, "weiboChildComment", null);
__decorate([
    (0, http_1.Post)('saveArticle'),
    __param(0, (0, http_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], CrawlingController.prototype, "saveArticle", null);
__decorate([
    (0, core_1.Get)('weiboDetail'),
    __param(0, (0, http_1.Query)('url')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CrawlingController.prototype, "ajaxDetail", null);
__decorate([
    (0, core_1.Get)('getUser'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CrawlingController.prototype, "getUser", null);
__decorate([
    (0, core_1.Get)('weiboListUrl'),
    __param(0, (0, http_1.Query)('url')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CrawlingController.prototype, "weiboListUrl", null);
__decorate([
    (0, core_1.Get)('weiboDetailUrl'),
    __param(0, (0, http_1.Query)('url')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CrawlingController.prototype, "weiboDetailUrl", null);
exports.CrawlingController = CrawlingController = __decorate([
    (0, core_1.Controller)('@nger/weibo/crawling'),
    __metadata("design:paramtypes", [core_1.Injector])
], CrawlingController);
