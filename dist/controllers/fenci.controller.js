"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FenciController = void 0;
const core_1 = require("@nger/core");
const http_1 = require("@nger/http");
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const count_1 = require("../entities/count");
const spilder_topic_1 = require("../entities/spilder-topic");
const spilder_topic_article_1 = require("../entities/spilder-topic-article");
const spilder_topic_article_comment_1 = require("../entities/spilder-topic-article-comment");
let FenciController = class FenciController {
    injector;
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    get manager() {
        return this.injector.get(rabbitmq_1.TaskManager);
    }
    constructor(injector) {
        this.injector = injector;
    }
    async topic(page, ctx) {
        page = page || 1;
        const psize = 1000;
        const topics = await this.db.manager.find(spilder_topic_1.SpilderTopic, {
            skip: (Number(page) - 1) * psize,
            take: psize
        });
        if (topics.length > 0) {
            await Promise.all(topics.map(topic => {
                if (topic.summary.length > 0) {
                    return this.manager.send({
                        topic: '@nger/weibo/fenci',
                        data: {
                            id: topic.id,
                            type: 'topic',
                            text: topic.summary
                        }
                    });
                }
            }));
            ctx.redirect(`/@nger/weibo/fenci/topic?page=${Number(page) + 1}`);
            return;
        }
        else {
            return {
                code: 0,
                message: '任务完成'
            };
        }
    }
    async article(page, ctx) {
        page = page || 1;
        const psize = 1000;
        const topics = await this.db.manager.find(spilder_topic_article_1.SpilderTopicArticle, {
            skip: (Number(page) - 1) * psize,
            take: psize
        });
        if (topics.length > 0) {
            await Promise.all(topics.map(topic => {
                if (topic.text_raw.length > 0) {
                    let text = topic.text_raw;
                    // let reg = /[(\u4e00-\u9fa5)(\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3010|\u3011|\u007e|,|.|?|!|\"|\`|\')]+/g
                    // const res = text.match(reg)
                    // if (res) text = res[0];
                    if (text.length) {
                        return this.manager.send({
                            topic: '@nger/weibo/fenci',
                            data: {
                                id: topic.id,
                                type: 'article',
                                platform: topic.platform,
                                eid: topic.eid,
                                postAt: topic.create_at,
                                text: text,
                                pid: 0
                            }
                        });
                    }
                }
            }));
            ctx.redirect(`/@nger/weibo/fenci/article?page=${Number(page) + 1}`);
            return;
        }
        else {
            return {
                code: 0,
                message: '操作成功'
            };
        }
    }
    async analysisEvent(eid, dates) {
        eid = Number(eid);
        const times = dates.split('|').map(it => new Date(it));
        const db = this.injector.get(typeorm_1.Db);
        const start = await db.manager.findOne(count_1.CountArticle, { where: { eid }, order: { postAt: 'asc' } });
        const end = await db.manager.findOne(count_1.CountArticle, { where: { eid }, order: { postAt: 'desc' } });
        if (start) {
            times.unshift(start.postAt);
        }
        if (end) {
            times.push(end.postAt);
        }
        const res = await (0, count_1.analysisEvent)(eid)(db, times);
        return {
            eid,
            times,
            data: res
        };
    }
    async comment(page, ctx) {
        page = page || 1;
        const psize = 100;
        const topics = await this.db.manager.find(spilder_topic_article_comment_1.SpilderTopicArticleComment, {
            skip: (Number(page) - 1) * psize,
            take: psize
        });
        if (topics.length > 0) {
            await Promise.all(topics.map(topic => {
                if (topic.text.length > 0) {
                    this.manager.send({
                        topic: '@nger/weibo/fenci',
                        data: {
                            id: topic.id,
                            eid: topic.eid,
                            postAt: topic.created_at,
                            platform: topic.platform,
                            type: 'comment',
                            text: topic.text,
                            pid: topic.pid
                        }
                    });
                }
            }));
            ctx.redirect(`/@nger/weibo/fenci/comment?page=${Number(page) + 1}`);
            return;
        }
        return {
            code: 200,
            message: `操作成功`
        };
    }
};
exports.FenciController = FenciController;
__decorate([
    (0, core_1.Get)('topic'),
    __param(0, (0, http_1.Query)('page')),
    __param(1, (0, core_1.Inject)(core_1.CONTEXT)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FenciController.prototype, "topic", null);
__decorate([
    (0, core_1.Get)('article'),
    __param(0, (0, http_1.Query)('page')),
    __param(1, (0, core_1.Inject)(core_1.CONTEXT)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FenciController.prototype, "article", null);
__decorate([
    (0, core_1.Get)('analysis'),
    __param(0, (0, http_1.Query)('eid')),
    __param(1, (0, http_1.Query)('date')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], FenciController.prototype, "analysisEvent", null);
__decorate([
    (0, core_1.Get)('comment'),
    __param(0, (0, http_1.Query)('page')),
    __param(1, (0, core_1.Inject)(core_1.CONTEXT)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FenciController.prototype, "comment", null);
exports.FenciController = FenciController = __decorate([
    (0, core_1.Controller)('@nger/weibo/fenci'),
    __metadata("design:paramtypes", [core_1.Injector])
], FenciController);
/**
 *
 * 60 * 7 = 420 * = 3000
 *
 * 4-5 700 = 3500
 *
 */
