"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginController = void 0;
const core_1 = require("@nger/core");
const entities_1 = require("@nger/entities");
const http_1 = require("@nger/http");
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const utils_1 = require("@nger/utils");
const spilder_factory_1 = require("../adapter/spilder-factory");
const platform_1 = require("../entities/platform");
const task_1 = require("../entities/task");
const ws_1 = require("ws");
const rxjs_1 = require("rxjs");
const spilder_topic_1 = require("../entities/spilder-topic");
const spilder_topic_article_1 = require("../entities/spilder-topic-article");
const spilder_topic_article_comment_1 = require("../entities/spilder-topic-article-comment");
const event_1 = require("../entities/event");
const spilder_account_1 = require("../entities/spilder-account");
let LoginController = class LoginController {
    injector;
    page;
    get rabbitmqStarter() {
        return this.injector.get(rabbitmq_1.RabbitmqStarter);
    }
    get webSocketServer() {
        return this.injector.get(ws_1.WebSocketServer);
    }
    constructor(injector) {
        this.injector = injector;
    }
    async loginAdmin(username, password) {
        if (!username) {
            return {
                errno: -1,
                message: '用户名不能为空'
            };
        }
        if (!password) {
            return {
                errno: -1,
                message: '密码不能为空'
            };
        }
        if (!this.db) {
            return {
                errno: -1,
                message: '数据库连接失败'
            };
        }
        const old = await this.db.manager.findOne(entities_1.User, { where: { username } });
        if (!old) {
            return {
                errno: -1,
                message: '用户不存在或已删除',
                data: { username }
            };
        }
        const crypto = this.injector.get(utils_1.CryptoService);
        const psd = crypto.md5(password, old.salt);
        if (psd === old.password) {
            return {
                errno: 200,
                message: '恭喜您，登录成功',
                data: {
                    uid: old.uid,
                    username: old.username,
                    email: old.email
                }
            };
        }
        return {
            errno: -1,
            message: '对不起，密码不正确'
        };
    }
    async schedule(eid, time) {
        let task = new task_1.WbTask();
        task.time = time;
        task.ids = [eid];
        task = await this.db.manager.save(task_1.WbTask, task);
        const manager = this.injector.get(rabbitmq_1.TaskManager);
        await manager.send({
            topic: '@nger/weibo/schedule-task',
            data: task
        });
        return {
            errno: 200,
            message: '操作成功'
        };
    }
    async createTask(where) {
        const manager = this.injector.get(rabbitmq_1.TaskManager);
        if (!where.keyword) {
            return {
                code: 0,
                msg: 'please select keyword'
            };
        }
        await manager.send({
            topic: '@nger/weibo/real-time-task',
            data: {
                type: 'weibo',
                eid: where.eid,
                keyword: where.keyword
            }
        }).catch(e => {
            console.error(e.message);
            throw e;
        });
        return {
            code: 200,
            msg: `成功创建1个爬取任务`
        };
    }
    /**
     * create topic page links task
     */
    async topic(where) {
        const manager = this.injector.get(rabbitmq_1.TaskManager);
        const platforms = await this.db.manager.find(platform_1.WbPlatform);
        Promise.all(platforms.map(async (it) => {
            await manager.send({
                topic: '@nger/weibo/topic-page-links-task',
                data: {
                    type: it.name,
                    eid: where.eid,
                    keyword: where.keyword
                }
            }).catch(e => {
                console.error(e.message);
                throw e;
            });
        }));
        return {
            code: 200,
            msg: `成功创建${platforms.length}个爬取任务`
        };
    }
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    get spilderFactory() {
        return this.injector.get(spilder_factory_1.SpilderFactory);
    }
    countInfo() {
        const eventCount$ = () => (0, rxjs_1.from)(this.db.manager.count(event_1.WbEvent)).pipe((0, rxjs_1.startWith)(0));
        const accountCount$ = () => (0, rxjs_1.from)(this.db.manager.count(spilder_account_1.SpilderAccount)).pipe((0, rxjs_1.startWith)(0));
        const topicCount$ = () => (0, rxjs_1.from)(this.db.manager.count(spilder_topic_1.SpilderTopic)).pipe((0, rxjs_1.startWith)(0));
        const topicArticleCount$ = () => (0, rxjs_1.from)(this.db.manager.count(spilder_topic_article_1.SpilderTopicArticle)).pipe((0, rxjs_1.startWith)(0));
        const topicArticleCommentCount$ = () => (0, rxjs_1.from)(this.db.manager.count(spilder_topic_article_comment_1.SpilderTopicArticleComment)).pipe((0, rxjs_1.startWith)(0));
        const interval$ = (0, rxjs_1.interval)(1000);
        return (0, rxjs_1.combineLatest)([
            eventCount$(),
            accountCount$(),
            topicCount$(),
            topicArticleCount$(),
            topicArticleCommentCount$(),
            interval$
        ]).pipe((0, rxjs_1.map)(([event, account, topic, article, comment, time], index) => {
            return {
                event, account, topic, article, comment, time
            };
        }), (0, rxjs_1.catchError)((e, caught) => {
            console.log(e.message);
            return caught;
        }));
    }
};
exports.LoginController = LoginController;
__decorate([
    (0, http_1.Post)('loginAdmin'),
    __param(0, (0, http_1.Body)('username')),
    __param(1, (0, http_1.Body)('password')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], LoginController.prototype, "loginAdmin", null);
__decorate([
    (0, http_1.Post)('schedule'),
    __param(0, (0, http_1.Body)('eid')),
    __param(1, (0, http_1.Body)('time')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], LoginController.prototype, "schedule", null);
__decorate([
    (0, http_1.Post)('topic_v1'),
    __param(0, (0, http_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LoginController.prototype, "createTask", null);
__decorate([
    (0, http_1.Post)('topic'),
    __param(0, (0, http_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LoginController.prototype, "topic", null);
__decorate([
    (0, core_1.Get)('count-info'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], LoginController.prototype, "countInfo", null);
exports.LoginController = LoginController = __decorate([
    (0, core_1.Controller)('@nger/weibo'),
    __metadata("design:paramtypes", [core_1.Injector])
], LoginController);
