"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageController = void 0;
const core_1 = require("@nger/core");
const http_1 = require("@nger/http");
const typeorm_1 = require("@nger/typeorm");
const rxjs_1 = require("rxjs");
const message_1 = require("../entities/message");
const amqp_1 = require("../v2/amqp");
const message_2 = require("../v2/message");
let MessageController = class MessageController {
    db;
    amqp;
    message;
    next = new rxjs_1.Subject();
    constructor(db, amqp) {
        this.db = db;
        this.amqp = amqp;
        this.message = new message_2.SystemMessage(this.amqp, this.db);
    }
    async read(body) {
        const sn = body.sn;
        await this.db.manager.update(message_1.SysMessageEntity, { sn }, { status: 1 });
        this.next.next();
        return {
            body
        };
    }
    mine(query) {
        const uid = query.get('uid');
        return (0, rxjs_1.merge)(this.message.receive(), this.next).pipe((0, rxjs_1.startWith)({}), (0, rxjs_1.switchMap)(() => {
            return this.findAndCount(uid);
        }));
    }
    findAndCount(uid) {
        return new rxjs_1.Observable((sub) => {
            this.db.manager.findAndCount(message_1.SysMessageEntity, {
                select: ['sn', 'data', 'type', 'status', 'send_date', 'from'],
                where: { to: uid, status: 0 },
                order: { send_date: 'desc' },
                take: 10
            }).then(([list, total]) => {
                sub.next({
                    list: list.map(item => {
                        item.data = JSON.parse(item.data);
                        return item;
                    }), total
                });
            }).catch(e => sub.error(e));
        });
    }
};
exports.MessageController = MessageController;
__decorate([
    (0, http_1.Post)('read'),
    __param(0, (0, http_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MessageController.prototype, "read", null);
__decorate([
    (0, core_1.Get)('/mine'),
    __param(0, (0, core_1.Inject)(http_1.QUERY)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [URLSearchParams]),
    __metadata("design:returntype", void 0)
], MessageController.prototype, "mine", null);
exports.MessageController = MessageController = __decorate([
    (0, core_1.Controller)('message'),
    __metadata("design:paramtypes", [typeorm_1.Db, amqp_1.Amqp])
], MessageController);
