"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemInfo = void 0;
const core_1 = require("@nger/core");
const rxjs_1 = require("rxjs");
const node_os_utils_1 = require("node-os-utils");
const os_1 = require("os");
/**
 * [{"name":"WBPSESS","value":"MD8woSgWOYgA6XbWfXpGFoWMnnIEan99X5JgREhUAf0UwZ91RUL7JYNbeIVyU_u3E4GUvhn-p0iK-vlVrUAknFqh6cQCsMAIinPCxpwmyG9lcC-j_MrsDhF40J7QMdrqM1i1cMBoA7dcmbM8J284Mw==","domain":"weibo.com","path":"/","expires":1663813117.094376,"size":159,"httpOnly":true,"secure":true,"session":false,"sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"SCF","value":"Akdfljuy_LX6i-5A4u4EWM0VLLUe0a1u3h5vT6w7FqrCiU8Imd3lYgat4Jc4aphtkGIcm5ZxWI9_Gy9GDxwD-lI.","domain":".weibo.com","path":"/","expires":1979086716.885195,"size":91,"httpOnly":true,"secure":true,"session":false,"sameSite":"None","sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"SUB","value":"_2A25OLgQsDeRhGeFJ7FES9CvJyjWIHXVtWnLkrDV8PUNbmtANLW7xkW9NfyTxuSqhxRPm99bK__-tYJWbv1vHu0XU","domain":".weibo.com","path":"/","expires":-1,"size":93,"httpOnly":true,"secure":true,"session":true,"sameSite":"None","sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"ALF","value":"1695262714","domain":".weibo.com","path":"/","expires":1695262714.885537,"size":13,"httpOnly":false,"secure":true,"session":false,"sameSite":"None","sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"SUBP","value":"0033WrSXqPxfM725Ws9jqgMF55529P9D9Whz.n8cvBpxCSW1JQcNxBGM5JpX5KMhUgL.FoMNS0e0Sh-feK.2dJLoIp7LxKML1KBLBKnLxKqL1hnLBoMNS0M0e0BfSK24","domain":".weibo.com","path":"/","expires":1695262716.885337,"size":132,"httpOnly":false,"secure":true,"session":false,"sameSite":"None","sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"cross_origin_proto","value":"SSL","domain":".weibo.com","path":"/","expires":-1,"size":21,"httpOnly":false,"secure":false,"session":true,"sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"login_sid_t","value":"5df770257eb09808210b4641742d701a","domain":".weibo.com","path":"/","expires":-1,"size":43,"httpOnly":false,"secure":false,"session":true,"sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"XSRF-TOKEN","value":"katIZ2wYraGG4SOnRj0CXe_z","domain":"weibo.com","path":"/","expires":-1,"size":34,"httpOnly":false,"secure":true,"session":true,"sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"SSOLoginState","value":"1663319840","domain":".weibo.com","path":"/","expires":-1,"size":23,"httpOnly":false,"secure":true,"session":true,"sameSite":"None","sameParty":false,"sourceScheme":"Secure","sourcePort":443}]
 */
let SystemInfo = class SystemInfo {
    info() {
        return (0, rxjs_1.interval)(1000).pipe((0, rxjs_1.startWith)(0), (0, rxjs_1.switchMap)(() => {
            const _cpu = (0, rxjs_1.from)(node_os_utils_1.cpu.usage());
            const _mem = (0, rxjs_1.from)(node_os_utils_1.mem.info());
            const _net = (0, rxjs_1.from)(node_os_utils_1.netstat.stats()).pipe((0, rxjs_1.map)(res => {
                if (typeof res === 'string') {
                    return [];
                }
                if (Array.isArray(res)) {
                    return res;
                }
                return [];
            }));
            const _drive = (0, rxjs_1.from)(node_os_utils_1.drive.info('/')).pipe((0, rxjs_1.catchError)((e) => {
                return (0, rxjs_1.of)({ freeGb: 0, freePercentage: 0, totalGb: 0, usedGb: 0, usedPercentage: 0 });
            }));
            return (0, rxjs_1.zip)(_cpu, _mem, _net, _drive);
        }), (0, rxjs_1.map)(([cpu, mem, net, drive]) => {
            const netData = net.reduce((a, b) => {
                return {
                    inputBytes: Number(a.inputBytes) + Number(b.inputBytes),
                    outputBytes: Number(a.outputBytes) + Number(b.outputBytes)
                };
            }, { inputBytes: 0, outputBytes: 0 });
            const driveData = {};
            Object.keys(drive).map(k => {
                Reflect.set(driveData, k, Number(drive[k]));
            });
            return {
                mem,
                platform: (0, os_1.platform)(),
                cpu,
                net: netData,
                drive: driveData
            };
        }), (0, rxjs_1.catchError)((e) => {
            return (0, rxjs_1.of)({});
        }));
    }
};
exports.SystemInfo = SystemInfo;
__decorate([
    (0, core_1.Get)('info'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], SystemInfo.prototype, "info", null);
exports.SystemInfo = SystemInfo = __decorate([
    (0, core_1.Controller)('@nger/system')
], SystemInfo);
