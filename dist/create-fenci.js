#!/usr/bin/env node
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppFenciModule = void 0;
require("reflect-metadata");
process.env.TZ = 'Asia/Shanghai';
const core_1 = require("@nger/core");
const zookeeper_1 = require("@nger/zookeeper");
const typeorm_1 = require("@nger/typeorm");
const utils_1 = require("@nger/utils");
const rabbitmq_1 = require("@nger/rabbitmq");
const weibo_node_module_1 = require("./weibo-node.module");
const spilder_topic_1 = require("./entities/spilder-topic");
let AppFenciModule = class AppFenciModule {
};
exports.AppFenciModule = AppFenciModule;
exports.AppFenciModule = AppFenciModule = __decorate([
    (0, core_1.Module)({
        imports: [
            typeorm_1.TypeormModule.forRoot(),
            zookeeper_1.ZookeeperModule.forRoot(),
            rabbitmq_1.RabbitMqModule.forRoot(),
            utils_1.UtilsModule,
            weibo_node_module_1.WeiboNodeModule,
        ]
    })
], AppFenciModule);
(0, core_1.platformCore)([{
        provide: core_1.APP_ROOT,
        useValue: process.cwd()
    }]).bootstrap(AppFenciModule).then(async (injector) => {
    try {
        const rabbitmqStarter = injector.get(rabbitmq_1.RabbitmqStarter);
        await rabbitmqStarter.start(false);
        const manager = injector.get(rabbitmq_1.TaskManager);
        const db = injector.get(typeorm_1.Db);
        const topics = await db.manager.find(spilder_topic_1.SpilderTopic);
        await Promise.all(topics.map(topic => {
            if (topic.summary.length > 0) {
                manager.send({
                    topic: '@nger/weibo/fenci',
                    data: {
                        id: topic.id,
                        type: 'topic',
                        text: topic.summary
                    }
                });
            }
        }));
        console.log('恭喜您，爬虫节点启动成功');
    }
    catch (e) {
        console.log(e.message);
    }
});
