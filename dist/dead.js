#!/usr/bin/env node
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const dotenv = require('dotenv');
dotenv.config({
    path: '../config/.env'
});
require("reflect-metadata");
process.env.TZ = 'Asia/Shanghai';
const core_1 = require("@nger/core");
const framework_1 = require("./framework");
const task_1 = require("./framework/tasks/task");
const typeorm_1 = require("@nger/typeorm");
const spider_user_1 = require("./entities/spider_user");
const stream_1 = require("stream");
const tokens_1 = require("./tokens");
stream_1.EventEmitter.setMaxListeners(1000);
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, core_1.Module)({
        imports: [
            framework_1.RabbitmqModule.forRoot(),
            framework_1.TasksModule.forRoot(),
            typeorm_1.TypeormModule.forEnv(),
            framework_1.RedisModule,
            typeorm_1.TypeormModule.forFeature([
                spider_user_1.SpiderUser
            ])
        ],
        providers: [
            ...tokens_1.coreProviders
        ]
    })
], AppModule);
(0, core_1.platformCore)([{
        provide: core_1.APP_ROOT,
        useValue: process.cwd()
    }]).bootstrap(AppModule)
    .then(async (injector) => {
    const tasks = injector.getMulti(task_1.Task);
    await Promise.all(tasks.map(task => {
        return Promise.all([
            task.dead().catch(e => { }),
        ]);
    }));
    console.info('恭喜您主dead启动成功');
});
