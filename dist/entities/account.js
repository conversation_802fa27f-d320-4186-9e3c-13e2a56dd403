"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WbAccount = void 0;
const typeorm_1 = require("typeorm");
const rest_1 = require("@nger/rest");
let WbAccount = class WbAccount {
    aid;
    avatar_hd;
    avatar_large;
    cover_image_phone;
    mbrank;
    mbtype;
    pc_new;
    weihao;
    verified;
    verified_reason;
    verified_type;
    verified_type_ext;
    platform;
    name;
    title;
    isv;
    vtitle;
    from;
    type;
    url;
    location;
    followers_count;
    followers_count_str;
    friends_count;
    description;
    gender;
    planet_video;
    profile_image_url;
    profile_url;
    statuses_count;
};
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        hideInAdd: true,
        hideInEdit: true,
        hideInSearch: true,
        sortable: true
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], WbAccount.prototype, "aid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '头像HD',
        hideInSearch: true,
        hideInAdd: true,
        hideInDetail: true,
        hideInEdit: true,
        widget: {
            type: 'avatar'
        }
    }),
    (0, typeorm_1.Column)({
        default: '',
        type: 'varchar'
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "avatar_hd", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '头像',
        hideInTable: true,
        hideInAdd: true,
        hideInEdit: true,
        hideInSearch: true,
        widget: {
            type: 'avatar'
        }
    }),
    (0, typeorm_1.Column)({
        default: '',
        type: 'varchar'
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "avatar_large", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true,
        hideInAdd: true,
        hideInEdit: true,
        hideInSearch: true,
    }),
    (0, typeorm_1.Column)({
        default: '',
        type: 'varchar'
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "cover_image_phone", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true,
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", Number)
], WbAccount.prototype, "mbrank", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true,
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", Number)
], WbAccount.prototype, "mbtype", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true,
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", Number)
], WbAccount.prototype, "pc_new", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '微号'
    }),
    (0, typeorm_1.Column)({
        default: '',
        type: 'varchar'
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "weihao", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '是否认证',
        widget: {
            type: 'boolean'
        }
    }),
    (0, typeorm_1.Column)({
        default: false
    }),
    __metadata("design:type", Boolean)
], WbAccount.prototype, "verified", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '认证原因'
    }),
    (0, typeorm_1.Column)({
        default: '',
        type: 'varchar'
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "verified_reason", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '认证类型',
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", Number)
], WbAccount.prototype, "verified_type", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '认证类型',
        hideInTable: true,
        hideInAdd: true,
        hideInDetail: true,
        hideInEdit: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "verified_type_ext", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台',
        widget: {
            type: 'radio',
            config: {
                options: [{
                        label: '微博',
                        value: 'weibo'
                    }, {
                        label: '知乎',
                        value: 'zhihu'
                    }, {
                        label: '头条',
                        value: 'toutiao'
                    }, {
                        label: '其他',
                        value: 'other'
                    }]
            }
        }
    }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 16,
        default: 'other'
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "platform", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: 'ID',
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "name", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '账号',
    }),
    (0, typeorm_1.Column)({
        default: '',
        type: 'varchar'
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "title", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: `是否认证`,
        widget: {
            type: 'radio',
            config: {
                options: [{
                        label: '已认证',
                        value: '1'
                    }, {
                        label: '未认证',
                        value: '2'
                    }, {
                        label: '未知',
                        value: '3'
                    }]
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: '3'
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "isv", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '认证信息',
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "vtitle", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '所属媒体'
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "from", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '类型',
        span: 12,
        widget: {
            type: 'radio',
            config: {
                options: [{
                        label: '央级主流媒体',
                        value: '1'
                    }, {
                        label: '地方级主流媒体',
                        value: '2'
                    }, {
                        label: '市场化媒体',
                        value: '3'
                    }, {
                        label: '自媒体',
                        value: '4'
                    }]
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: '4'
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "type", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: 'url',
        span: 12
    }),
    (0, typeorm_1.Column)({
        default: '',
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "url", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '地区'
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "location", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '粉丝数',
        hideInAdd: true,
        hideInDetail: true,
        hideInEdit: true,
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], WbAccount.prototype, "followers_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '粉丝数'
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "followers_count_str", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '朋友数'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], WbAccount.prototype, "friends_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '简介'
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "description", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '性别'
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "gender", void 0);
__decorate([
    (0, typeorm_1.Column)({
        default: false
    }),
    __metadata("design:type", Boolean)
], WbAccount.prototype, "planet_video", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInAdd: true,
        hideInDetail: true,
        hideInEdit: true,
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "profile_image_url", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '连接'
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbAccount.prototype, "profile_url", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '数量'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], WbAccount.prototype, "statuses_count", void 0);
WbAccount = __decorate([
    (0, rest_1.Component)({
        title: '媒体平台账号'
    }),
    (0, typeorm_1.Entity)({
        name: 'wb_account'
    })
], WbAccount);
exports.WbAccount = WbAccount;
