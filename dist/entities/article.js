"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WbArticle = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
const event_1 = require("./event");
const spilder_topic_1 = require("./spilder-topic");
let WbArticle = class WbArticle {
    id;
    /**
     * 话题
     */
    tid;
    topic;
    /**
     * 事件
     */
    eid;
    event;
    /**
     * 标签
     */
    tag;
    account;
    accountId;
    aid;
    code;
    content;
    shareCount;
    goodCount;
    commentCount;
    images;
    video;
    from;
    postAt;
    address;
};
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        hideInAdd: true,
        hideInSearch: true,
        sortable: true
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], WbArticle.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '话题',
        path: 'topic.title',
        widget: {
            type: 'selector',
            from: 'wb_topic',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], WbArticle.prototype, "tid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '话题'
    }),
    (0, typeorm_1.ManyToOne)(() => spilder_topic_1.SpilderTopic, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({ name: 'tid' }),
    __metadata("design:type", spilder_topic_1.SpilderTopic)
], WbArticle.prototype, "topic", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件',
        path: 'event.title',
        widget: {
            type: 'selector',
            from: 'wb_event',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], WbArticle.prototype, "eid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件'
    }),
    (0, typeorm_1.ManyToOne)(() => event_1.WbEvent, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'eid'
    }),
    __metadata("design:type", event_1.WbEvent)
], WbArticle.prototype, "event", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '标签',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbArticle.prototype, "tag", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true,
        hideInSearch: true,
        title: '账号'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbArticle.prototype, "account", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true,
        title: '账号ID',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        name: 'account_id',
    }),
    __metadata("design:type", String)
], WbArticle.prototype, "accountId", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '帖子ID',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbArticle.prototype, "aid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '帖子ID',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbArticle.prototype, "code", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '内容',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        type: 'text'
    }),
    __metadata("design:type", String)
], WbArticle.prototype, "content", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '转发数',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        name: 'share_count',
        type: 'bigint'
    }),
    __metadata("design:type", Number)
], WbArticle.prototype, "shareCount", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '点赞数',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        name: 'good_count',
        type: 'bigint'
    }),
    __metadata("design:type", Number)
], WbArticle.prototype, "goodCount", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '评论数',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        name: 'comment_count',
        type: 'bigint'
    }),
    __metadata("design:type", Number)
], WbArticle.prototype, "commentCount", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '图片',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)('simple-array'),
    __metadata("design:type", Array)
], WbArticle.prototype, "images", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '视频',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbArticle.prototype, "video", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '来源'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbArticle.prototype, "from", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '发布时间',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({ name: 'post_at' }),
    __metadata("design:type", String)
], WbArticle.prototype, "postAt", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '发布地'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbArticle.prototype, "address", void 0);
WbArticle = __decorate([
    (0, rest_1.Component)({
        title: '帖子'
    }),
    (0, typeorm_1.Entity)({
        name: 'wb_article'
    })
], WbArticle);
exports.WbArticle = WbArticle;
