"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WbCity = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
/**
 * 全国地区代码表
 */
let WbCity = class WbCity {
    id;
    title;
    code;
    pid;
    parent;
    children;
};
exports.WbCity = WbCity;
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        hideInAdd: true,
        hideInSearch: true,
        sortable: true
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], WbCity.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '城市名称'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbCity.prototype, "title", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '城市代码',
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbCity.prototype, "code", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '上级城市',
        hideInSearch: true,
        widget: {
            type: 'selector',
            from: 'wb_city',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", Number)
], WbCity.prototype, "pid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '上级城市'
    }),
    (0, typeorm_1.TreeParent)(),
    (0, typeorm_1.JoinColumn)({
        name: 'pid',
    }),
    __metadata("design:type", WbCity)
], WbCity.prototype, "parent", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '下级城市'
    }),
    (0, typeorm_1.TreeChildren)(),
    __metadata("design:type", Array)
], WbCity.prototype, "children", void 0);
exports.WbCity = WbCity = __decorate([
    (0, rest_1.Component)({
        title: '地区'
    }),
    (0, typeorm_1.Entity)({
        name: 'wb_city'
    }),
    (0, typeorm_1.Tree)('closure-table')
], WbCity);
