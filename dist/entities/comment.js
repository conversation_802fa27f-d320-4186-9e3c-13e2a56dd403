"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WbComment = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
let WbComment = class WbComment {
    id;
    /**
     * 文章
     */
    aid;
    /**
     * 事件
     */
    eid;
    /**
     * 话题
     */
    tid;
    source;
    created_at;
    content;
    like_counts;
    cid;
    isLikedByMblogAuthor;
    pid;
};
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        hideInAdd: true,
        hideInSearch: true,
        sortable: true
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], WbComment.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '文章'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], WbComment.prototype, "aid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], WbComment.prototype, "eid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '话题'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], WbComment.prototype, "tid", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbComment.prototype, "source", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'created_at'
    }),
    __metadata("design:type", String)
], WbComment.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text'
    }),
    __metadata("design:type", String)
], WbComment.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'like_counts'
    }),
    __metadata("design:type", Number)
], WbComment.prototype, "like_counts", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'bigint'
    }),
    __metadata("design:type", Number)
], WbComment.prototype, "cid", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'is_liked_by_mblog_author'
    }),
    __metadata("design:type", Boolean)
], WbComment.prototype, "isLikedByMblogAuthor", void 0);
__decorate([
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", Number)
], WbComment.prototype, "pid", void 0);
WbComment = __decorate([
    (0, typeorm_1.Entity)({
        name: 'wb_comment'
    })
], WbComment);
exports.WbComment = WbComment;
