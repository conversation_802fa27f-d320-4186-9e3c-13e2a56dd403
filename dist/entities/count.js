"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CountArticleComment = exports.CountArticle = void 0;
exports.analysisEvent = analysisEvent;
const typeorm_1 = require("typeorm");
const rest_1 = require("@nger/rest");
/**
 * n 名词
 * d 副词
 * a 形容词
 * v 动词
 */
/**
 * 核心是帖子
 */
let CountArticle = class CountArticle {
    id;
    postAt;
    eid;
    platform;
    /**
     * 帖子
     */
    aid;
    /**
     * 长度
     */
    l;
    /**
     * 名词
     */
    n;
    nWords;
    /**
     * 副词
     */
    d;
    dWords;
    /**
     * 形容词
     */
    a;
    aWords;
    /**
     * 动词
     */
    v;
    vWords;
    negative;
    negativeWords;
    neutral;
    neutralWords;
    positive;
    positiveWords;
    readability;
    /**
     * 情感强度
     */
    emotion;
};
exports.CountArticle = CountArticle;
__decorate([
    (0, rest_1.Widget)({
        title: '编号'
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], CountArticle.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '发布时间'
    }),
    (0, typeorm_1.Column)({
        name: 'post_at'
    }),
    __metadata("design:type", Date)
], CountArticle.prototype, "postAt", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], CountArticle.prototype, "eid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台'
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], CountArticle.prototype, "platform", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '帖子'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], CountArticle.prototype, "aid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '长度'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticle.prototype, "l", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '名词N'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticle.prototype, "n", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '名词'
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'n_words'
    }),
    __metadata("design:type", Array)
], CountArticle.prototype, "nWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '副词N'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticle.prototype, "d", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '副词'
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'd_words'
    }),
    __metadata("design:type", Array)
], CountArticle.prototype, "dWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '形容词N'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticle.prototype, "a", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '形容词'
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'a_words'
    }),
    __metadata("design:type", Array)
], CountArticle.prototype, "aWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '动词N'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticle.prototype, "v", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '动词'
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'v_words'
    }),
    __metadata("design:type", Array)
], CountArticle.prototype, "vWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticle.prototype, "negative", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'negative_words'
    }),
    __metadata("design:type", Array)
], CountArticle.prototype, "negativeWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticle.prototype, "neutral", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'neutral_words'
    }),
    __metadata("design:type", Array)
], CountArticle.prototype, "neutralWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticle.prototype, "positive", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'positive_words'
    }),
    __metadata("design:type", Array)
], CountArticle.prototype, "positiveWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '可读性'
    }),
    (0, typeorm_1.Column)({
        type: 'float'
    }),
    __metadata("design:type", Number)
], CountArticle.prototype, "readability", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '情感强度'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticle.prototype, "emotion", void 0);
exports.CountArticle = CountArticle = __decorate([
    (0, rest_1.Component)({
        title: '帖子NLP'
    }),
    (0, typeorm_1.Entity)({
        name: 'spilder_count_article'
    })
], CountArticle);
let CountArticleComment = class CountArticleComment {
    id;
    postAt;
    isChild;
    eid;
    platform;
    /**
     * 评论id
     */
    cid;
    /**
     * 长度
     */
    l;
    /**
     * 名词
     */
    n;
    nWords;
    /**
     * 副词
     */
    d;
    dWords;
    /**
     * 形容词
     */
    a;
    aWords;
    /**
     * 动词
     */
    v;
    vWords;
    negative;
    negativeWords;
    neutral;
    neutralWords;
    positive;
    positiveWords;
    readability;
};
exports.CountArticleComment = CountArticleComment;
__decorate([
    (0, rest_1.Widget)({
        title: '编号'
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], CountArticleComment.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '发布时间'
    }),
    (0, typeorm_1.Column)({
        name: 'post_at'
    }),
    __metadata("design:type", Date)
], CountArticleComment.prototype, "postAt", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: 'child'
    }),
    (0, typeorm_1.Column)({
        name: 'is_child'
    }),
    __metadata("design:type", Boolean)
], CountArticleComment.prototype, "isChild", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], CountArticleComment.prototype, "eid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台'
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], CountArticleComment.prototype, "platform", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '评论'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], CountArticleComment.prototype, "cid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '长度'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticleComment.prototype, "l", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '名词N'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticleComment.prototype, "n", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '名词'
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'n_words'
    }),
    __metadata("design:type", Array)
], CountArticleComment.prototype, "nWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '副词N'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticleComment.prototype, "d", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '副词'
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'd_words'
    }),
    __metadata("design:type", Array)
], CountArticleComment.prototype, "dWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '形容词N'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticleComment.prototype, "a", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '形容词'
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'a_words'
    }),
    __metadata("design:type", Array)
], CountArticleComment.prototype, "aWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '动词N'
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticleComment.prototype, "v", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '动词'
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'v_words'
    }),
    __metadata("design:type", Array)
], CountArticleComment.prototype, "vWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticleComment.prototype, "negative", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'negative_words'
    }),
    __metadata("design:type", Array)
], CountArticleComment.prototype, "negativeWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticleComment.prototype, "neutral", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'neutral_words'
    }),
    __metadata("design:type", Array)
], CountArticleComment.prototype, "neutralWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], CountArticleComment.prototype, "positive", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true
    }),
    (0, typeorm_1.Column)('simple-array', {
        name: 'positive_words'
    }),
    __metadata("design:type", Array)
], CountArticleComment.prototype, "positiveWords", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '可读性'
    }),
    (0, typeorm_1.Column)({
        type: 'float'
    }),
    __metadata("design:type", Number)
], CountArticleComment.prototype, "readability", void 0);
exports.CountArticleComment = CountArticleComment = __decorate([
    (0, rest_1.Component)({
        title: '评论NLP'
    }),
    (0, typeorm_1.Entity)({
        name: 'spilder_count_article_comment'
    })
], CountArticleComment);
function analysisEvent(eid) {
    async function getResult(db, dates) {
        const list = [];
        const length = dates.length;
        for (let i = 0; i < dates.length; i++) {
            if (i + 1 < length) {
                list.push([
                    dates[i],
                    dates[i + 1]
                ]);
            }
        }
        /**
         * 文章的总和
         */
        const SA = await Promise.all(list.map(([start, end]) => {
            return getSA(db, start, end);
        }));
        const SAC = await Promise.all(list.map(([start, end]) => {
            return getSAC(db, start, end);
        }));
        const COUNT_A = SA.map(it => count(it));
        const COUNT_C = SAC.map(it => count(it));
        const POS_A = SA.map(it => pos(it));
        const POS_C = SAC.map(it => pos(it));
        const NEG_A = SA.map(it => neg(it));
        const NEG_C = SAC.map(it => neg(it));
        const AVG_LEN_A = SA.map(it => avg_len(it));
        const AVG_LEN_C = SAC.map(it => avg_len(it));
        const CM = SA.filter(it => getCmCount(it));
        return {
            COUNT_A,
            COUNT_C,
            POS_A,
            POS_C,
            NEG_A,
            NEG_C,
            AVG_LEN_A,
            AVG_LEN_C,
            CM
        };
    }
    return getResult;
    function getCmCount(list) {
        return 0;
    }
    function avg_len(list) {
        return 0;
    }
    function neg(list) {
        return 0;
    }
    function count(list) {
        return list.length;
    }
    /**
     * 正向情感总数/总数
     */
    function pos(list) {
        const positive = list.map(it => it.positive).reduce((a, b) => {
            return a + b;
        }, 0);
        return positive / count(list);
    }
    /**
     * 平均长度
     */
    function avgLength(list) {
        const sum = list.map(li => li.l).reduce((a, b) => a + b, 0);
        return sum / count(list);
    }
    async function getSAC(db, start, end) {
        return await db.manager.find(CountArticleComment, {
            where: {
                postAt: (0, typeorm_1.Between)(start, end),
                eid: eid
            }
        });
    }
    async function getSA(db, start, end) {
        return await db.manager.find(CountArticle, {
            where: {
                postAt: (0, typeorm_1.Between)(start, end),
                eid: eid
            }
        });
    }
}
