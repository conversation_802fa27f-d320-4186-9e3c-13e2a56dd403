"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpiderMiddleware = exports.DownloaderMiddleware = exports.Pipeline = exports.Spider = exports.Downloader = exports.Scheduler = exports.Engine = exports.CrawlingTask = void 0;
class CrawlingTask {
    id;
    // 类型 search position
    type;
    // 关键字
    condiction;
    // 平台
    platform;
    // 开始时间
    start;
    // 结束时间
    end;
    // 步长
    step;
    // 定时
    schedule;
}
exports.CrawlingTask = CrawlingTask;
// 引擎负责控制数据流在系统中所有组件中流动, 并在相应动作发生时触发事件
class Engine {
    spiders = [];
    start() {
        this.spiders.map(spider => spider.start());
    }
    addUrl(url) { }
}
exports.Engine = Engine;
// 调度器从引擎接受request并将他们入队, 以便之后引擎请求他们时提供给引擎
class Scheduler {
    addRequest(req) { }
}
exports.Scheduler = Scheduler;
// 下载器负责获取页面数据并提供给引擎，而后提供给spider
class Downloader {
}
exports.Downloader = Downloader;
class Spider {
    start() {
        // 找到URL地址
    }
}
exports.Spider = Spider;
// 典型的处理有清理、 验证及持久化
class Pipeline {
}
exports.Pipeline = Pipeline;
// 下载器中间件是在引擎及下载器之间的特定钩子
class DownloaderMiddleware {
}
exports.DownloaderMiddleware = DownloaderMiddleware;
// Spider中间件是在引擎及Spider之间的特定钩子
class SpiderMiddleware {
}
exports.SpiderMiddleware = SpiderMiddleware;
