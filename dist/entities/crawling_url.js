"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrawlingUrl = void 0;
const typeorm_1 = require("typeorm");
const keyword_1 = require("./keyword");
let CrawlingUrl = class CrawlingUrl {
    id;
    link;
    type;
    keyword;
    keyword_id;
    create_date;
    update_date;
    procress;
};
exports.CrawlingUrl = CrawlingUrl;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], CrawlingUrl.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        unique: true,
        type: 'varchar',
        width: 255
    }),
    __metadata("design:type", String)
], CrawlingUrl.prototype, "link", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        width: 8
    }),
    __metadata("design:type", String)
], CrawlingUrl.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => keyword_1.Keyword, k => k.id, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'keyword_id'
    }),
    __metadata("design:type", keyword_1.Keyword)
], CrawlingUrl.prototype, "keyword", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'keyword_id',
        type: 'int'
    }),
    __metadata("design:type", Number)
], CrawlingUrl.prototype, "keyword_id", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CrawlingUrl.prototype, "create_date", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CrawlingUrl.prototype, "update_date", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'int4',
        default: 0
    }),
    __metadata("design:type", Number)
], CrawlingUrl.prototype, "procress", void 0);
exports.CrawlingUrl = CrawlingUrl = __decorate([
    (0, typeorm_1.Entity)({
        name: 'crawling_url'
    })
], CrawlingUrl);
