"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WbEventType = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
let WbEventType = class WbEventType {
    code;
    title;
};
exports.WbEventType = WbEventType;
__decorate([
    (0, rest_1.Widget)({
        title: '性质编号',
        hideInSearch: true,
    }),
    (0, typeorm_1.PrimaryColumn)(),
    __metadata("design:type", String)
], WbEventType.prototype, "code", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '性质名称'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbEventType.prototype, "title", void 0);
exports.WbEventType = WbEventType = __decorate([
    (0, rest_1.Component)({
        title: '事件性质'
    }),
    (0, typeorm_1.Entity)({
        name: 'wb_event_type'
    })
], WbEventType);
