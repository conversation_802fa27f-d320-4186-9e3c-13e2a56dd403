"use strict";
/**
 * 事件
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WbEvent = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
const city_1 = require("./city");
const event_category_1 = require("./event-category");
const event_type_1 = require("./event-type");
const spilder_topic_1 = require("./spilder-topic");
const spilder_topic_article_1 = require("./spilder-topic-article");
const spilder_topic_article_comment_1 = require("./spilder-topic-article-comment");
let WbEvent = class WbEvent {
    id;
    title;
    address;
    city;
    type;
    typeEntity;
    category;
    categoryEntity;
    desc;
    topics;
    articles;
    comments;
    keywords;
};
exports.WbEvent = WbEvent;
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        hideInAdd: true,
        hideInSearch: true,
        sortable: true,
        hideInEdit: true
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], WbEvent.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件名称'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbEvent.prototype, "title", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '地域',
        hideInSearch: true,
        path: 'city.title',
        widget: {
            type: 'selector',
            from: 'wb_city',
            config: {
                label: 'title',
                value: 'code'
            }
        }
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbEvent.prototype, "address", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '地域'
    }),
    (0, typeorm_1.ManyToOne)(() => city_1.WbCity, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'address',
        referencedColumnName: 'code'
    }),
    __metadata("design:type", city_1.WbCity)
], WbEvent.prototype, "city", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件性质',
        path: 'typeEntity.title',
        widget: {
            type: 'selector',
            from: 'wb_event_type',
            config: {
                value: 'code',
                label: 'title'
            }
        }
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], WbEvent.prototype, "type", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件性质'
    }),
    (0, typeorm_1.ManyToOne)(() => event_type_1.WbEventType, {
        createForeignKeyConstraints: true
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'type',
        referencedColumnName: 'code'
    }),
    __metadata("design:type", event_type_1.WbEventType)
], WbEvent.prototype, "typeEntity", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '行业类型',
        path: 'categoryEntity.title',
        widget: {
            type: 'selector',
            from: 'wb_event_category',
            config: {
                value: 'code',
                label: 'title'
            }
        }
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], WbEvent.prototype, "category", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '行业类型'
    }),
    (0, typeorm_1.ManyToOne)(() => event_category_1.WbEventCategory, {
        createForeignKeyConstraints: true
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'category',
        referencedColumnName: 'code'
    }),
    __metadata("design:type", event_category_1.WbEventCategory)
], WbEvent.prototype, "categoryEntity", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '备注',
        hideInSearch: true,
        hideInTable: true,
        required: false
    }),
    (0, typeorm_1.Column)({
        default: '',
    }),
    __metadata("design:type", String)
], WbEvent.prototype, "desc", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '话题',
        widget: {
            type: 'array',
            where: {
                eid: 'id'
            }
        }
    }),
    (0, typeorm_1.OneToMany)(() => spilder_topic_1.SpilderTopic, it => it.eid, {
        createForeignKeyConstraints: false
    }),
    __metadata("design:type", Array)
], WbEvent.prototype, "topics", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '帖子',
        widget: {
            type: 'array',
            where: {
                eid: 'id'
            }
        }
    }),
    (0, typeorm_1.OneToMany)(() => spilder_topic_article_1.SpilderTopicArticle, it => it.eid, {
        createForeignKeyConstraints: false
    }),
    __metadata("design:type", Array)
], WbEvent.prototype, "articles", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '评论',
        widget: {
            type: 'array',
            where: {
                eid: 'id'
            }
        }
    }),
    (0, typeorm_1.OneToMany)(() => spilder_topic_article_comment_1.SpilderTopicArticleComment, it => it.eid, {
        createForeignKeyConstraints: false
    }),
    __metadata("design:type", Array)
], WbEvent.prototype, "comments", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '关键字',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)('simple-array', {
        nullable: true
    }),
    __metadata("design:type", Array)
], WbEvent.prototype, "keywords", void 0);
exports.WbEvent = WbEvent = __decorate([
    (0, rest_1.Component)({
        title: '事件',
        table: {
            loginWeibo: {
                title: '登录',
                icon: 'icon-denglu1',
                type: 'dropdown',
                handler: async (ctx, next, injector) => {
                    // 搜索关键字
                    if (next)
                        await next();
                }
            },
            searchZhihu: {
                title: '知乎',
                icon: 'icon-shejiaotubiao-46',
                type: 'zhihu',
                handler: async (ctx, next, injector) => {
                    // 搜索关键字
                    if (next)
                        await next();
                }
            },
            searchToutiao: {
                title: '头条',
                icon: 'icon-toutiaoyangshi',
                type: 'toutiao',
                handler: async (ctx, next, injector) => {
                    // 搜索关键字
                    if (next)
                        await next();
                }
            }
        }
    }),
    (0, typeorm_1.Entity)({
        name: 'wb_event'
    })
], WbEvent);
