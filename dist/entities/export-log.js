"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderExportLog = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
let SpilderExportLog = class SpilderExportLog {
    id;
    username;
    uid;
    exportDate;
    eid;
    event_title;
};
exports.SpilderExportLog = SpilderExportLog;
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        sortable: true
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SpilderExportLog.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '用户名',
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SpilderExportLog.prototype, "username", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '用户ID',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], SpilderExportLog.prototype, "uid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '导出日期',
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true,
    }),
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], SpilderExportLog.prototype, "exportDate", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件ID',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], SpilderExportLog.prototype, "eid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件名称'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SpilderExportLog.prototype, "event_title", void 0);
exports.SpilderExportLog = SpilderExportLog = __decorate([
    (0, rest_1.Component)({
        title: '导出日志',
        hideAdd: true,
        hideDelete: true,
        hideEdit: true
    }),
    (0, typeorm_1.Entity)({
        name: 'spilder_export_log'
    })
], SpilderExportLog);
