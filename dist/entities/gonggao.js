"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Gonggao = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
const event_1 = require("./event");
let Gonggao = class Gonggao {
    id;
    author;
    content;
    post_at;
    eid;
    event;
    yuqi;
    taidu;
    qiangdu;
    fangshi;
    fengge;
    reason;
};
exports.Gonggao = Gonggao;
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        sortable: true,
        hideInAdd: true,
        hideInEdit: true,
        hideInSearch: true,
        widget: {
            type: 'number'
        }
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Gonggao.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '发布者',
        hideInSearch: true,
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], Gonggao.prototype, "author", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '公告内容',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        type: 'text'
    }),
    __metadata("design:type", String)
], Gonggao.prototype, "content", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '发布时间',
        sortable: true,
        widget: {
            type: 'date',
            config: {
                showTime: true
            }
        }
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", Date)
], Gonggao.prototype, "post_at", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '所属事件',
        path: 'event.title',
        widget: {
            type: 'selector',
            from: 'wb_event',
            config: {
                value: 'id',
                label: 'title'
            }
        }
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], Gonggao.prototype, "eid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '所属事件'
    }),
    (0, typeorm_1.ManyToOne)(() => event_1.WbEvent, {
        createForeignKeyConstraints: true
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'eid',
        referencedColumnName: 'id'
    }),
    __metadata("design:type", event_1.WbEvent)
], Gonggao.prototype, "event", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '回应语气',
        hideInSearch: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                        label: '陈述',
                        value: 'A1'
                    }, {
                        label: '疑问',
                        value: 'A2'
                    }, {
                        label: '感叹',
                        value: 'A3'
                    }, {
                        label: '祈祷',
                        value: 'A4'
                    }]
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], Gonggao.prototype, "yuqi", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '回应态度',
        hideInSearch: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                        label: '关心',
                        value: 'B1'
                    }, {
                        label: '追责',
                        value: 'B2'
                    }, {
                        label: '称赞',
                        value: 'B3'
                    }, {
                        label: '补救',
                        value: 'B4'
                    }, {
                        label: '完全否认',
                        value: 'B5'
                    }, {
                        label: '部分否认',
                        value: 'B6'
                    }, {
                        label: '解释',
                        value: 'B7'
                    }, {
                        label: '承诺',
                        value: 'B8'
                    }, {
                        label: '转移',
                        value: 'B9'
                    }]
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], Gonggao.prototype, "taidu", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '情感强度',
        hideInSearch: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                        label: '客观',
                        value: 0
                    }, {
                        label: '较弱',
                        value: 1
                    }, {
                        label: '弱',
                        value: 2
                    }, {
                        label: '强',
                        value: 3
                    }, {
                        label: '级强',
                        value: 4
                    }]
            }
        }
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Gonggao.prototype, "qiangdu", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '回应方式',
        hideInSearch: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                        label: '响应型',
                        value: 'D1'
                    }, {
                        label: '处置弱型',
                        value: 'D2'
                    }, {
                        label: '处置强型',
                        value: 'D3'
                    }, {
                        label: '长期行动型',
                        value: 'D4'
                    }, {
                        label: '政策型',
                        value: 'D5'
                    }]
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], Gonggao.prototype, "fangshi", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '公告风格',
        hideInSearch: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                        label: '真诚亲民',
                        value: 'C1'
                    }, {
                        label: '诙谐幽默',
                        value: 'C2'
                    }, {
                        label: '客观真实',
                        value: 'C3'
                    }, {
                        label: '古板僵化',
                        value: 'C4'
                    }, {
                        label: '避重就轻或避硬就软',
                        value: 'C5'
                    }, {
                        label: '措辞不当',
                        value: 'C6'
                    }, {
                        label: '说教口吻或居高临下',
                        value: 'C7'
                    }]
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], Gonggao.prototype, "fengge", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '判断依据',
        hideInSearch: true,
        hideInTable: true,
        widget: {
            type: 'string'
        }
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], Gonggao.prototype, "reason", void 0);
exports.Gonggao = Gonggao = __decorate([
    (0, rest_1.Component)({
        title: '政府公告'
    }),
    (0, typeorm_1.Entity)({
        name: 'spider_gonggao'
    })
], Gonggao);
