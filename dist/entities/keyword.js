"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Keyword = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
let Keyword = class Keyword {
    id;
    value;
    status;
    create_date;
};
exports.Keyword = Keyword;
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        sortable: true,
        hideInAdd: true,
        hideInEdit: true,
        widget: {
            type: 'number'
        }
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Keyword.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '关键字',
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Keyword.prototype, "value", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '关键字状态',
        widget: {
            type: 'radio',
            config: {
                options: [{
                        label: '禁用',
                        value: -1
                    }, {
                        label: '启用',
                        value: 1
                    }]
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], Keyword.prototype, "status", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '创建日期',
        hideInAdd: true,
        hideInEdit: true,
        hideInSearch: true
    }),
    (0, typeorm_1.CreateDateColumn)({
        name: 'create_date'
    }),
    __metadata("design:type", Date)
], Keyword.prototype, "create_date", void 0);
exports.Keyword = Keyword = __decorate([
    (0, rest_1.Component)({
        title: '关键字'
    }),
    (0, typeorm_1.Entity)({
        name: 'spider_keyword'
    })
], Keyword);
