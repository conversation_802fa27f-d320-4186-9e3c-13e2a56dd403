"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.News = void 0;
const typeorm_1 = require("typeorm");
let News = class News {
    /**
     * 编号
     */
    id;
    /**
     * 平台
     */
    platform;
    /**
     * 类型
     */
    type;
    /**
     * 内容
     */
    data;
    /**
     * 点赞
     */
    like;
    /**
     * 分享
     */
    share;
    /**
     * 评论
     */
    comment;
    /**
     * 用户
     */
    user;
    /**
     * 爬取方式
     */
    from;
    aid;
    uid;
    post_at;
    // nlp
    readablity;
    word_num;
    cut;
    // 关键字
    keywords;
    // 情感强度
    negative_probs;
    positive_probs;
    sentiment_key;
    sentiment_label;
    comment_cut;
    comment_emotion;
    comment_readablity_avg;
    comment_positive_avg;
    comment_negative_avg;
    comment_negative_count;
    comment_word_num_avg;
    comment_positive_count;
    share_cut;
    share_emotion;
    share_readablity_avg;
    share_positive_avg;
    share_negative_avg;
    share_negative_count;
    share_positive_count;
    share_word_num_avg;
    create_date;
    update_date;
};
exports.News = News;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], News.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        width: 16
    }),
    __metadata("design:type", String)
], News.prototype, "platform", void 0);
__decorate([
    (0, typeorm_1.Column)({
        width: 16
    }),
    __metadata("design:type", String)
], News.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb'
    }),
    __metadata("design:type", Object)
], News.prototype, "data", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb'
    }),
    __metadata("design:type", Object)
], News.prototype, "like", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb'
    }),
    __metadata("design:type", Object)
], News.prototype, "share", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb'
    }),
    __metadata("design:type", Object)
], News.prototype, "comment", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb'
    }),
    __metadata("design:type", Object)
], News.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({
        width: 320
    }),
    __metadata("design:type", String)
], News.prototype, "from", void 0);
__decorate([
    (0, typeorm_1.Column)({
        width: 16
    }),
    __metadata("design:type", String)
], News.prototype, "aid", void 0);
__decorate([
    (0, typeorm_1.Column)({
        width: 12
    }),
    __metadata("design:type", String)
], News.prototype, "uid", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Date)
], News.prototype, "post_at", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "readablity", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "word_num", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb'
    }),
    __metadata("design:type", Object)
], News.prototype, "cut", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 120
    }),
    __metadata("design:type", String)
], News.prototype, "keywords", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "negative_probs", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "positive_probs", void 0);
__decorate([
    (0, typeorm_1.Column)({
        width: 12
    }),
    __metadata("design:type", String)
], News.prototype, "sentiment_key", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "sentiment_label", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb',
        default: []
    }),
    __metadata("design:type", Object)
], News.prototype, "comment_cut", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb',
        default: []
    }),
    __metadata("design:type", Object)
], News.prototype, "comment_emotion", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "comment_readablity_avg", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "comment_positive_avg", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "comment_negative_avg", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "comment_negative_count", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "comment_word_num_avg", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "comment_positive_count", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb',
        default: []
    }),
    __metadata("design:type", Object)
], News.prototype, "share_cut", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb',
        default: []
    }),
    __metadata("design:type", Object)
], News.prototype, "share_emotion", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "share_readablity_avg", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "share_positive_avg", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "share_negative_avg", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "share_negative_count", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "share_positive_count", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], News.prototype, "share_word_num_avg", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], News.prototype, "create_date", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], News.prototype, "update_date", void 0);
exports.News = News = __decorate([
    (0, typeorm_1.Entity)({
        name: 'sys_news'
    }),
    (0, typeorm_1.Unique)('UK_news_platform_aid', ['platform', 'aid'])
], News);
