"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WbPlatform = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
let WbPlatform = class WbPlatform {
    id;
    title;
    name;
    icon;
    loginUrl;
    uid;
    nickname;
    status;
    cookies;
};
exports.WbPlatform = WbPlatform;
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        sortable: true,
        hideInAdd: true,
        hideInEdit: true,
        widget: {
            type: 'number'
        }
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], WbPlatform.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台名称',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbPlatform.prototype, "title", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '代号'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbPlatform.prototype, "name", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '图标',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbPlatform.prototype, "icon", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '登录地址',
        hideInAdd: true,
        hideInDetail: true,
        hideInEdit: true,
        hideInSearch: true,
        hideInTable: true,
        required: false
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbPlatform.prototype, "loginUrl", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '绑定用户',
        required: false,
        hideInAdd: true,
        hideInSearch: true,
        hideInEdit: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbPlatform.prototype, "uid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '用户昵称',
        required: false,
        hideInAdd: true,
        hideInSearch: true,
        hideInEdit: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], WbPlatform.prototype, "nickname", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '是否有效',
        required: false,
        hideInAdd: true,
        hideInSearch: true,
        hideInEdit: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], WbPlatform.prototype, "status", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '登录凭证',
        hideInTable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInDetail: true,
        hideInEdit: true
    }),
    (0, typeorm_1.Column)('simple-json', {
        nullable: true
    }),
    __metadata("design:type", Array)
], WbPlatform.prototype, "cookies", void 0);
exports.WbPlatform = WbPlatform = __decorate([
    (0, rest_1.Component)({
        title: '平台'
    }),
    (0, typeorm_1.Entity)({
        name: 'wb_platform'
    }),
    (0, typeorm_1.Unique)(['name'])
], WbPlatform);
