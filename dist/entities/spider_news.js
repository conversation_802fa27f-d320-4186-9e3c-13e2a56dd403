"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpiderNews = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
let SpiderNews = class SpiderNews {
    nid;
    // 平台
    platform;
    // 链接 唯一
    url;
    // 更新时间
    updated_time;
    // 创建时间
    created_time;
    // 分享
    share_count;
    share = [];
    // 赞同
    voteup_count;
    voteup = [];
    // 评论
    comment_count;
    comment = [];
    // 文章id
    id;
    // 标题
    title;
    // 内容
    content;
    // 简介
    excerpt;
    // 图片
    thumbnails;
    // 作者唯一标识
    author;
    // 文章类型
    article_type = 'article';
    // 问题
    question_id;
    keywords;
    extend;
    // 情感值
    emotion;
    // 可读性
    readablity;
    // 话题
    topic;
    // 爬取时间
    spider_create_time;
    // 最后一次爬取更新时间
    spider_update_time;
};
exports.SpiderNews = SpiderNews;
__decorate([
    (0, rest_1.Widget)({
        hideInAdd: true,
        hideInEdit: true,
        sortable: true,
        title: '序号'
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SpiderNews.prototype, "nid", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SpiderNews.prototype, "platform", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        unique: true
    }),
    __metadata("design:type", String)
], SpiderNews.prototype, "url", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], SpiderNews.prototype, "updated_time", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    __metadata("design:type", Number)
], SpiderNews.prototype, "created_time", void 0);
__decorate([
    (0, typeorm_1.Column)({
        default: 0
    }),
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    __metadata("design:type", Number)
], SpiderNews.prototype, "share_count", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb'
    }),
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    __metadata("design:type", Array)
], SpiderNews.prototype, "share", void 0);
__decorate([
    (0, typeorm_1.Column)({
        default: 0
    }),
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    __metadata("design:type", Number)
], SpiderNews.prototype, "voteup_count", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb'
    }),
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    __metadata("design:type", Array)
], SpiderNews.prototype, "voteup", void 0);
__decorate([
    (0, typeorm_1.Column)({
        default: 0
    }),
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    __metadata("design:type", Number)
], SpiderNews.prototype, "comment_count", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb'
    }),
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    __metadata("design:type", Array)
], SpiderNews.prototype, "comment", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SpiderNews.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], SpiderNews.prototype, "title", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], SpiderNews.prototype, "content", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], SpiderNews.prototype, "excerpt", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)('simple-array'),
    __metadata("design:type", Array)
], SpiderNews.prototype, "thumbnails", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], SpiderNews.prototype, "author", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SpiderNews.prototype, "article_type", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], SpiderNews.prototype, "question_id", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)('simple-array'),
    __metadata("design:type", Array)
], SpiderNews.prototype, "keywords", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)('simple-json'),
    __metadata("design:type", Object)
], SpiderNews.prototype, "extend", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpiderNews.prototype, "emotion", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpiderNews.prototype, "readablity", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], SpiderNews.prototype, "topic", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], SpiderNews.prototype, "spider_create_time", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInSearch: true
    }),
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], SpiderNews.prototype, "spider_update_time", void 0);
exports.SpiderNews = SpiderNews = __decorate([
    (0, rest_1.Component)({
        title: '新闻管理',
        hideAdd: true
    }),
    (0, typeorm_1.Entity)({
        name: 'spider_news'
    })
], SpiderNews);
