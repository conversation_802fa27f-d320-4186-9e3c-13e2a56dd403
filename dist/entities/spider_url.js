"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpiderUrl = void 0;
const typeorm_1 = require("typeorm");
let SpiderUrl = class SpiderUrl {
    id;
    url;
    account_url;
    platform;
    type;
    keyword;
    status;
    create_date;
};
exports.SpiderUrl = SpiderUrl;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SpiderUrl.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SpiderUrl.prototype, "url", void 0);
__decorate([
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], SpiderUrl.prototype, "account_url", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    (0, typeorm_1.Index)('spider_url_platform'),
    __metadata("design:type", String)
], SpiderUrl.prototype, "platform", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    (0, typeorm_1.Index)('spider_url_type'),
    __metadata("design:type", String)
], SpiderUrl.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        nullable: true
    }),
    (0, typeorm_1.Index)('spider_url_keyword'),
    __metadata("design:type", String)
], SpiderUrl.prototype, "keyword", void 0);
__decorate([
    (0, typeorm_1.Column)({
        default: 'waiting'
    }),
    (0, typeorm_1.Index)('spider_url_status'),
    __metadata("design:type", String)
], SpiderUrl.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'create_date'
    }),
    __metadata("design:type", Date)
], SpiderUrl.prototype, "create_date", void 0);
exports.SpiderUrl = SpiderUrl = __decorate([
    (0, typeorm_1.Entity)({
        name: 'spider_url'
    }),
    (0, typeorm_1.Unique)(['url', 'platform'])
], SpiderUrl);
