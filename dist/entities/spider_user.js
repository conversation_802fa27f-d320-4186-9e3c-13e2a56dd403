"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpiderUser = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
let SpiderUser = class SpiderUser {
    id;
    platform;
    username;
    status;
    cookies;
    create_date;
    update_date;
};
exports.SpiderUser = SpiderUser;
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        sortable: true,
        hideInAdd: true,
        hideInEdit: true,
        widget: {
            type: 'number'
        }
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SpiderUser.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SpiderUser.prototype, "platform", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '用户名'
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpiderUser.prototype, "username", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '状态'
    }),
    (0, typeorm_1.Column)({
        default: 1
    }),
    __metadata("design:type", Number)
], SpiderUser.prototype, "status", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '登录凭证',
        hideInEdit: true,
        hideInAdd: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)('simple-json', {
        nullable: true
    }),
    __metadata("design:type", Object)
], SpiderUser.prototype, "cookies", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '创建时间',
        hideInEdit: true,
        hideInAdd: true,
        hideInSearch: true
    }),
    (0, typeorm_1.CreateDateColumn)({
        name: 'create_date'
    }),
    __metadata("design:type", Date)
], SpiderUser.prototype, "create_date", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '创建时间',
        hideInEdit: true,
        hideInAdd: true,
        hideInSearch: true
    }),
    (0, typeorm_1.UpdateDateColumn)({
        name: 'update_date'
    }),
    __metadata("design:type", Date)
], SpiderUser.prototype, "update_date", void 0);
exports.SpiderUser = SpiderUser = __decorate([
    (0, typeorm_1.Entity)({
        name: 'spider_user'
    }),
    (0, typeorm_1.Unique)(['platform', 'username'])
], SpiderUser);
