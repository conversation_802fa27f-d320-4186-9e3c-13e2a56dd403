"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderAccount = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
const spilder_topic_1 = require("./spilder-topic");
const spilder_topic_article_1 = require("./spilder-topic-article");
const spilder_topic_article_comment_1 = require("./spilder-topic-article-comment");
let SpilderAccount = class SpilderAccount {
    id;
    uid;
    nickname;
    platform;
    type;
    verified;
    verified_type;
    verified_reason;
    verified_type_ext;
    weihao;
    from;
    url;
    location;
    followers_count;
    friends_count;
    description;
    created_at;
    gender;
    statuses_count;
    profile_url;
    profile_image_url;
    company;
    education;
    career;
    birthday;
    sunshine_credit;
    tags;
    mbrank;
    mbtype;
    pc_new;
    top_user;
    user_type;
    wenda;
    planet_video;
    is_muteuser;
    topics;
    articles;
    comments;
    createDate;
    updateDate;
};
exports.SpilderAccount = SpilderAccount;
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true,
        widget: {
            type: 'number'
        }
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SpilderAccount.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: 'ID',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SpilderAccount.prototype, "uid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '账号'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SpilderAccount.prototype, "nickname", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台',
        widget: {
            type: 'selector',
            from: 'wb_platform',
            config: {
                label: 'title',
                value: 'name'
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: 'weibo'
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "platform", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '账号类型'
    }),
    (0, typeorm_1.Column)({
        default: '自媒体'
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "type", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '是否认证',
        hideInSearch: true,
        widget: {
            type: 'boolean'
        }
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "verified", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '认证类型',
        sortable: true,
        width: '160px',
        hideInSearch: true,
        hideInTable: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                        label: '未认证',
                        value: -1
                    }, {
                        label: '微博个人认证',
                        value: 2
                    }, {
                        label: '微博官方认证',
                        value: 1
                    }]
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: 0,
    }),
    __metadata("design:type", Number)
], SpilderAccount.prototype, "verified_type", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '认证信息',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "verified_reason", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '认证类型标题',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "verified_type_ext", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '微号',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "weihao", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '所属媒体',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "from", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '连接',
        hideInSearch: true,
        widget: {
            type: 'link',
            label: 'nickname'
        }
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "url", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '地区',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "location", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '关注数',
        hideInSearch: true,
        hideInTable: true,
        sortable: true
    }),
    (0, typeorm_1.Column)({
        default: 0,
        type: 'bigint'
    }),
    __metadata("design:type", Number)
], SpilderAccount.prototype, "followers_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '朋友数',
        hideInSearch: true,
        hideInTable: true,
        sortable: true
    }),
    (0, typeorm_1.Column)({
        default: 0,
        type: 'bigint'
    }),
    __metadata("design:type", Number)
], SpilderAccount.prototype, "friends_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '详情',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "description", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '创建时间',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "created_at", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '性别',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "gender", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '粉丝数',
        hideInSearch: true,
        hideInTable: true,
        sortable: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderAccount.prototype, "statuses_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '个人连接',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "profile_url", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '头像',
        width: '65px',
        hideInSearch: true,
        hideInTable: true,
        widget: {
            type: 'image',
            config: {
                style: {
                    width: '65px',
                }
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "profile_image_url", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '公司',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "company", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '学校',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "education", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '行业',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "career", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '生日',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "birthday", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '信誉',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "sunshine_credit", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '标签',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)('simple-array'),
    __metadata("design:type", Array)
], SpilderAccount.prototype, "tags", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '认证小类',
        sortable: true,
        hideInSearch: true,
        hideInTable: true,
        widget: {
            type: 'number'
        }
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderAccount.prototype, "mbrank", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '认证大类',
        sortable: true,
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderAccount.prototype, "mbtype", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true,
        hideInSearch: true,
        sortable: true,
        widget: {
            type: 'number'
        }
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderAccount.prototype, "pc_new", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true,
        hideInSearch: true,
        sortable: true,
        widget: {
            type: 'number'
        }
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderAccount.prototype, "top_user", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: 'V',
        sortable: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                        label: '无',
                        value: 0
                    }, {
                        label: '黄',
                        value: 1
                    }, {
                        label: '灰',
                        value: 2
                    }, {
                        label: '蓝',
                        value: 3
                    }, {
                        label: '红',
                        value: 4
                    }]
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderAccount.prototype, "user_type", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '问答',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderAccount.prototype, "wenda", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: false
    }),
    __metadata("design:type", Boolean)
], SpilderAccount.prototype, "planet_video", void 0);
__decorate([
    (0, rest_1.Widget)({
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: false,
    }),
    __metadata("design:type", Boolean)
], SpilderAccount.prototype, "is_muteuser", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '话题',
        widget: {
            type: 'array',
            from: 'spilder_topic',
            where: {
                uid: 'uid'
            }
        }
    }),
    (0, typeorm_1.OneToMany)(() => spilder_topic_1.SpilderTopic, it => it.uid, {
        createForeignKeyConstraints: false
    }),
    __metadata("design:type", Array)
], SpilderAccount.prototype, "topics", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '帖子',
        widget: {
            type: 'array',
            from: 'spilder_topic_article',
            where: {
                uid: 'uid'
            }
        }
    }),
    (0, typeorm_1.OneToMany)(() => spilder_topic_article_1.SpilderTopicArticle, it => it.uid, {
        createForeignKeyConstraints: false
    }),
    __metadata("design:type", Array)
], SpilderAccount.prototype, "articles", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '评论',
        widget: {
            type: 'array',
            from: 'spilder_topic_article_comment',
            where: {
                uid: 'uid'
            }
        }
    }),
    (0, typeorm_1.OneToMany)(() => spilder_topic_article_comment_1.SpilderTopicArticleComment, it => it.uid, {
        createForeignKeyConstraints: false
    }),
    __metadata("design:type", Array)
], SpilderAccount.prototype, "comments", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    }),
    (0, typeorm_1.CreateDateColumn)({
        name: 'create_date'
    }),
    __metadata("design:type", Date)
], SpilderAccount.prototype, "createDate", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '更新时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    }),
    (0, typeorm_1.UpdateDateColumn)({
        name: 'update_date'
    }),
    __metadata("design:type", Date)
], SpilderAccount.prototype, "updateDate", void 0);
exports.SpilderAccount = SpilderAccount = __decorate([
    (0, rest_1.Component)({
        title: '媒体'
    }),
    (0, typeorm_1.Entity)({
        name: 'spilder_account'
    }),
    (0, typeorm_1.Unique)(['uid', 'platform'])
], SpilderAccount);
