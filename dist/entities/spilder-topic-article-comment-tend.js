"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderTopicArticleCommentTend = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
let SpilderTopicArticleCommentTend = class SpilderTopicArticleCommentTend {
    id;
    cid;
    like_counts;
    createDate;
};
exports.SpilderTopicArticleCommentTend = SpilderTopicArticleCommentTend;
__decorate([
    (0, rest_1.Widget)({
        title: '序号'
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SpilderTopicArticleCommentTend.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '评论'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], SpilderTopicArticleCommentTend.prototype, "cid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '点赞',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        nullable: true,
        type: 'bigint'
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleCommentTend.prototype, "like_counts", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    }),
    (0, typeorm_1.CreateDateColumn)({
        name: 'create_date'
    }),
    __metadata("design:type", Date)
], SpilderTopicArticleCommentTend.prototype, "createDate", void 0);
exports.SpilderTopicArticleCommentTend = SpilderTopicArticleCommentTend = __decorate([
    (0, rest_1.Component)({
        title: '评论截面'
    }),
    (0, typeorm_1.Entity)({
        name: 'spilder_topic_article_comment_tend'
    })
], SpilderTopicArticleCommentTend);
