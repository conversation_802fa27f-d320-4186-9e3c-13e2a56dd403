"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderTopicArticleCommentUnique = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
let SpilderTopicArticleCommentUnique = class SpilderTopicArticleCommentUnique {
    uid;
    platform;
    cid;
};
exports.SpilderTopicArticleCommentUnique = SpilderTopicArticleCommentUnique;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SpilderTopicArticleCommentUnique.prototype, "uid", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SpilderTopicArticleCommentUnique.prototype, "platform", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'bigint'
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleCommentUnique.prototype, "cid", void 0);
exports.SpilderTopicArticleCommentUnique = SpilderTopicArticleCommentUnique = __decorate([
    (0, rest_1.Component)({
        title: '评论仿重表'
    }),
    (0, typeorm_1.Entity)({
        name: 'spilder_topic_article_comment_unique'
    }),
    (0, typeorm_1.Unique)(['platform', 'cid'])
], SpilderTopicArticleCommentUnique);
