"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderTopicArticleComment = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
const event_1 = require("./event");
const platform_1 = require("./platform");
const spilder_topic_1 = require("./spilder-topic");
const spilder_topic_article_1 = require("./spilder-topic-article");
let SpilderTopicArticleComment = class SpilderTopicArticleComment {
    id;
    platform;
    platformEntity;
    eid;
    event;
    tid;
    topic;
    aid;
    article;
    created_at;
    cid;
    floor_number;
    text;
    source;
    uid;
    nickname;
    like_counts;
    total_number;
    max_id;
    isLikedByMblogAuthor;
    pid;
    parent;
    comments;
    createDate;
    updateDate;
};
exports.SpilderTopicArticleComment = SpilderTopicArticleComment;
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        hideInSearch: true,
        sortable: true
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SpilderTopicArticleComment.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台',
        path: 'platformEntity.title',
        widget: {
            type: 'selector',
            from: 'wb_platform',
            config: {
                label: 'title',
                value: 'name'
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: 'weibo',
    }),
    __metadata("design:type", String)
], SpilderTopicArticleComment.prototype, "platform", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台',
        hideInTable: true
    }),
    (0, typeorm_1.ManyToOne)(() => platform_1.WbPlatform, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'platform',
        referencedColumnName: 'name'
    }),
    __metadata("design:type", platform_1.WbPlatform)
], SpilderTopicArticleComment.prototype, "platformEntity", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件',
        path: 'event.title',
        widget: {
            type: 'selector',
            from: 'wb_event',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleComment.prototype, "eid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件'
    }),
    (0, typeorm_1.ManyToOne)(() => event_1.WbEvent, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'eid',
        referencedColumnName: 'id'
    }),
    __metadata("design:type", event_1.WbEvent)
], SpilderTopicArticleComment.prototype, "event", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '话题',
        path: 'topic.title',
        widget: {
            type: 'selector',
            from: 'spilder_topic',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleComment.prototype, "tid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '话题'
    }),
    (0, typeorm_1.ManyToOne)(() => spilder_topic_1.SpilderTopic, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'tid',
        referencedColumnName: 'id'
    }),
    __metadata("design:type", spilder_topic_1.SpilderTopic)
], SpilderTopicArticleComment.prototype, "topic", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '帖子',
        path: 'article.text_raw',
        width: '220px',
        widget: {
            type: 'selector',
            from: 'spilder_topic_article',
            config: {
                label: 'text_raw',
                value: 'id'
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleComment.prototype, "aid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '文章'
    }),
    (0, typeorm_1.ManyToOne)(() => spilder_topic_article_1.SpilderTopicArticle, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'aid',
        referencedColumnName: 'id'
    }),
    __metadata("design:type", spilder_topic_article_1.SpilderTopicArticle)
], SpilderTopicArticleComment.prototype, "article", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '评论时间',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", Date)
], SpilderTopicArticleComment.prototype, "created_at", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '评论ID',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleComment.prototype, "cid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '楼层',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleComment.prototype, "floor_number", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '评论文本',
        hideInSearch: true,
        widget: {
            type: 'textarea',
        }
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopicArticleComment.prototype, "text", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '来源',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopicArticleComment.prototype, "source", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '用户ID',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopicArticleComment.prototype, "uid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '用户昵称',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopicArticleComment.prototype, "nickname", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '点赞',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleComment.prototype, "like_counts", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '子评论',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleComment.prototype, "total_number", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: 'ID',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleComment.prototype, "max_id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '是否被作者喜欢',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        name: 'is_liked_by_mblog_author',
        default: false
    }),
    __metadata("design:type", Boolean)
], SpilderTopicArticleComment.prototype, "isLikedByMblogAuthor", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '上级ID',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleComment.prototype, "pid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '上级评论'
    }),
    (0, typeorm_1.ManyToOne)(() => SpilderTopicArticleComment, it => it.pid, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'pid',
        referencedColumnName: 'id'
    }),
    __metadata("design:type", SpilderTopicArticleComment)
], SpilderTopicArticleComment.prototype, "parent", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '子评论'
    }),
    (0, typeorm_1.OneToMany)(() => SpilderTopicArticleComment, it => it.pid, {
        createForeignKeyConstraints: false
    }),
    __metadata("design:type", Array)
], SpilderTopicArticleComment.prototype, "comments", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    }),
    (0, typeorm_1.CreateDateColumn)({
        name: 'create_date'
    }),
    __metadata("design:type", Date)
], SpilderTopicArticleComment.prototype, "createDate", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '更新时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    }),
    (0, typeorm_1.UpdateDateColumn)({
        name: 'update_date'
    }),
    __metadata("design:type", Date)
], SpilderTopicArticleComment.prototype, "updateDate", void 0);
exports.SpilderTopicArticleComment = SpilderTopicArticleComment = __decorate([
    (0, rest_1.Component)({
        title: '评论'
    }),
    (0, typeorm_1.Entity)({
        name: 'spilder_topic_article_comment'
    })
], SpilderTopicArticleComment);
