"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderTopicArticleTend = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
let SpilderTopicArticleTend = class SpilderTopicArticleTend {
    id;
    aid;
    reposts_count;
    comments_count;
    attitudes_count;
    createDate;
};
exports.SpilderTopicArticleTend = SpilderTopicArticleTend;
__decorate([
    (0, rest_1.Widget)({
        title: '序号'
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SpilderTopicArticleTend.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '文章'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], SpilderTopicArticleTend.prototype, "aid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '转发数'
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleTend.prototype, "reposts_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '评论数'
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleTend.prototype, "comments_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '点赞数'
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopicArticleTend.prototype, "attitudes_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    }),
    (0, typeorm_1.CreateDateColumn)({
        name: 'create_date'
    }),
    __metadata("design:type", Date)
], SpilderTopicArticleTend.prototype, "createDate", void 0);
exports.SpilderTopicArticleTend = SpilderTopicArticleTend = __decorate([
    (0, rest_1.Component)({
        title: '帖子截面'
    }),
    (0, typeorm_1.Entity)({
        name: 'spilder_topic_article_tend'
    })
], SpilderTopicArticleTend);
