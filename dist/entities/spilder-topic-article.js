"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderTopicArticle = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
const event_1 = require("./event");
const platform_1 = require("./platform");
const spilder_account_1 = require("./spilder-account");
const spilder_topic_1 = require("./spilder-topic");
const spilder_topic_article_comment_1 = require("./spilder-topic-article-comment");
let SpilderTopicArticle = class SpilderTopicArticle {
    id;
    tid;
    topic;
    eid;
    event;
    platform;
    platformEntity;
    create_at;
    mid;
    mblogid;
    title;
    uid;
    nickname;
    user_type;
    account;
    text_raw;
    source;
    reposts_count;
    comments_count;
    attitudes_count;
    pic_bg_new;
    structs;
    hasImage;
    hasVideo;
    hasLink;
    region_name;
    textLength;
    comments;
    createDate;
    updateDate;
};
exports.SpilderTopicArticle = SpilderTopicArticle;
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        hideInSearch: true
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SpilderTopicArticle.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '话题',
        path: 'topic.title',
        widget: {
            type: 'selector',
            from: 'spilder_topic',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], SpilderTopicArticle.prototype, "tid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '话题'
    }),
    (0, typeorm_1.ManyToOne)(() => spilder_topic_1.SpilderTopic, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'tid',
        referencedColumnName: 'id'
    }),
    __metadata("design:type", spilder_topic_1.SpilderTopic)
], SpilderTopicArticle.prototype, "topic", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件',
        path: 'event.title',
        widget: {
            type: 'selector',
            from: 'wb_event',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], SpilderTopicArticle.prototype, "eid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件'
    }),
    (0, typeorm_1.ManyToOne)(() => event_1.WbEvent, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'eid',
        referencedColumnName: 'id'
    }),
    __metadata("design:type", event_1.WbEvent)
], SpilderTopicArticle.prototype, "event", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台',
        path: 'platformEntity.title',
        widget: {
            type: 'selector',
            from: 'wb_platform',
            config: {
                label: 'title',
                value: 'name'
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: 'weibo'
    }),
    __metadata("design:type", String)
], SpilderTopicArticle.prototype, "platform", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台'
    }),
    (0, typeorm_1.ManyToOne)(() => platform_1.WbPlatform, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'platform',
        referencedColumnName: 'name'
    }),
    __metadata("design:type", platform_1.WbPlatform)
], SpilderTopicArticle.prototype, "platformEntity", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '发帖时间',
        widget: {
            type: 'date',
            config: {
                showTime: true
            }
        }
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", Date)
], SpilderTopicArticle.prototype, "create_at", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '文章id',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopicArticle.prototype, "mid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '文章id',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopicArticle.prototype, "mblogid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '标题标签',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopicArticle.prototype, "title", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '用户ID',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopicArticle.prototype, "uid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '媒体',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopicArticle.prototype, "nickname", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '媒体类型',
        path: 'account.type',
        hideInSearch: true
    }),
    __metadata("design:type", String)
], SpilderTopicArticle.prototype, "user_type", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '媒体'
    }),
    (0, typeorm_1.ManyToOne)(() => spilder_account_1.SpilderAccount, it => it.uid, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'uid',
        referencedColumnName: 'uid'
    }),
    __metadata("design:type", spilder_account_1.SpilderAccount)
], SpilderTopicArticle.prototype, "account", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '帖子文本',
        hideInSearch: true,
        width: '220px',
        widget: {
            type: 'textarea',
        }
    }),
    (0, typeorm_1.Column)({
        type: 'text'
    }),
    __metadata("design:type", String)
], SpilderTopicArticle.prototype, "text_raw", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '来源',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopicArticle.prototype, "source", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '转发数',
        hideInSearch: true,
        widget: {
            type: 'number'
        }
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderTopicArticle.prototype, "reposts_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '评论数',
        hideInSearch: true,
        widget: {
            type: 'number'
        }
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderTopicArticle.prototype, "comments_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '点赞数',
        hideInSearch: true,
        widget: {
            type: 'number'
        }
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        default: 0
    }),
    __metadata("design:type", Number)
], SpilderTopicArticle.prototype, "attitudes_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '大图',
        hideInSearch: true,
        hideInTable: true,
        widget: {
            type: 'image',
            config: {
                style: {
                    width: '65px',
                }
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: '',
    }),
    __metadata("design:type", String)
], SpilderTopicArticle.prototype, "pic_bg_new", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '关联话题',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)('simple-array', {
        nullable: true
    }),
    __metadata("design:type", Array)
], SpilderTopicArticle.prototype, "structs", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '有无图片',
        hideInSearch: true,
        widget: {
            type: 'boolean',
            checked: '是',
            unchecked: '否'
        }
    }),
    (0, typeorm_1.Column)({
        name: 'has_image',
        default: false
    }),
    __metadata("design:type", Boolean)
], SpilderTopicArticle.prototype, "hasImage", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '有无视频',
        hideInSearch: true,
        widget: {
            type: 'boolean',
            checked: '是',
            unchecked: '否'
        }
    }),
    (0, typeorm_1.Column)({
        name: 'has_video',
        default: false
    }),
    __metadata("design:type", Boolean)
], SpilderTopicArticle.prototype, "hasVideo", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '有无连接',
        hideInSearch: true,
        widget: {
            type: 'boolean',
            checked: '是',
            unchecked: '否'
        }
    }),
    (0, typeorm_1.Column)({
        name: 'has_link',
        default: false
    }),
    __metadata("design:type", Boolean)
], SpilderTopicArticle.prototype, "hasLink", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '地址',
        hideInSearch: true,
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopicArticle.prototype, "region_name", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '长度',
        hideInSearch: true,
    }),
    (0, typeorm_1.Column)({
        default: 0,
        name: 'text_length'
    }),
    __metadata("design:type", Number)
], SpilderTopicArticle.prototype, "textLength", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '评论',
        widget: {
            type: 'array',
            where: { aid: 'id' }
        }
    }),
    (0, typeorm_1.OneToMany)(() => spilder_topic_article_comment_1.SpilderTopicArticleComment, it => it.aid, {
        createForeignKeyConstraints: false
    }),
    __metadata("design:type", Array)
], SpilderTopicArticle.prototype, "comments", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true,
        widget: {
            type: 'date',
            config: {
                showTime: true
            }
        }
    }),
    (0, typeorm_1.CreateDateColumn)({
        name: 'create_date'
    }),
    __metadata("design:type", Date)
], SpilderTopicArticle.prototype, "createDate", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '更新时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true,
        widget: {
            type: 'date',
            config: {
                showTime: true
            }
        }
    }),
    (0, typeorm_1.UpdateDateColumn)({
        name: 'update_date'
    }),
    __metadata("design:type", Date)
], SpilderTopicArticle.prototype, "updateDate", void 0);
exports.SpilderTopicArticle = SpilderTopicArticle = __decorate([
    (0, rest_1.Component)({
        title: '帖子'
    }),
    (0, typeorm_1.Entity)({
        name: 'spilder_topic_article'
    })
], SpilderTopicArticle);
