"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderTopicTend = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
let SpilderTopicTend = class SpilderTopicTend {
    id;
    tid;
    read_count;
    mention_count;
    ori_uv_Count;
    createDate;
};
exports.SpilderTopicTend = SpilderTopicTend;
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        widget: {
            type: 'number'
        },
        hideInSearch: true
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SpilderTopicTend.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '话题',
        widget: {
            type: 'selector',
            from: 'spilder_topic',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], SpilderTopicTend.prototype, "tid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '阅读次数',
        widget: {
            type: 'number'
        },
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopicTend.prototype, "read_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '讨论次数',
        widget: {
            type: 'number'
        },
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopicTend.prototype, "mention_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '原创人数',
        widget: {
            type: 'number'
        },
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopicTend.prototype, "ori_uv_Count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    }),
    (0, typeorm_1.CreateDateColumn)({
        name: 'create_date'
    }),
    __metadata("design:type", Date)
], SpilderTopicTend.prototype, "createDate", void 0);
exports.SpilderTopicTend = SpilderTopicTend = __decorate([
    (0, rest_1.Component)({
        title: '主题截面'
    }),
    (0, typeorm_1.Entity)({
        name: 'spilder_topic_tend'
    })
], SpilderTopicTend);
