"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderTopic = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
const event_1 = require("./event");
const platform_1 = require("./platform");
const spilder_account_1 = require("./spilder-account");
const spilder_topic_article_1 = require("./spilder-topic-article");
const spilder_topic_article_comment_1 = require("./spilder-topic-article-comment");
let SpilderTopic = class SpilderTopic {
    id;
    platform;
    platformEntity;
    title;
    uid;
    user;
    eid;
    event;
    cate_id;
    category_str;
    location;
    related_topic;
    summary;
    target_url;
    topic_tag;
    create_at;
    claim_id;
    claim;
    claim_name;
    read_count;
    mention_count;
    ori_uv_Count;
    articles;
    comments;
    createDate;
    updateDate;
};
exports.SpilderTopic = SpilderTopic;
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        hideInSearch: true
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SpilderTopic.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台',
        path: 'platformEntity.title',
        widget: {
            type: 'selector',
            from: 'wb_platform',
            config: {
                label: 'title',
                value: 'name'
            }
        }
    }),
    (0, typeorm_1.Column)({
        default: 'weibo'
    }),
    __metadata("design:type", String)
], SpilderTopic.prototype, "platform", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台'
    }),
    (0, typeorm_1.ManyToOne)(() => platform_1.WbPlatform, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'platform',
        referencedColumnName: 'name'
    }),
    __metadata("design:type", platform_1.WbPlatform)
], SpilderTopic.prototype, "platformEntity", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '话题名'
    }),
    (0, typeorm_1.Column)({
        nullable: false,
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopic.prototype, "title", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '创建用户',
        path: 'user.nickname',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], SpilderTopic.prototype, "uid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '创建用户'
    }),
    (0, typeorm_1.ManyToOne)(() => spilder_account_1.SpilderAccount, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'uid',
        referencedColumnName: 'uid'
    }),
    __metadata("design:type", spilder_account_1.SpilderAccount)
], SpilderTopic.prototype, "user", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '所属事件',
        path: 'event.title',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopic.prototype, "eid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '事件'
    }),
    (0, typeorm_1.ManyToOne)(() => event_1.WbEvent, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'eid',
        referencedColumnName: 'id'
    }),
    __metadata("design:type", event_1.WbEvent)
], SpilderTopic.prototype, "event", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '分类ID',
        hideInTable: true,
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopic.prototype, "cate_id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '分类名',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopic.prototype, "category_str", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '发布位置',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopic.prototype, "location", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '相关话题',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopic.prototype, "related_topic", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '导语',
        widget: {
            type: 'textarea',
        },
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopic.prototype, "summary", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '话题连接',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopic.prototype, "target_url", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '主题标签',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopic.prototype, "topic_tag", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '创建时间',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: 0,
        type: 'bigint',
    }),
    __metadata("design:type", Number)
], SpilderTopic.prototype, "create_at", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '主持人id',
        // path: 'claim.nickname',
        hideInSearch: true,
        hideInTable: true
    }),
    (0, typeorm_1.Column)({
        nullable: true
    }),
    __metadata("design:type", String)
], SpilderTopic.prototype, "claim_id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '主持人'
    }),
    (0, typeorm_1.ManyToOne)(() => spilder_account_1.SpilderAccount, {
        createForeignKeyConstraints: false
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'claim_id',
        referencedColumnName: 'uid'
    }),
    __metadata("design:type", spilder_account_1.SpilderAccount)
], SpilderTopic.prototype, "claim", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '主持人昵称',
        hideInSearch: true
    }),
    (0, typeorm_1.Column)({
        default: ''
    }),
    __metadata("design:type", String)
], SpilderTopic.prototype, "claim_name", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '阅读次数',
        hideInSearch: true,
        sortable: true
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopic.prototype, "read_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '讨论次数',
        hideInSearch: true,
        sortable: true
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopic.prototype, "mention_count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '原创人数',
        hideInSearch: true,
        sortable: true
    }),
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true
    }),
    __metadata("design:type", Number)
], SpilderTopic.prototype, "ori_uv_Count", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '帖子',
        widget: {
            type: 'array',
            where: { tid: 'id' }
        }
    }),
    (0, typeorm_1.OneToMany)(() => spilder_topic_article_1.SpilderTopicArticle, it => it.eid, {
        createForeignKeyConstraints: false
    }),
    __metadata("design:type", Array)
], SpilderTopic.prototype, "articles", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '评论',
        widget: {
            type: 'array',
            where: { tid: 'id' }
        }
    }),
    (0, typeorm_1.OneToMany)(() => spilder_topic_article_comment_1.SpilderTopicArticleComment, it => it.eid, {
        createForeignKeyConstraints: false
    }),
    __metadata("design:type", Array)
], SpilderTopic.prototype, "comments", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    }),
    (0, typeorm_1.CreateDateColumn)({
        name: 'create_date'
    }),
    __metadata("design:type", Date)
], SpilderTopic.prototype, "createDate", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '更新时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    }),
    (0, typeorm_1.UpdateDateColumn)({
        name: 'update_date'
    }),
    __metadata("design:type", Date)
], SpilderTopic.prototype, "updateDate", void 0);
exports.SpilderTopic = SpilderTopic = __decorate([
    (0, rest_1.Component)({
        title: '话题/问答'
    }),
    (0, typeorm_1.Entity)({
        name: 'spilder_topic'
    })
], SpilderTopic);
