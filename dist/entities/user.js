"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WbUser = void 0;
const rest_1 = require("@nger/rest");
const typeorm_1 = require("typeorm");
let WbUser = class WbUser {
    id;
    from;
    uid;
    name;
    avatar;
    cookies;
};
__decorate([
    (0, rest_1.Widget)({
        title: '序号',
        hideInAdd: true,
        hideInEdit: true,
        hideInSearch: true,
        sortable: true,
        width: `120px`
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], WbUser.prototype, "id", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '平台'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbUser.prototype, "from", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '用户ID'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbUser.prototype, "uid", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '用户名'
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbUser.prototype, "name", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '头像',
        hideInSearch: true,
        widget: {
            type: 'avatar'
        }
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WbUser.prototype, "avatar", void 0);
__decorate([
    (0, rest_1.Widget)({
        title: '凭证',
        hideInEdit: true,
        hideInSearch: true,
        hideInTable: true,
        hideInAdd: true
    }),
    (0, typeorm_1.Column)({
        type: 'text'
    }),
    __metadata("design:type", String)
], WbUser.prototype, "cookies", void 0);
WbUser = __decorate([
    (0, rest_1.Component)({
        title: '模拟登录用户',
        hideAdd: true
    }),
    (0, typeorm_1.Entity)({
        name: 'wb_user'
    })
], WbUser);
exports.WbUser = WbUser;
