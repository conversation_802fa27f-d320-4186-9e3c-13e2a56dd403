"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MqService = void 0;
const rabbitmq_util_1 = require("./rabbitmq.util");
const rxjs_1 = require("rxjs");
class MqService {
    injector;
    options;
    get util() {
        return this.injector.get(rabbitmq_util_1.RabbbitmqUtil);
    }
    constructor(injector, options) {
        this.injector = injector;
        this.options = options;
    }
    onError(e) {
        console.log(`mq server`, e);
    }
    async get() {
        const connection = await this.util.init();
        const channel = await this.util.createConfirmChannel(connection);
        const msg = await channel.get(this.options.queue, { noAck: false });
        return msg;
    }
    async count() {
        const connection = await this.util.init();
        const channel = await this.util.createConfirmChannel(connection);
        const count = await channel.purgeQueue(this.options.queue).then(q => q.messageCount);
        return count;
    }
    async init() { }
    async publish(msg, ttl = 1000 * 60 * 30) {
        return this.send(JSON.stringify(msg), ttl).catch(e => this.onError(e));
    }
    publishs(list, ttl = 1000 * 60 * 30) {
        return this.sends(list.filter(it => {
            return !!it;
        }).map(it => {
            return {
                msg: JSON.stringify(it),
                ttl
            };
        })).catch(e => this.onError(e));
    }
    async watch(isDead = false) {
        let sub;
        if (isDead) {
            sub = await this.dead();
        }
        else {
            sub = await this.consumer();
        }
        return sub;
    }
    async sends(list) {
        const connection = await this.util.init();
        const channel = await this.util.createConfirmChannel(connection);
        return Promise.all(list.map(it => {
            return new Promise((resolve, reject) => {
                this._send(channel, it.msg, it.ttl, (err) => reject(err), () => resolve(true));
            });
        })).finally(() => {
            channel.close().then(() => {
                return connection.close().catch(e => { });
            }).catch(() => { });
        });
    }
    async _send(channel, msg, ttl, onError, onSuccess) {
        channel.on('error', (err) => {
            onError(err);
        });
        // 默认交换机-队列-路由
        channel.assertExchange(this.options.exchange, 'direct', {
            durable: true
        }).then(() => {
            channel.publish(this.options.exchange, this.options.routingKey, Buffer.from(msg), {
                expiration: ttl,
                persistent: true,
                mandatory: true
            }, (err, ok) => {
                if (err) {
                    onError(err);
                }
                else {
                    onSuccess(true);
                }
            });
        });
    }
    async send(msg, ttl) {
        const connection = await this.util.init();
        const channel = await this.util.createConfirmChannel(connection);
        const isSend = await new Promise((resolve, reject) => {
            this._send(channel, msg, ttl, (err) => reject(err), () => resolve(true));
        }).catch((e) => {
            console.error('catch an error', e.message);
        });
        await channel.close().catch(e => {
            console.log('channel close error', e.message);
        });
        await connection.close().catch(e => { });
        return isSend;
    }
    async consumer() {
        const connection = await this.util.init();
        const channel = await this.util.createChannel(connection);
        return new rxjs_1.Observable(sub => {
            channel.on('error', (erro) => {
                sub.error(erro);
            });
            this._createConsumer(channel, sub);
            return () => {
                channel.close().catch(e => {
                    console.log({ err: e });
                }).finally(() => {
                    connection.close().catch(e => { });
                });
            };
        });
    }
    async _createConsumer(channel, sub) {
        channel.on('error', (err) => {
            sub.error(err);
        });
        channel.prefetch(this.options.prefetch || 1);
        // 死信交换机-路由
        await channel.assertExchange(this.options.exchange, 'direct', { durable: true });
        const queueEX = await channel.assertQueue(this.options.queue, {
            exclusive: false,
            deadLetterExchange: this.options.deadExchange,
            deadLetterRoutingKey: this.options.deadRoutingKey,
        });
        await channel.bindQueue(this.options.queue, this.options.exchange, this.options.routingKey);
        await channel.consume(queueEX.queue, (msg) => {
            const ack = () => channel.ack(msg);
            const nack = (allUpTo = true, requee = true) => channel.nack(msg, allUpTo, requee);
            const reject = (requee = false) => channel.reject(msg, requee);
            sub.next({ message: msg, ack: ack, noAck: nack, channel, data: this.getMsg(msg?.content), reject });
        }, { noAck: false });
    }
    async dead() {
        const connection = await this.util.init();
        const channel = await this.util.createChannel(connection);
        return new rxjs_1.Observable((sub) => {
            this._createDead(channel, sub);
            return () => {
                channel.close().catch(e => { });
            };
        });
    }
    async _createDead(channel, sub) {
        channel.on('error', (err) => {
            sub.error(err);
        });
        await channel.prefetch(this.options.prefetch || 1);
        // 死信交换机-路由
        // 默认交换机
        await channel.assertExchange(this.options.deadExchange, 'direct', { durable: true });
        // 队列，超时发动到死信队列
        const queueDLX = await channel.assertQueue(this.options.deadQueue, {
            exclusive: false
        });
        await channel.bindQueue(this.options.deadQueue, this.options.deadExchange, this.options.deadRoutingKey);
        await channel.consume(queueDLX.queue, msg => {
            const ack = (allUpTo = true) => channel.ack(msg, allUpTo);
            const nack = (allUpTo = true, requee = true) => channel.nack(msg, allUpTo, requee);
            const reject = (requee = false) => channel.reject(msg, requee);
            sub.next({ reject, message: msg, ack: ack, noAck: nack, channel, data: this.getMsg(msg?.content) });
        }, { noAck: false });
    }
    sleep(time) {
        return new Promise((resolve) => setTimeout(resolve, time * 1000));
    }
    getMsg(buf) {
        try {
            if (buf) {
                const str = buf.toString('utf-8');
                return JSON.parse(str);
            }
        }
        catch (e) {
            console.log(e);
        }
        return null;
    }
}
exports.MqService = MqService;
