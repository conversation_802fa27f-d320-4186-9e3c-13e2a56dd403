"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RabbitmqController = void 0;
const core_1 = require("@nger/core");
const http_1 = require("@nger/http");
const mq_factory_1 = require("./mq.factory");
let RabbitmqController = class RabbitmqController {
    injector;
    get mqFactory() {
        return this.injector.get(mq_factory_1.MqFactory);
    }
    constructor(injector) {
        this.injector = injector;
    }
    async publish(body) {
        const mq = await this.mqFactory.create({
            exchange: 'task.exchange',
            deadExchange: 'dead.task.exchange',
            routingKey: 'task.routingKey',
            deadRoutingKey: 'dead.task.routingKey',
            queue: 'task.queue',
            deadQueue: 'dead.task.queue'
        });
        await mq.publish(body);
        return body;
    }
    async publishs(body) {
        const mq = await this.mqFactory.create({
            exchange: 'task.exchange',
            deadExchange: 'dead.task.exchange',
            routingKey: 'task.routingKey',
            deadRoutingKey: 'dead.task.routingKey',
            queue: 'task.queue',
            deadQueue: 'dead.task.queue'
        });
        const result = await mq.publishs(body);
        return result;
    }
    async count() {
        const mq = await this.mqFactory.create({
            exchange: 'task.exchange',
            deadExchange: 'dead.task.exchange',
            routingKey: 'task.routingKey',
            deadRoutingKey: 'dead.task.routingKey',
            queue: 'task.queue',
            deadQueue: 'dead.task.queue'
        });
        const count = await mq.count();
        return count;
    }
};
exports.RabbitmqController = RabbitmqController;
__decorate([
    (0, http_1.Post)('publish'),
    __param(0, (0, http_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RabbitmqController.prototype, "publish", null);
__decorate([
    (0, http_1.Post)('publishs'),
    __param(0, (0, http_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RabbitmqController.prototype, "publishs", null);
__decorate([
    (0, core_1.Get)('count'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RabbitmqController.prototype, "count", null);
exports.RabbitmqController = RabbitmqController = __decorate([
    (0, core_1.Controller)('@nger/mq'),
    __metadata("design:paramtypes", [core_1.Injector])
], RabbitmqController);
