"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var RabbitmqModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RabbitmqModule = void 0;
const core_1 = require("@nger/core");
const mq_factory_1 = require("./mq.factory");
const rabbitmq_controller_1 = require("./rabbitmq.controller");
const rabbitmq_util_1 = require("./rabbitmq.util");
let RabbitmqModule = RabbitmqModule_1 = class RabbitmqModule {
    static forRoot() {
        return {
            providers: [
                rabbitmq_util_1.RabbbitmqUtil,
                mq_factory_1.MqFactory
            ],
            module: RabbitmqModule_1
        };
    }
};
exports.RabbitmqModule = RabbitmqModule;
exports.RabbitmqModule = RabbitmqModule = RabbitmqModule_1 = __decorate([
    (0, core_1.Module)({
        providers: [],
        imports: [],
        controllers: [
            rabbitmq_controller_1.RabbitmqController
        ]
    })
], RabbitmqModule);
