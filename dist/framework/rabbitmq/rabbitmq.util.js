"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RabbbitmqUtil = void 0;
const core_1 = require("@nger/core");
const amqplib_1 = require("amqplib");
let RabbbitmqUtil = class RabbbitmqUtil {
    async init() {
        const connect = await this.createConnect();
        return connect;
    }
    async destory(connect) {
        await connect.close().catch(e => {
            console.log('destory error', e.message);
        });
    }
    createConnect() {
        const option = {
            hostname: process.env['MQ_HOST'] || '***************',
            port: parseInt(process.env['MQ_PORT'] || '5672'),
            username: process.env['MQ_USER'] || 'imeepos',
            password: process.env['MQ_PASS'] || '123qwe',
            protocol: 'amqp'
        };
        return (0, amqplib_1.connect)(option);
    }
    createChannel(connect) {
        return connect.createChannel();
    }
    createConfirmChannel(connect) {
        return connect.createConfirmChannel();
    }
    sleep(time) {
        return new Promise((resolve) => setTimeout(resolve, time * 1000));
    }
};
exports.RabbbitmqUtil = RabbbitmqUtil;
exports.RabbbitmqUtil = RabbbitmqUtil = __decorate([
    (0, core_1.Injectable)()
], RabbbitmqUtil);
