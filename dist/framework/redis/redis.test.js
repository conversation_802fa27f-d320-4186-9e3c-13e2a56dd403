"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const _1 = require(".");
const factory = new _1.RedisFactory(new _1.RedisUtil());
async function bootstrap() {
    const client = await factory.create();
    const addResult = await client.bf.add('demo', `1`);
    const exists = await client.bf.exists('demo', `1`);
    await client.bf.add('demo', `2`);
    debugger;
}
bootstrap();
