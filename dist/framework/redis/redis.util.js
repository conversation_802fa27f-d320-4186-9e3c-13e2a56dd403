"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.REDIS = exports.RedisUtil = void 0;
const core_1 = require("@nger/core");
let RedisUtil = class RedisUtil {
    async getRedisConfig() {
        return {
            url: process.env['REDIS_URL'] || 'redis://***************:6379',
            socket: {
                keepAlive: 1000 * 3
            },
            database: 0
        };
    }
};
exports.RedisUtil = RedisUtil;
exports.RedisUtil = RedisUtil = __decorate([
    (0, core_1.Injectable)()
], RedisUtil);
exports.REDIS = new core_1.InjectionToken(`RedisClientType`);
