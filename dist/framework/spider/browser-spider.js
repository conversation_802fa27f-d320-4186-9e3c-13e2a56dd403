"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BrowserSpider = exports.BrowserRequest = void 0;
const rxjs_1 = require("rxjs");
const path_1 = require("path");
const url_1 = require("url");
const crypto_1 = require("crypto");
const tokens_1 = require("../../tokens");
class BrowserRequest {
    type = 'request';
    headers;
    url;
    cookies;
    constructor(url, cookies = [], headers) {
        this.url = url;
        if (headers)
            this.headers = headers;
        this.cookies = cookies;
    }
    static is(val) {
        return val && val.type === 'request';
    }
}
exports.BrowserRequest = BrowserRequest;
class BrowserSpider {
    injector;
    constructor(injector) {
        this.injector = injector;
    }
    md5(text) {
        return (0, crypto_1.createHash)('md5').update(text).digest('base64');
    }
    isPage(val) {
        if (!val)
            return false;
        return !this.isHttpResponse(val) && !this.isHttpRequest(val);
    }
    isHttpResponse(val) {
        if (!val)
            return false;
        return Reflect.has(val, 'request');
    }
    isHttpRequest(val) {
        if (!val)
            return false;
        return Reflect.has(val, 'respond');
    }
    openInBrowser(req, handle, options = 'networkidle0', finish, fail) {
        return new rxjs_1.Observable((sub) => {
            this._createPage(req).then(page => {
                page.on('close', () => sub.complete());
                page.on('error', err => sub.error(err));
                this._openInBrowser(page, req, handle, sub, options, finish, fail).catch(e => sub.error(e));
                return page;
            });
        });
    }
    get puppeteerPool() {
        return this.injector.get(tokens_1.PUPPETEER_POOL);
    }
    async _createPage(req) {
        const browser = await this.puppeteerPool.acquire();
        this.puppeteerPool.release(browser);
        // const ctx = browser.defaultBrowserContext();
        // await ctx.overridePermissions(req.url, ['geolocation'])
        const page = await browser.newPage();
        await page.setCookie(...req.cookies);
        await page.setJavaScriptEnabled(true);
        await page.setRequestInterception(true);
        // await page.setUserAgent(
        //     "Mozilla/5.0 (Windows NT 6.0) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.36 Safari/536.5")
        page.removeAllListeners();
        await page.setViewport({
            width: 1325,
            height: 768
        });
        return page;
    }
    // 浏览器打开某链接
    async _openInBrowser(page, req, handle, sub, options, finish, fail) {
        const ignoreExtnames = [
            '.ico', '.png', '.jpeg', '.jpg', '.css', '.svg', '.bmp', '.gif', '.ttf', '.eot', '.woff', '.woff2',
            '.mp3', '.mp4'
        ];
        const waitRun = [];
        page.on('request', req => {
            const url = new url_1.URL(req.url());
            const path = url.pathname;
            const ext = (0, path_1.extname)(path).toLowerCase();
            if (ignoreExtnames.includes(ext)) {
                req.respond({
                    status: 200,
                    contentType: 'text/plain',
                    body: 'not found'
                });
            }
            else {
                req.continue();
            }
        });
        page.on('response', function (res) {
            const url = new url_1.URL(res.url());
            const path = url.pathname;
            const ext = (0, path_1.extname)(path).toLowerCase();
            if (ignoreExtnames.includes(ext)) {
                return;
            }
            if (ext === '.js') {
                return;
            }
            if (res.request().method().toUpperCase() === 'OPTIONS') {
                return;
            }
            waitRun.push(handle(res, sub));
        });
        await page.goto(req.url, {
            waitUntil: [options],
        }).catch(async (e) => {
            console.error(e.message);
            if (fail)
                await fail(page, e);
        });
        await Promise.all(waitRun);
        if (handle) {
            await handle(page, sub);
        }
        else {
            await this.delay(200);
        }
        if (finish) {
            await finish(sub);
        }
        await page.close().catch(e => { });
    }
    delay(ms) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                resolve();
            }, ms);
        });
    }
}
exports.BrowserSpider = BrowserSpider;
