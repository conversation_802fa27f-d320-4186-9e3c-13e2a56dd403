"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoreSpider = void 0;
const browser_spider_1 = require("./browser-spider");
const cheerio_1 = require("cheerio");
const redis_1 = require("../redis");
const typeorm_1 = require("@nger/typeorm");
const tokens_1 = require("../../tokens");
class CoreSpider extends browser_spider_1.BrowserSpider {
    type;
    platform;
    bf;
    v;
    total;
    get redisFactory() {
        return this.injector.get(redis_1.RedisFactory);
    }
    _redis;
    get redis() {
        if (this._redis)
            return this._redis;
        this._redis = this.redisFactory.create();
        return this._redis;
    }
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    constructor(injector) {
        super(injector);
        this.init();
    }
    load(html) {
        return (0, cheerio_1.load)(html);
    }
    get weiboUserPool() {
        return this.injector.get(tokens_1.WEIBO_USER_POOL);
    }
    get zhihuUserPool() {
        return this.injector.get(tokens_1.ZHIHU_USER_POOL);
    }
    async init() {
        try {
            if (this.v && this.total && this.bf) {
                const client = await this.redis;
                await client.bf.reserve(this.bf, this.v, this.total);
            }
        }
        catch (e) {
            if (e.message.endsWith('item exists')) {
                // exists
            }
            else {
                console.log(e);
            }
        }
    }
    async exists(key) {
        const client = await this.redis;
        return await client.bf.exists(this.bf, key);
    }
    async save(key) {
        const client = await this.redis;
        return await client.bf.add(this.bf, key);
    }
    createItem(url, data, type) {
        const item = {
            platform: this.platform,
            data,
            url,
            create_at: new Date()
        };
        return {
            type,
            payload: item
        };
    }
}
exports.CoreSpider = CoreSpider;
