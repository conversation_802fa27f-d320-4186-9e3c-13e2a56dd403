"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Spider = void 0;
const types_1 = require("../tasks/types");
const detail_task_1 = require("../tasks/detail.task");
const core_1 = require("./core");
const comment_task_1 = require("../tasks/comment.task");
const like_task_1 = require("../tasks/like.task");
const repostTimeline_task_1 = require("../tasks/repostTimeline.task");
class Spider extends core_1.CoreSpider {
    getUser() {
        return this.zhihuUserPool.acquire();
    }
    retry(fn, msg, times = 4, delay = Math.random() * 1000 * 30) {
        return new Promise(function (resolve, reject) {
            const tFn = function () {
                fn().then(resolve).catch((e) => {
                    if (times-- > 0) {
                        console.log(`${msg}--还有${times}次机会: ${e.message}`);
                        setTimeout(tFn, delay);
                    }
                    else {
                        reject(e);
                    }
                });
            };
            return tFn();
        });
    }
    async publishDetailLinks(urls) {
        const detail = this.injector.get(detail_task_1.DetailTask);
        const tasks = urls.map(payload => {
            return {
                type: this.platform,
                payload
            };
        });
        await detail.publishs(tasks);
        return tasks;
    }
    async publishDetail(url) {
        if (!types_1.Detail.isDetail(url)) {
            return;
        }
        const detail = this.injector.get(detail_task_1.DetailTask);
        await detail.publish({
            type: this.platform,
            payload: {
                ...url
            }
        });
        return { url };
    }
    async publishGetComment(id, aid, platform) {
        const commentTask = this.injector.get(comment_task_1.CommentTask);
        await commentTask.publish({
            type: 'comment',
            payload: {
                id, aid, platform
            }
        });
    }
    async publishGetLike(id, aid, platform) {
        const likeTask = this.injector.get(like_task_1.LikeTask);
        await likeTask.publish({
            type: 'like',
            payload: {
                id, aid, platform
            }
        });
    }
    async publishGetRepostTimeline(id, aid, platform) {
        const likeTask = this.injector.get(repostTimeline_task_1.RepostTimelineTask);
        await likeTask.publish({
            type: 'report-timeline',
            payload: {
                id, aid, platform
            }
        });
    }
}
exports.Spider = Spider;
