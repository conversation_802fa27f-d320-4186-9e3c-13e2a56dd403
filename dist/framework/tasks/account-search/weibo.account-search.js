"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeiboAccountSearch = void 0;
const core_1 = require("@nger/core");
const cheerio_1 = require("cheerio");
const spider_1 = require("../../spider");
let WeiboAccountSearch = class WeiboAccountSearch extends spider_1.Spider {
    async dead(msg) {
    }
    platform = 'weibo';
    type = 'account';
    async run(action) {
        console.log(action);
        const { payload } = action;
        const keyword = encodeURIComponent(payload.keyword);
        const url = `https://s.weibo.com/user?q=${keyword}&Refer=weibo_user`;
        const user = await this.weiboUserPool.acquire();
        if (user) {
            const headers = {};
            const cookies = user.cookies;
            this.weiboUserPool.release(user);
            const req = new spider_1.BrowserRequest(url, cookies, headers);
            return this.openInBrowser(req, async (page, sub) => {
                if (this.isPage(page)) {
                    const content = await page.content();
                    const $ = this.load(content);
                    const accounts = $('#pl_user_feedList .card-user-b');
                    accounts.map((index, element) => {
                        const $ = (0, cheerio_1.load)(element);
                        const avatarElement = $('div.avator > a > img');
                        const avatar = avatarElement[0].attribs['src'];
                        const nameElement = $('div.info > div > a');
                        const name = (0, cheerio_1.text)(nameElement).trim();
                        const link = nameElement[0].attribs['href'];
                        const descElement = $('div.info > p:nth-child(2)');
                        const desc = (0, cheerio_1.text)(descElement).trim();
                        const fansElement = $('div.info > p:nth-child(3) > span');
                        const fans = (0, cheerio_1.text)(fansElement).trim();
                        const data = {
                            avatar,
                            name,
                            link,
                            desc,
                            fans
                        };
                        sub.next(data);
                    });
                }
                else if (this.isHttpRequest(page)) {
                    page.continue();
                }
                else { }
            });
        }
        console.log(user);
    }
    createKey(url) {
        throw new Error("Method not implemented.");
    }
};
exports.WeiboAccountSearch = WeiboAccountSearch;
exports.WeiboAccountSearch = WeiboAccountSearch = __decorate([
    (0, core_1.Injectable)()
], WeiboAccountSearch);
