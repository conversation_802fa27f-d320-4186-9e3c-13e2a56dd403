"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZhihuAccountSearch = void 0;
const core_1 = require("@nger/core");
const axios_1 = __importDefault(require("axios"));
const spider_1 = require("../../spider");
let ZhihuAccountSearch = class ZhihuAccountSearch extends spider_1.Spider {
    async dead(msg) {
    }
    platform = 'weibo';
    type = 'account';
    async run(action) {
        console.log(action);
        const { payload } = action;
        const keyword = encodeURIComponent(payload.keyword);
        const url = `https://www.zhihu.com/search?type=people&q=${keyword}`;
        const user = await this.zhihuUserPool.acquire();
        this.zhihuUserPool.release(user);
        if (user) {
            const headers = {};
            const cookies = user.cookies;
            const req = new spider_1.BrowserRequest(url, cookies, headers);
            return this.openInBrowser(req, async (page, sub) => {
                if (this.isPage(page)) {
                    // await this.delay(1000 * 8)
                }
                else if (this.isHttpRequest(page)) {
                    const url = page.url();
                    if (url.startsWith('https://www.zhihu.com/api/v4/search_v3')) {
                        const headers = page.headers();
                        await axios_1.default.get(url, { headers }).then(res => {
                            const { data } = res.data;
                            data.map((item) => {
                                const { object } = item;
                                const avatar = object.avatar_url;
                                const name = object.name.replaceAll('<em>', '').replaceAll('</em>', '');
                                const desc = object.headline;
                                const link = object.url;
                                const fans = `${object.articles_count}文章,${object.answer_count}回答,${object.follower_count}关注者`;
                                const user = {
                                    avatar,
                                    name,
                                    link,
                                    desc,
                                    fans
                                };
                                sub.next(user);
                            });
                            page.respond({
                                status: res.status,
                                headers: res.headers,
                                contentType: 'application/json',
                                body: JSON.stringify(res.data)
                            });
                        }).catch(e => {
                            console.log(e.message);
                        });
                    }
                    else {
                        page.continue();
                    }
                }
                else { }
            });
        }
    }
    createKey(url) {
        throw new Error("Method not implemented.");
    }
};
exports.ZhihuAccountSearch = ZhihuAccountSearch;
exports.ZhihuAccountSearch = ZhihuAccountSearch = __decorate([
    (0, core_1.Injectable)()
], ZhihuAccountSearch);
