"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.accountProvicers = void 0;
const core_1 = require("@nger/core");
const spider_1 = require("../../spider");
const weibo_account_1 = require("./weibo.account");
exports.accountProvicers = [
    {
        provide: spider_1.CoreSpider,
        useFactory: (injector) => {
            return new weibo_account_1.WeiboAccountSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    }
];
