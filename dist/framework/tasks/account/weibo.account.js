"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeiboAccountSpider = void 0;
const axios_1 = __importDefault(require("axios"));
const rxjs_1 = require("rxjs");
const spider_1 = require("../../spider");
const const_1 = require("../const");
const types_1 = require("../types");
const account_task_1 = require("../account.task");
class WeiboAccountSpider extends spider_1.Spider {
    async dead(msg) {
        const task = this.injector.get(account_task_1.AccountTask);
        return (0, rxjs_1.from)(task.publish(msg));
    }
    bf = const_1.ARTICLE_DETAIL_KEY;
    v = const_1.ARTICLE_DETAIL_V;
    total = const_1.ARTICLE_DETAIL_TOTAL;
    platform = 'weibo';
    type = 'account';
    async scrollDown(page) {
        await page.evaluate(() => {
            return new Promise((resolve, reject) => {
                let totalHeight = 0;
                const distance = 100;
                const timer = setInterval(() => {
                    const scrollHeight = document.body.scrollHeight;
                    window.scrollBy(0, distance);
                    totalHeight += distance;
                    console.log(totalHeight);
                    if (totalHeight >= scrollHeight) {
                        clearInterval(timer);
                        resolve();
                    }
                }, 50);
            });
        });
    }
    async getUser() {
        let user = await this.weiboUserPool.acquire().catch(e => {
            return undefined;
        });
        while (!user) {
            await this.delay(10000 * 10 * Math.random());
            user = await this.weiboUserPool.acquire().catch(e => {
                return undefined;
            });
        }
        this.weiboUserPool.release(user);
        return user;
    }
    async run(action) {
        const { payload, type } = action;
        const keyword = payload.keyword;
        let url = keyword;
        if (url.startsWith('/')) {
            url = `https:${url}`;
        }
        let user = await this.getUser();
        if (user) {
            this.weiboUserPool.release(user);
            const headers = {};
            const cookies = user.cookies;
            const req = new spider_1.BrowserRequest(url, cookies, headers);
            return this.openInBrowser(req, async (page, sub) => {
                if (this.isPage(page)) {
                    const btn = await page.waitForSelector('#app div.woo-pop-wrap > span > button').catch(e => { });
                    if (btn) {
                        await btn.click();
                        await this.delay(200);
                    }
                }
                else if (this.isHttpRequest(page)) {
                    const url = page.url();
                    const headers = page.headers();
                    if (url.startsWith('https://weibo.com/ajax/statuses/mymblog')) {
                        await axios_1.default.get(url, { headers }).then(async (res) => {
                            const a = new URL(url);
                            const { data, ok } = res.data;
                            if (ok === 1 && data) {
                                const { list, since_id, total } = data;
                                const uid = a.searchParams.get('uid');
                                const p = a.searchParams.get('page');
                                const feature = a.searchParams.get('feature');
                                list.map((item) => {
                                    const { mblogid } = item;
                                    const link = `https://weibo.com/${uid}/${mblogid}`;
                                    const l = new types_1.DetailLink();
                                    l.href = link;
                                    l.platform = this.platform;
                                    sub.next(l);
                                });
                                await page.respond({
                                    status: res.status,
                                    headers: res.headers,
                                    contentType: 'application/json',
                                    body: JSON.stringify(res.data)
                                });
                                if (since_id) {
                                    await this.getMore(since_id, uid, parseInt(p) + 1, feature, headers, sub);
                                }
                            }
                        });
                        await this.delay(1000);
                    }
                    else {
                        await page.continue();
                    }
                }
                else { }
            }).pipe((0, rxjs_1.bufferTime)(1000), (0, rxjs_1.mergeMap)((list) => {
                return (0, rxjs_1.from)(this.publishDetailLinks(list).catch(e => console.log(e)));
            }));
        }
    }
    async getMore(since_id, uid, p, feature, headers, sub) {
        await this.delay(Math.random() * 500);
        const url = `https://weibo.com/ajax/statuses/mymblog?uid=${uid}&page=${p}&feature=${feature}&since_id=${since_id}`;
        return axios_1.default.get(url, { headers }).then(res => {
            const { data, ok } = res.data;
            if (ok === 1) {
                const { list, since_id, total } = data;
                list.map((item) => {
                    const { mblogid } = item;
                    const link = `https://weibo.com/${uid}/${mblogid}`;
                    const l = new types_1.DetailLink();
                    l.href = link;
                    l.platform = this.platform;
                    sub.next(l);
                });
                if (since_id && since_id.length > 0) {
                    return this.getMore(since_id, uid, p + 1, feature, headers, sub);
                }
                console.log({ data: res.data });
            }
            else {
                console.log('load error', res.data);
            }
        }).catch(e => {
            return this.getMore(since_id, uid, p, feature, headers, sub);
        });
    }
    createKey(url) {
        return this.md5(url);
    }
}
exports.WeiboAccountSpider = WeiboAccountSpider;
