"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.commentProvicers = void 0;
const core_1 = require("@nger/core");
const spider_1 = require("../../spider");
const weibo_comment_1 = require("./weibo.comment");
exports.commentProvicers = [
    {
        provide: spider_1.CoreSpider,
        useFactory: (injector) => {
            return new weibo_comment_1.WeiboCommentSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    }
];
