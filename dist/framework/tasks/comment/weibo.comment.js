"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeiboCommentSpider = void 0;
const typeorm_1 = require("@nger/typeorm");
const axios_1 = __importDefault(require("axios"));
const rxjs_1 = require("rxjs");
const news_1 = require("../../../entities/news");
const spider_1 = require("../../spider");
const comment_task_1 = require("../comment.task");
const error_1 = require("../error");
const nlp_service_1 = require("../nlp.service");
class WeiboCommentSpider extends spider_1.Spider {
    platform = 'weibo';
    type = 'comment';
    async getUser() {
        let user = await this.weiboUserPool.acquire().catch(e => {
            return undefined;
        });
        while (!user) {
            await this.delay(10000 * 10 * Math.random());
            user = await this.weiboUserPool.acquire().catch(e => {
                return undefined;
            });
        }
        this.weiboUserPool.release(user);
        return user;
    }
    async run(msg) {
        const { payload } = msg;
        const { id, aid, uid } = payload;
        let user = await this.getUser();
        if (user) {
            const db = this.injector.get(typeorm_1.Db);
            const article = await db.manager.findOne(news_1.News, { select: ['uid', 'id', 'comment'], where: { id } });
            const psize = 200;
            if (article) {
                article.comment = article.comment || [];
                await this.getComponents(aid, article.uid, 0, psize, article);
                if (article.comment.length > 0) {
                    const comments = article.comment.map((it) => {
                        if (it.children && it.children.length > 0) {
                            const children = it.children.map((it) => it.text_raw);
                            return [it.text_raw, ...children];
                        }
                        return [it.text_raw];
                    }).flat().filter((it) => it && it.length > 0);
                    if (comments.length > 0) {
                        const nlpService = this.injector.get(nlp_service_1.NlpService);
                        const cuts = await nlpService.cut(comments);
                        const sentas = await nlpService.senta(comments);
                        if (cuts && cuts.length > 0) {
                            article.comment_cut = cuts;
                            let total_comment_word_num = 0;
                            let total_comment_readablity = 0;
                            cuts.map((cut) => {
                                const [cut_res, word_num, readablity] = cut;
                                total_comment_word_num += word_num;
                                total_comment_readablity += readablity;
                            });
                            article.comment_readablity_avg = Math.round(100 * total_comment_readablity / cuts.length);
                            article.comment_word_num_avg = Math.round(100 * total_comment_word_num / cuts.length);
                        }
                        if (sentas && sentas.length > 0) {
                            article.comment_emotion = sentas;
                            let comment_positive_total = 0;
                            let comment_negative_total = 0;
                            let comment_negative_count = 0;
                            let comment_positive_count = 0;
                            sentas.map((emotion) => {
                                const { negative_probs, positive_probs, sentiment_key, sentiment_label } = emotion;
                                comment_positive_total += positive_probs;
                                comment_negative_total += negative_probs;
                                if (sentiment_label == 1) {
                                    comment_positive_count += 1;
                                }
                                else {
                                    comment_negative_count += 1;
                                }
                            });
                            article.comment_positive_avg = Math.round(comment_positive_total * 100 / sentas.length);
                            article.comment_negative_avg = Math.round(comment_negative_total * 100 / sentas.length);
                            article.comment_negative_count = comment_negative_count;
                            article.comment_positive_count = comment_positive_count;
                        }
                    }
                    await this.updateDb(article);
                }
            }
        }
    }
    async updateDb(article) {
        const db = this.injector.get(typeorm_1.Db);
        const { aid, id, ...data } = article;
        return await db.manager.update(news_1.News, article.id, { ...data }).catch(e => {
            const msg = e.message;
            if (msg.includes('duplicate key value violates unique constraint')) {
                return;
            }
            throw e;
        });
    }
    async dead(msg) {
        const task = this.injector.get(comment_task_1.CommentTask);
        return (0, rxjs_1.from)(task.publish(msg));
    }
    createKey(url) {
        throw new Error("Method not implemented.");
    }
    async getHeaders() {
        const user = await this.getUser();
        if (user) {
            const cookies = user.cookies;
            const headers = {
                cookie: cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; '),
                ['user-agent']: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36'
            };
            return { headers, user };
        }
        throw new error_1.UserLogoutError();
    }
    async getComponents(id, uid, max_id, psize, news) {
        const url = `https://weibo.com/ajax/statuses/buildComments`;
        const query = {};
        query.is_reload = 1;
        query.id = id;
        query.count = psize;
        query.uid = uid;
        query.is_show_bulletin = 2;
        if (max_id) {
            query.flow = 0;
            query.is_mix = 0;
            query.max_id = max_id;
        }
        else {
            query.is_mix = 0;
        }
        const _url = this.createUrl(url, query);
        const headers = await this.getHeaders();
        await axios_1.default.get(_url, {
            headers: headers.headers
        }).then(async (res) => {
            const { data, max_id, ok, trendsText } = res.data;
            if (trendsText) {
                console.log(trendsText);
            }
            if (ok == 1 && data.length > 0) {
                const components = await Promise.all(data.map(async (item) => {
                    if (item.comments.length > 0) {
                        await this.getChildComponents(item, uid, headers.headers, 0);
                    }
                    return item;
                }));
                news.comment.push(...components);
                if (max_id) {
                    await this.getComponents(id, uid, max_id, psize, news);
                }
            }
        }).catch(async (e) => {
            const message = e.message;
            const is414 = message.includes('Request failed with status code 414');
            if (is414) {
                throw new error_1.Error414();
            }
            const is400 = message.includes('Request failed with status code 400');
            if (is400) {
                return;
            }
            throw e;
        });
        this.weiboUserPool.release(headers.user);
    }
    createUrl(url, query) {
        const str = Object.keys(query).map(key => {
            return `${key}=${query[key]}`;
        }).join('&');
        return `${url}?${str}`;
    }
    getChildComponents(item, uid, headers, max_id = 0) {
        const url = `https://weibo.com/ajax/statuses/buildComments`;
        const query = {};
        query.is_reload = 1;
        query.id = item.id;
        query.is_show_bulletin = 2;
        query.is_mix = 1;
        query.fetch_level = 1;
        query.count = 100;
        query.uid = uid;
        if (max_id) {
            query.flow = 0;
            query.max_id = max_id;
        }
        else {
            query.max_id = 0;
        }
        const _url = this.createUrl(url, query);
        return this.retry(async () => {
            return axios_1.default.get(_url, {
                headers: headers
            }).then(async (res) => {
                const { data, ok, max_id } = res.data;
                if (ok === 1 && data.length > 0) {
                    item.children = item.children || [];
                    item.children.push(...data);
                    if (max_id && data.length > 0) {
                        await this.delay(1000 * 10 * Math.random());
                        return this.getChildComponents(item, uid, headers, max_id);
                    }
                }
            }).catch((e) => {
                const message = e.message;
                const is414 = message.includes('Request failed with status code 414');
                if (is414) {
                    throw new error_1.Error414();
                }
                const is400 = message.includes('Request failed with status code 400');
                if (is400) {
                    return;
                }
                throw e;
            });
        }, '获取子评论-getChildComponents');
    }
}
exports.WeiboCommentSpider = WeiboCommentSpider;
