"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.detailProvicers = void 0;
const core_1 = require("@nger/core");
const spider_1 = require("../../spider");
const weibo_detail_1 = require("./weibo.detail");
exports.detailProvicers = [
    {
        provide: spider_1.CoreSpider,
        useFactory: (injector) => {
            return new weibo_detail_1.WeiboDetailSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    }
];
