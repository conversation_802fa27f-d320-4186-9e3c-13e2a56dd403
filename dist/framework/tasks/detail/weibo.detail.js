"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeiboDetailSpider = void 0;
const core_1 = require("@nger/core");
const axios_1 = __importStar(require("axios"));
const cheerio_1 = require("cheerio");
const fs_1 = require("fs");
const path_1 = require("path");
const rxjs_1 = require("rxjs");
const news_1 = require("../../../entities/news");
const spider_1 = require("../../spider");
const fs_extra_1 = require("fs-extra");
const detail_task_1 = require("../detail.task");
const typeorm_1 = require("@nger/typeorm");
const error_1 = require("../error");
const nlp_service_1 = require("../nlp.service");
const message_1 = require("../../../v2/message");
class WeiboDetailSpider extends spider_1.Spider {
    platform = 'weibo';
    type = 'detail';
    async scrollDown(page) {
        await page.evaluate(() => {
            return new Promise((resolve, reject) => {
                let totalHeight = 0;
                const distance = 100;
                const timer = setInterval(() => {
                    const scrollHeight = document.body.scrollHeight;
                    window.scrollBy(0, distance);
                    totalHeight += distance;
                    if (totalHeight >= scrollHeight) {
                        clearInterval(timer);
                        resolve();
                    }
                }, 0);
            });
        });
    }
    async getComment(page) {
        let isEnd = false;
        if (!isEnd) {
            const content = await page.content();
            const $ = this.load(content);
            const more = $('.Bottom_text_1kFLe');
            const moreTip = (0, cheerio_1.text)(more).trim();
            if (moreTip.includes('没有更多内容了')) {
                isEnd = true;
            }
            await this.scrollDown(page);
        }
    }
    async getArticleDetail() {
    }
    async dead(msg) {
        const task = this.injector.get(detail_task_1.DetailTask);
        return (0, rxjs_1.from)(task.publish(msg));
    }
    async getUser() {
        let user = await this.weiboUserPool.acquire().catch(e => {
            return undefined;
        });
        while (!user) {
            await this.delay(10000 * 10 * Math.random());
            user = await this.weiboUserPool.acquire().catch(e => {
                return undefined;
            });
        }
        this.weiboUserPool.release(user);
        return user;
    }
    async getHeaders() {
        let user = await this.getUser();
        if (user) {
            const cookies = user.cookies;
            const headers = {
                cookie: cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; '),
                ['user-agent']: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36'
            };
            return { headers, user };
        }
        throw new error_1.UserLogoutError();
    }
    get systemMessage() {
        return this.injector.get(message_1.SystemMessage);
    }
    async run(msg) {
        const { payload } = msg;
        const link = payload.href;
        let user = await this.getHeaders();
        if (user) {
            const news = new news_1.News();
            news.type = 'article';
            news.comment = [];
            news.like = [];
            news.share = [];
            if (!link) {
                return;
            }
            const _url = new URL(link);
            const [, uid, mid] = _url.pathname.split('/');
            if (!mid) {
                throw new Error('文章ID无效: ' + link);
            }
            const redis = await this.redis;
            const isExisit = await redis.exists(`${this.platform}.${mid}`);
            if (isExisit) {
                const dataStr = JSON.stringify(payload);
                this.systemMessage.sendText(`detail`, {
                    title: '文章已存在',
                    content: dataStr
                }).subscribe();
                return;
            }
            return new rxjs_1.Observable((sub) => {
                const url = `https://weibo.com/ajax/statuses/show?id=${mid}`;
                axios_1.default.get(url, {
                    headers: user.headers
                }).then(async (res) => {
                    if (res.status === 400) {
                        sub.error(new error_1.Error400());
                        return;
                    }
                    if (res.status === 414) {
                        sub.error(new error_1.Error414());
                        return;
                    }
                    const { user: account, ok, error_code, message, ...content } = res.data;
                    if (ok == 1) {
                        news.platform = this.platform;
                        news.from = url;
                        content.id = `${content.id}`;
                        news.data = content;
                        news.user = account;
                        news.aid = content.id;
                        news.uid = account.id;
                        news.post_at = new Date(content.created_at);
                        news.readablity = 0;
                        news.word_num = 0;
                        news.cut = {};
                        news.keywords = payload.keyword || '';
                        news.negative_probs = 0;
                        news.positive_probs = 1;
                        news.sentiment_key = 'positive';
                        news.sentiment_label = 1;
                        // 分享情感
                        news.share_cut = [];
                        news.share_emotion = [];
                        news.share_readablity_avg = 0;
                        news.share_word_num_avg = 0;
                        news.share_positive_avg = 0;
                        news.share_negative_avg = 0;
                        news.share_negative_count = 0;
                        news.share_positive_count = 0;
                        news.comment_cut = [];
                        news.comment_emotion = [];
                        news.comment_readablity_avg = 0;
                        news.comment_positive_avg = 0;
                        news.comment_negative_avg = 0;
                        news.comment_negative_count = 0;
                        news.comment_positive_count = 0;
                        news.comment_word_num_avg = 0;
                        if (content.text_raw) {
                            const nlpService = this.injector.get(nlp_service_1.NlpService);
                            const cuts = await nlpService.cut([content.text_raw]);
                            const sentas = await nlpService.senta([content.text_raw]);
                            if (cuts && cuts.length > 0) {
                                const cut = cuts[0];
                                const [cut_res, word_num, readablity] = cut;
                                news.readablity = Math.round(100 * readablity);
                                news.word_num = word_num;
                                news.cut = cut_res;
                            }
                            if (sentas && sentas.length > 0) {
                                const emotion = sentas[0];
                                const { negative_probs, positive_probs, sentiment_key, sentiment_label } = emotion;
                                news.negative_probs = Math.round(100 * negative_probs);
                                news.positive_probs = Math.round(100 * positive_probs);
                                news.sentiment_key = sentiment_key;
                                news.sentiment_label = sentiment_label;
                            }
                        }
                        const saveNews = await this.saveDb(news);
                        await redis.set(`${this.platform}.${mid}`, 1);
                        const dataStr = JSON.stringify(payload);
                        this.systemMessage.sendText(`detail`, {
                            title: '爬取成功',
                            content: dataStr
                        }).subscribe();
                        if (saveNews) {
                            await this.publishGetLike(saveNews.id, saveNews.aid, this.platform);
                            await this.publishGetRepostTimeline(saveNews.id, saveNews.aid, this.platform);
                            await this.publishGetComment(saveNews.id, saveNews.aid, this.platform);
                        }
                        this.weiboUserPool.release(user.user);
                        sub.complete();
                    }
                    else {
                        this.systemMessage.sendText('detail.error', {
                            title: message,
                            content: message
                        }).subscribe();
                        sub.complete();
                        return;
                    }
                }).catch(async (e) => {
                    if (e instanceof axios_1.AxiosError) {
                        const response = e.response;
                        const code = e.code;
                        if (code === 'ETIMEDOUT') {
                            sub.error(new error_1.Error414());
                            return;
                        }
                        if (!response) {
                            sub.error(e);
                            return;
                        }
                        const data = response.data;
                        const status = response.status;
                        if (status == 400) {
                            sub.error(new error_1.Error400());
                            return;
                        }
                        if (data.includes('Request failed with status code 500')) {
                            sub.error(e);
                            return;
                        }
                        const is400 = data.includes('Request failed with status code 400');
                        if (is400) {
                            sub.error(new error_1.Error400());
                            return;
                        }
                        if (data.includes('only be seen by login user')) {
                            await this.weiboUserPool.destroy(user.user);
                            sub.error(new error_1.UserLogoutError());
                            return;
                        }
                        if (data.includes('Request failed with status code 414')) {
                            sub.error(new error_1.Error414());
                            return;
                        }
                    }
                    const error = e;
                    if (error?.response?.status === 504) {
                        sub.error(e);
                        return;
                    }
                    sub.error(e);
                });
            });
        }
        else {
            /**
            * 没有有效登陆用户时，停止运行，直到有登陆触发时，重新开始
            * */
            throw new error_1.UserLogoutError();
        }
    }
    async saveDb(article) {
        const db = this.injector.get(typeorm_1.Db);
        const old = await db.manager.findOne(news_1.News, {
            select: ['id', 'aid'],
            where: { aid: article.aid }
        });
        if (old) {
            article.id = old.id;
            return article;
        }
        else {
            return await db.manager.save(news_1.News, article).catch(e => {
                const msg = e.message;
                if (msg.includes('duplicate key value violates unique constraint')) {
                    return;
                }
                throw e;
            });
        }
    }
    async saveLocal(article) {
        const data = article.data;
        const id = data.id;
        if (id) {
            const root = this.injector.get(core_1.APP_ROOT);
            const created_at = new Date(data.created_at);
            const month = created_at.getMonth() + 1;
            const day = created_at.getDate();
            const hour = created_at.getHours();
            const filePath = (0, path_1.join)(root, 'data/weibo', `${month}`, `${day}`, `${hour}`);
            (0, fs_extra_1.ensureDirSync)(filePath);
            const stream = (0, fs_1.createWriteStream)((0, path_1.join)(filePath, `${id}.json`));
            if (data.ok == 0) {
                return;
            }
            else {
                return new Promise((resolve, reject) => {
                    stream.write(JSON.stringify(article, null, 2), (err) => {
                        if (err)
                            reject(err);
                        stream.close();
                        resolve();
                    });
                });
            }
        }
    }
    async createKey(url) {
        return this.md5(url);
    }
    strToInt(str) {
        return parseInt(str);
    }
}
exports.WeiboDetailSpider = WeiboDetailSpider;
