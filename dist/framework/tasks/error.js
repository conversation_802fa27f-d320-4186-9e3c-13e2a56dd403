"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Error400 = exports.Error414 = exports.UserLogoutError = void 0;
class UserLogoutError extends Error {
    constructor() {
        super(`没有可用登陆用户`);
    }
}
exports.UserLogoutError = UserLogoutError;
class Error414 extends Error {
    constructor() {
        super(`系统繁忙，请稍后再试`);
    }
}
exports.Error414 = Error414;
class Error400 extends Error {
    constructor() {
        super(`指定资源没有找到或已删除`);
    }
}
exports.Error400 = Error400;
