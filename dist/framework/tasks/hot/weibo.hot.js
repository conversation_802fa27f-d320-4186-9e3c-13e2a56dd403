"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeiboHotSpider = void 0;
const spider_1 = require("../../spider");
class WeiboHotSpider extends spider_1.Spider {
    dead(msg) {
        throw new Error("Method not implemented.");
    }
    run(msg) {
        throw new Error("Method not implemented.");
    }
    createKey(url) {
        throw new Error("Method not implemented.");
    }
}
exports.WeiboHotSpider = WeiboHotSpider;
