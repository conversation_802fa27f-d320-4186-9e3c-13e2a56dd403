"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.likeProvicers = void 0;
const core_1 = require("@nger/core");
const spider_1 = require("../../spider");
const weibo_like_1 = require("./weibo.like");
exports.likeProvicers = [
    {
        provide: spider_1.CoreSpider,
        useFactory: (injector) => {
            return new weibo_like_1.WeiboLikeSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    }
];
