"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeiboLikeSpider = void 0;
const typeorm_1 = require("@nger/typeorm");
const axios_1 = __importDefault(require("axios"));
const rxjs_1 = require("rxjs");
const news_1 = require("../../../entities/news");
const spider_1 = require("../../spider");
const error_1 = require("../error");
const like_task_1 = require("../like.task");
class WeiboLikeSpider extends spider_1.Spider {
    platform = 'weibo';
    type = 'like';
    async getUser() {
        let user = await this.weiboUserPool.acquire().catch(e => {
            return undefined;
        });
        while (!user) {
            await this.delay(10000 * 10 * Math.random());
            user = await this.weiboUserPool.acquire().catch(e => {
                return undefined;
            });
        }
        return user;
    }
    async run(msg) {
        const { payload } = msg;
        const { id, aid } = payload;
        let user = await this.getUser();
        if (user) {
            const p = 0;
            const psize = 200;
            const news = new news_1.News();
            news.id = id;
            news.aid = aid;
            news.like = news.like || [];
            const res = await this.getLikeShow(aid, p + 1, psize, news);
            if (res instanceof Error) {
                throw res;
            }
            await this.updateDb(news);
            this.weiboUserPool.release(user);
        }
    }
    async updateDb(article) {
        const db = this.injector.get(typeorm_1.Db);
        const { aid, id, like } = article;
        return await db.manager.update(news_1.News, article.id, { like }).catch(e => {
            const msg = e.message;
            if (msg.includes('duplicate key value violates unique constraint')) {
                return;
            }
            throw e;
        });
    }
    async getHeaders() {
        const user = await this.getUser();
        if (user) {
            const cookies = user.cookies;
            const headers = {
                cookie: cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; '),
                ['user-agent']: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36'
            };
            return { headers, user };
        }
        return;
    }
    async getLikeShow(id, page, psize, news) {
        const url = `https://weibo.com/ajax/statuses/likeShow?id=${id}&attitude_type=0&attitude_enable=1&page=${page}&count=${psize}`;
        const headers = await this.getHeaders();
        return this.retry(async () => {
            if (headers) {
                return axios_1.default.get(url, {
                    headers: headers.headers
                }).then(async (res) => {
                    const { data, ok, total_number } = res.data;
                    if (ok === -100) {
                        await this.weiboUserPool.destroy(headers.user);
                        return new error_1.UserLogoutError();
                    }
                    if (ok == 1 && data.length > 0) {
                        news.like.push(...data);
                        const current = (page - 1) * psize;
                        if (total_number > current) {
                            return this.getLikeShow(id, page + 1, psize, news);
                        }
                    }
                }).catch(e => {
                    const message = e.message;
                    const is414 = message.includes('Request failed with status code 414');
                    if (is414) {
                        throw new error_1.Error414();
                    }
                    const is400 = message.includes('Request failed with status code 400');
                    if (is400) {
                        return;
                    }
                    throw e;
                });
            }
        }, '获取点赞-getLikeShow');
    }
    async dead(msg) {
        const task = this.injector.get(like_task_1.LikeTask);
        return (0, rxjs_1.from)(task.publish(msg));
    }
    createKey(url) {
        throw new Error("Method not implemented.");
    }
}
exports.WeiboLikeSpider = WeiboLikeSpider;
