"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.listProvicers = void 0;
const core_1 = require("@nger/core");
const spider_1 = require("../../spider");
const weibo_list_1 = require("./weibo.list");
exports.listProvicers = [
    {
        provide: spider_1.CoreSpider,
        useFactory: (injector) => {
            return new weibo_list_1.WeiboListSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    },
];
