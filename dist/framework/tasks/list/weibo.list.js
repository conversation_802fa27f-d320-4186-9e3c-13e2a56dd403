"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeiboListSpider = void 0;
const cheerio_1 = require("cheerio");
const rxjs_1 = require("rxjs");
const spider_1 = require("../../spider");
const list_task_1 = require("../list.task");
const types_1 = require("../types");
const const_1 = require("../const");
const detail_task_1 = require("../detail.task");
const error_1 = require("../error");
const page_task_1 = require("../page.task");
const message_1 = require("../../../v2/message");
class <PERSON>boListSpider extends spider_1.Spider {
    async dead(msg) {
        const task = this.injector.get(list_task_1.ListTask);
        return (0, rxjs_1.from)(task.publish(msg));
    }
    bf = const_1.ARTICLE_DETAIL_KEY;
    v = const_1.ARTICLE_DETAIL_V;
    total = const_1.ARTICLE_DETAIL_TOTAL;
    platform = 'weibo';
    type = 'list';
    get list() {
        return this.injector.get(list_task_1.ListTask);
    }
    formatDate(startDate) {
        return this.format(startDate, 'yyyy-MM-dd-hh');
    }
    format(date, fmt) {
        const o = {
            "M+": date.getMonth() + 1, // 月份 
            "d+": date.getDate(), // 日 
            "h+": date.getHours(), // 小时 
            "m+": date.getMinutes(), // 分 
            "s+": date.getSeconds(), // 秒 
            "q+": Math.floor((date.getMonth() + 3) / 3), // 季度 
            "S": date.getMilliseconds() // 毫秒 
        };
        if (/(y+)/.test(fmt))
            fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (let k in o)
            if (new RegExp("(" + k + ")").test(fmt))
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    async getUser() {
        let user = await this.weiboUserPool.acquire().catch(e => {
            return undefined;
        });
        while (!user) {
            await this.delay(10000 * 10 * Math.random());
            user = await this.weiboUserPool.acquire().catch(e => {
                return undefined;
            });
        }
        this.weiboUserPool.release(user);
        return user;
    }
    async run(action) {
        const { payload, type } = action;
        const url = payload.url;
        console.log(`list url: ${url}`);
        let user = await this.getUser();
        if (user) {
            this.weiboUserPool.release(user);
            const cookies = user.cookies;
            const headers = {};
            const req = new spider_1.BrowserRequest(url, cookies, headers);
            return this.openInBrowser(req, async (page, sub) => {
                return this.handler(page, sub, user, payload);
            }, 'domcontentloaded', () => { }, (p, e) => {
                console.log(e);
            });
        }
    }
    get systemMessage() {
        return this.injector.get(message_1.SystemMessage);
    }
    async handler(page, sub, user, payload) {
        if (this.isPage(page)) {
            const url = page.url();
            if (url.startsWith('search_need_login')) {
                this.systemMessage.sendText('logout', { title: 'login.fail', content: `reason: search_need_login, url: ${page.url()},${user.username} logout` }).subscribe();
                await this.weiboUserPool.destroy(user);
                sub.error(new error_1.UserLogoutError());
                return;
            }
            const content = await page.content();
            if (content.includes('Sina Visitor System')) {
                this.systemMessage.sendText('logout', { title: 'login.fail', content: `reason: Sina Visitor System, url: ${page.url()},${user.username} logout` }).subscribe();
                await this.weiboUserPool.destroy(user);
                sub.error(new error_1.UserLogoutError());
                return;
            }
            if (content.includes('404 Page not found')) {
                this.systemMessage.sendText('404', { title: '抱歉，未找到', content: `${url}` }).subscribe();
                sub.error(new error_1.Error400());
                return;
            }
            if (content.includes('抱歉，未找到')) {
                this.systemMessage.sendText('404', { title: '抱歉，未找到', content: `${url}` }).subscribe();
                sub.error(new error_1.Error400());
                return;
            }
            const $ = this.load(content);
            const element = $('.loginBtn');
            if (element.length > 0) {
                // this.systemMessage.sendText('logout', { title: 'login.fail', content: `reason: loginBtn, url: ${page.url()},${user.username} logout` }).subscribe();
                // await this.weiboUserPool.destroy(user);
                sub.error(new error_1.UserLogoutError());
                return;
            }
            // #pl_feedlist_index > div.card-wrap > div > p
            // #pl_feedlist_index > div:nth-child(2) > div:nth-child(1) > div.card > div.card-feed > div.content > div.from > a:nth-child(1)
            // #pl_feedlist_index > div:nth-child(2) > div:nth-child(1) > div > div.card-feed > div.content > div.from > a:nth-child(1)
            const links = $('#pl_feedlist_index .card-feed');
            const detailSend = [];
            // #pl_feedlist_index > div:nth-child(1) > div:nth-child(9) > div > div.card-feed > div.content > div.from > a:nth-child(1)
            links.map((index, item) => {
                try {
                    const $ = (0, cheerio_1.load)(item);
                    const linkElement = $('.content > .from > a');
                    const link = linkElement[0]?.attribs['href'];
                    const d = new types_1.DetailLink();
                    const url = this.parseUrl(link);
                    if (url) {
                        d.platform = this.platform;
                        d.href = url;
                        d.keyword = payload.keyword;
                        detailSend.push(d);
                    }
                }
                catch (e) {
                    // console.error(e)
                }
            });
            if (detailSend.length > 0) {
                const detailTask = this.injector.get(detail_task_1.DetailTask);
                await detailTask.publishs(detailSend.map(payload => {
                    return {
                        type: 'detail',
                        payload
                    };
                }));
                this.systemMessage.sendText('list', { title: 'list.detail', content: `send detail task ${detailSend.length}` }).subscribe();
            }
            const pageSend = [];
            // feed_list_page_morelist s-scroll
            const moreList = $('ul[node-type="feed_list_page_morelist"] > li');
            moreList.map((index, el) => {
                const cur = el.attribs['class'] && el.attribs['class'].includes('cur');
                if (!cur) {
                    const $ = (0, cheerio_1.load)(el);
                    const a = $('a')[0];
                    const href = a.attribs['href'];
                    const url = this.parseUrl(`https://s.weibo.com${href}`);
                    pageSend.push(url);
                }
            });
            if (pageSend.length > 0) {
                const pageTask = this.injector.get(page_task_1.PageTask);
                await pageTask.publishs(pageSend.map(p => {
                    return {
                        type: 'page',
                        payload: {
                            url: p,
                            platform: this.platform,
                            keyword: payload.keyword
                        }
                    };
                }));
                this.systemMessage.sendText('list', { title: 'list.page', content: `send page task ${pageSend.length}` }).subscribe();
            }
            await this.delay(1000 * Math.random() * 6);
            sub.complete();
        }
        else if (this.isHttpResponse(page)) {
        }
        else {
            page.continue();
        }
    }
    parseUrl(url) {
        if (!url) {
            return;
        }
        if (url.startsWith('https')) {
            return url;
        }
        else {
            return `https:${url}`;
        }
    }
    createKey(href) {
        const url = new URL(href);
        const key = url.hostname + url.pathname;
        return this.md5(key);
    }
}
exports.WeiboListSpider = WeiboListSpider;
