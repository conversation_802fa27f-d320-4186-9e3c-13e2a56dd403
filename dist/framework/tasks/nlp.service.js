"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NlpService = void 0;
const core_1 = require("@nger/core");
const axios_1 = __importDefault(require("axios"));
let NlpService = class NlpService {
    get url() {
        return process.env['NLP_URL'] || 'http://*************:8082';
    }
    get cutUrl() {
        return this.url + '/cut';
    }
    get sentaUrl() {
        return this.url + '/senta';
    }
    retry(fn, data, times = 10, delay = Math.random() * 1000 * 10) {
        return new Promise(function (resolve, reject) {
            const tFn = function () {
                fn().then(resolve).catch((e) => {
                    if (times-- > 0) {
                        console.log(`还有${times}次机会`, data);
                        setTimeout(tFn, delay);
                    }
                    else {
                        reject(e);
                    }
                });
            };
            return tFn();
        });
    }
    cut(texts) {
        return this.retry(() => {
            return axios_1.default.post(this.cutUrl, { texts }, {
                headers: {
                    'content-type': 'application/json'
                },
                timeout: 0
            }).then(res => res.data);
        }, texts);
    }
    senta(texts) {
        return this.retry(() => {
            return axios_1.default.post(this.sentaUrl, {
                texts: texts
            }, {
                headers: {
                    'content-type': 'application/json'
                },
                timeout: 0
            }).then(res => res.data);
        }, texts);
    }
};
exports.NlpService = NlpService;
exports.NlpService = NlpService = __decorate([
    (0, core_1.Injectable)()
], NlpService);
