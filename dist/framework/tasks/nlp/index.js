"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.nlpProvicers = void 0;
const core_1 = require("@nger/core");
const spider_1 = require("../../spider");
const weibo_nlp_1 = require("./weibo.nlp");
exports.nlpProvicers = [
    {
        provide: spider_1.CoreSpider,
        useFactory: (injector) => {
            return new weibo_nlp_1.WeiboNlpSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    }
];
