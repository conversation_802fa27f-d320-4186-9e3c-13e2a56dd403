"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeiboNlpSpider = void 0;
const spider_1 = require("../../spider");
const nlp_service_1 = require("../nlp.service");
const typeorm_1 = require("@nger/typeorm");
const news_1 = require("../../../entities/news");
class WeiboNlpSpider extends spider_1.Spider {
    dead(msg) {
        throw new Error("Method not implemented.");
    }
    platform = 'weibo';
    type = 'nlp';
    async run(msg) {
        const payload = msg.payload;
        const { type, id, text } = payload;
        const nlpService = this.injector.get(nlp_service_1.NlpService);
        const db = this.injector.get(typeorm_1.Db);
        const updateNews = {};
        const cuts = await nlpService.cut(text);
        const sentas = await nlpService.senta(text);
        if (type === 'article') {
            if (cuts && cuts.length > 0) {
                const cut = cuts[0];
                const [cut_res, word_num, readablity] = cut;
                updateNews.readablity = Math.round(100 * readablity);
                updateNews.word_num = word_num;
                updateNews.cut = cut_res;
            }
            if (sentas && sentas.length > 0) {
                const emotion = sentas[0];
                const { negative_probs, positive_probs, sentiment_key, sentiment_label } = emotion;
                updateNews.negative_probs = Math.round(100 * negative_probs);
                updateNews.positive_probs = Math.round(100 * positive_probs);
                updateNews.sentiment_key = sentiment_key;
                updateNews.sentiment_label = sentiment_label;
            }
            await this.db.manager.update(news_1.News, id, updateNews);
            return;
        }
        if (type === 'share') {
            if (cuts && cuts.length > 0) {
                updateNews.share_cut = cuts;
                let total_share_word_num = 0;
                let total_share_readablity = 0;
                cuts.map((cut) => {
                    const [cut_res, word_num, readablity] = cut;
                    total_share_word_num += word_num;
                    total_share_readablity += readablity;
                });
                updateNews.share_readablity_avg = Math.round(100 * total_share_readablity / cuts.length);
                updateNews.share_word_num_avg = Math.round(100 * total_share_word_num / cuts.length);
            }
            if (sentas && sentas.length > 0) {
                updateNews.share_emotion = sentas;
                let share_positive_total = 0;
                let share_negative_total = 0;
                let share_negative_count = 0;
                let share_positive_count = 0;
                sentas.map((emotion) => {
                    const { negative_probs, positive_probs, sentiment_key, sentiment_label } = emotion;
                    share_positive_total += positive_probs;
                    share_negative_total += negative_probs;
                    if (sentiment_label == 1) {
                        share_positive_count += 1;
                    }
                    else {
                        share_negative_count += 1;
                    }
                });
                updateNews.share_positive_avg = Math.round(100 * share_positive_total / sentas.length);
                updateNews.share_negative_avg = Math.round(100 * share_negative_total / sentas.length);
                updateNews.share_negative_count = share_negative_count;
                updateNews.share_positive_count = share_positive_count;
            }
            await this.db.manager.update(news_1.News, id, updateNews);
            return;
        }
        if (type === 'comment') {
            if (cuts && cuts.length > 0) {
                updateNews.comment_cut = cuts;
                let total_comment_word_num = 0;
                let total_comment_readablity = 0;
                cuts.map((cut) => {
                    const [cut_res, word_num, readablity] = cut;
                    total_comment_word_num += word_num;
                    total_comment_readablity += readablity;
                });
                updateNews.comment_readablity_avg = Math.round(100 * total_comment_readablity / cuts.length);
                updateNews.comment_word_num_avg = Math.round(100 * total_comment_word_num / cuts.length);
            }
            if (sentas && sentas.length > 0) {
                updateNews.comment_emotion = sentas;
                let comment_positive_total = 0;
                let comment_negative_total = 0;
                let comment_negative_count = 0;
                let comment_positive_count = 0;
                sentas.map((emotion) => {
                    const { negative_probs, positive_probs, sentiment_key, sentiment_label } = emotion;
                    comment_positive_total += positive_probs;
                    comment_negative_total += negative_probs;
                    if (sentiment_label == 1) {
                        comment_positive_count += 1;
                    }
                    else {
                        comment_negative_count += 1;
                    }
                });
                updateNews.comment_positive_avg = Math.round(comment_positive_total * 100 / sentas.length);
                updateNews.comment_negative_avg = Math.round(comment_negative_total * 100 / sentas.length);
                updateNews.comment_negative_count = comment_negative_count;
                updateNews.comment_positive_count = comment_positive_count;
            }
            await this.db.manager.update(news_1.News, id, updateNews);
            return;
        }
    }
    createKey(url) {
        return this.md5(url);
    }
}
exports.WeiboNlpSpider = WeiboNlpSpider;
