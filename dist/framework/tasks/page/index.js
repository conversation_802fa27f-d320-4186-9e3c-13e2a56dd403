"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.pageProvicers = void 0;
const core_1 = require("@nger/core");
const spider_1 = require("../../spider");
const weibo_page_1 = require("./weibo.page");
exports.pageProvicers = [
    {
        provide: spider_1.CoreSpider,
        useFactory: (injector) => {
            return new weibo_page_1.WeiboPageSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    },
];
