"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.repostTimelineProvicers = void 0;
const core_1 = require("@nger/core");
const spider_1 = require("../../spider");
const weibo_repost_timeline_1 = require("./weibo.repost-timeline");
exports.repostTimelineProvicers = [
    {
        provide: spider_1.CoreSpider,
        useFactory: (injector) => {
            return new weibo_repost_timeline_1.WeiRepostTimelineSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    }
];
