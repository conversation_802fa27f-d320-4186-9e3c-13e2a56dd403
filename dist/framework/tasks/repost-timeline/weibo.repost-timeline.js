"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeiRepostTimelineSpider = void 0;
const typeorm_1 = require("@nger/typeorm");
const axios_1 = __importDefault(require("axios"));
const rxjs_1 = require("rxjs");
const news_1 = require("../../../entities/news");
const spider_1 = require("../../spider");
const error_1 = require("../error");
const nlp_service_1 = require("../nlp.service");
const repostTimeline_task_1 = require("../repostTimeline.task");
class WeiRepostTimelineSpider extends spider_1.Spider {
    platform = 'weibo';
    type = 'repost-timeline';
    async getHeaders() {
        const user = await this.getUser();
        if (user) {
            const cookies = user.cookies;
            const headers = {
                cookie: cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; '),
                ['user-agent']: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36'
            };
            this.weiboUserPool.release(user);
            return { headers, user };
        }
        throw new error_1.UserLogoutError();
    }
    async getUser() {
        let user = await this.weiboUserPool.acquire().catch(e => {
            return undefined;
        });
        while (!user) {
            await this.delay(10000 * 10 * Math.random());
            user = await this.weiboUserPool.acquire().catch(e => {
                return undefined;
            });
        }
        return user;
    }
    async run(msg) {
        const { payload } = msg;
        const { id, aid } = payload;
        let user = await this.getUser();
        const db = this.injector.get(typeorm_1.Db);
        const article = await db.manager.findOne(news_1.News, { select: ['uid', 'id', 'share'], where: { id } });
        const psize = 200;
        if (article && user) {
            article.share = article.share || [];
            await this.getRepostTimeline(aid, 0, psize, article);
            const shares = article.share.map((s) => s.text_raw).filter((it) => it && it.length > 0);
            if (shares.length > 0) {
                const nlpService = this.injector.get(nlp_service_1.NlpService);
                const cuts = await nlpService.cut(shares);
                const sentas = await nlpService.senta(shares);
                if (cuts && cuts.length > 0) {
                    article.share_cut = cuts;
                    let total_share_word_num = 0;
                    let total_share_readablity = 0;
                    cuts.map((cut) => {
                        const [cut_res, word_num, readablity] = cut;
                        total_share_word_num += word_num;
                        total_share_readablity += readablity;
                    });
                    article.share_readablity_avg = Math.round(100 * total_share_readablity / cuts.length);
                    article.share_word_num_avg = Math.round(100 * total_share_word_num / cuts.length);
                }
                if (sentas && sentas.length > 0) {
                    article.share_emotion = sentas;
                    let share_positive_total = 0;
                    let share_negative_total = 0;
                    let share_negative_count = 0;
                    let share_positive_count = 0;
                    sentas.map((emotion) => {
                        const { negative_probs, positive_probs, sentiment_key, sentiment_label } = emotion;
                        share_positive_total += positive_probs;
                        share_negative_total += negative_probs;
                        if (sentiment_label == 1) {
                            share_positive_count += 1;
                        }
                        else {
                            share_negative_count += 1;
                        }
                    });
                    article.share_positive_avg = Math.round(100 * share_positive_total / sentas.length);
                    article.share_negative_avg = Math.round(100 * share_negative_total / sentas.length);
                    article.share_negative_count = share_negative_count;
                    article.share_positive_count = share_positive_count;
                }
            }
            await this.updateDb(article);
            this.weiboUserPool.release(user);
        }
    }
    async updateDb(article) {
        const db = this.injector.get(typeorm_1.Db);
        const { aid, id, ...data } = article;
        return await db.manager.update(news_1.News, article.id, { ...data }).catch(e => {
            const msg = e.message;
            if (msg.includes('duplicate key value violates unique constraint')) {
                return;
            }
            throw e;
        });
    }
    async dead(msg) {
        const task = this.injector.get(repostTimeline_task_1.RepostTimelineTask);
        return (0, rxjs_1.from)(task.publish(msg));
    }
    createKey(url) {
        throw new Error("Method not implemented.");
    }
    async getRepostTimeline(id, page, psize, news) {
        const url = `https://weibo.com/ajax/statuses/repostTimeline?id=${id}&page=${page}&moduleID=feed&count=${psize}`;
        const headers = await this.getHeaders();
        return this.retry(async () => {
            return axios_1.default.get(url, {
                headers: headers.headers
            }).then(async (res) => {
                const { data, max_page, ok, total_number, empty_tip } = res.data;
                if (empty_tip) {
                    console.log(empty_tip);
                }
                if (ok === 1 && data.length > 0) {
                    news.share.push(...data);
                    if (max_page > page) {
                        await this.delay(1000 * Math.random() * 10);
                        return this.getRepostTimeline(id, page + 1, psize, news);
                    }
                }
            }).catch(async (e) => {
                const message = e.message;
                const is414 = message.includes('Request failed with status code 414');
                const is400 = message.includes('Request failed with status code 400');
                if (is400) {
                    return;
                }
                if (is414) {
                    throw new error_1.Error414();
                }
                throw e;
            });
        }, '获取转发-getRepostTimeline');
    }
}
exports.WeiRepostTimelineSpider = WeiRepostTimelineSpider;
