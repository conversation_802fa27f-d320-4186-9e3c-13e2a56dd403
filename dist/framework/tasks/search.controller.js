"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchController = void 0;
const core_1 = require("@nger/core");
const http_1 = require("@nger/http");
const weibo_account_search_1 = require("./account-search/weibo.account-search");
const zhihu_account_search_1 = require("./account-search/zhihu.account-search");
const account_task_1 = require("./account.task");
const search_task_1 = require("./search.task");
let SearchController = class SearchController {
    injector;
    get weiboAccount() {
        return this.injector.get(weibo_account_search_1.WeiboAccountSearch);
    }
    get zhihuAccount() {
        return this.injector.get(zhihu_account_search_1.ZhihuAccountSearch);
    }
    get searchTask() {
        return this.injector.get(search_task_1.SearchTask);
    }
    get accountTask() {
        return this.injector.get(account_task_1.AccountTask);
    }
    constructor(injector) {
        this.injector = injector;
    }
    account(body) {
        const { keyword, platform } = body;
        switch (platform) {
            case 'weibo':
                return this.weiboAccount.run({ type: 'search-account', payload: { keyword } }).then(res => {
                    if (res)
                        return this.getObservableList(res);
                    return [];
                });
            case 'zhihu':
                return this.zhihuAccount.run({ type: 'search-account', payload: { keyword } }).then(res => {
                    if (res)
                        return this.getObservableList(res);
                    return [];
                });
        }
        return body;
    }
    getObservableList(obs) {
        return new Promise((resolve, reject) => {
            const list = [];
            obs.subscribe({
                next: (res) => {
                    list.push(res);
                },
                complete: () => {
                    resolve(list);
                },
                error: (e) => reject(e)
            });
        });
    }
    index() {
        return 'hello world!';
    }
    async publish(body) {
        console.log(body);
        const { type, payload } = body;
        if (type === 'search') {
            await this.searchTask.publish(body);
        }
        if (type === 'account') {
            await this.accountTask.publish(body);
        }
        return { ok: 1 };
    }
    publishs(body) {
        const { type, list } = body;
        if (type === 'search') {
            return this.searchTask.publishs(list);
        }
        if (type === 'account') {
            return this.accountTask.publishs(list);
        }
    }
};
exports.SearchController = SearchController;
__decorate([
    (0, http_1.Post)('account'),
    __param(0, (0, http_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], SearchController.prototype, "account", null);
__decorate([
    (0, core_1.Get)('index'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], SearchController.prototype, "index", null);
__decorate([
    (0, http_1.Post)('send'),
    __param(0, (0, http_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SearchController.prototype, "publish", null);
__decorate([
    (0, http_1.Post)('publishs'),
    __param(0, (0, http_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], SearchController.prototype, "publishs", null);
exports.SearchController = SearchController = __decorate([
    (0, core_1.Controller)('@nger/task'),
    __metadata("design:paramtypes", [core_1.Injector])
], SearchController);
