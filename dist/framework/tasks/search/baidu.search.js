"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaiduSearchSpider = void 0;
const rxjs_1 = require("rxjs");
const spider_1 = require("../../spider");
const cheerio_1 = require("cheerio");
const types_1 = require("../types");
class BaiduSearchSpider extends spider_1.Spider {
    platform = 'baidu';
    type = 'search';
    async dead(msg) {
    }
    async run(msg) {
        const { payload, type } = msg;
        const keyword = encodeURIComponent(payload.keyword);
        const url = `https://www.baidu.com/s?rtt=1&bsst=1&cl=2&tn=news&rsv_dl=ns_pc&word=${keyword}`;
        const headers = {};
        const cookies = [];
        const req = new spider_1.BrowserRequest(url, cookies, headers);
        return this.openInBrowser(req, this.handler.bind(this)).pipe((0, rxjs_1.mergeMap)(url => {
            if (types_1.Link.isLink(url)) {
                const req = new spider_1.BrowserRequest(url.href, cookies, headers);
                return this.openInBrowser(req, async (page, sub) => {
                    // get page list
                    if (this.isPage(page)) {
                        const content = await page.content();
                        const $ = this.load(content);
                        const links = $('#content_left .result-op');
                        links.map((index, result) => {
                            try {
                                const $ = (0, cheerio_1.load)(result);
                                const link = $('h3>a')[0];
                                const href = link.attribs['href'];
                                const title = (0, cheerio_1.text)([link]).trim();
                                const span = $('.news-source_Xj4Dv span.c-color-gray');
                                const author = (0, cheerio_1.text)(span).trim();
                                const img = $('.news-source_Xj4Dv .c-img > img')[0];
                                const avatar = img?.attribs['src'];
                                sub.next(new types_1.Detail(href, title, author, avatar));
                            }
                            catch (e) {
                                console.error(e);
                            }
                        });
                        await this.delay(Math.random() * 1000);
                    }
                });
            }
            return (0, rxjs_1.of)(url);
        }), (0, rxjs_1.mergeMap)((res) => {
            return (0, rxjs_1.from)(this.publishDetail(res).catch(e => console.log(e)));
        }));
    }
    async handler(page, sub) {
        if (this.isPage(page)) {
            const title = await page.title();
            if (title.includes('百度安全验证')) {
                // 百度安全验证
                await page.waitForNavigation({ timeout: 0 });
            }
            const content = await page.content();
            const $ = this.load(content);
            const links = $('#content_left .result-op');
            links.map((index, result) => {
                try {
                    const $ = (0, cheerio_1.load)(result);
                    const link = $('h3>a')[0];
                    const href = link.attribs['href'];
                    const title = (0, cheerio_1.text)([link]).trim();
                    const span = $('.news-source_Xj4Dv span.c-color-gray');
                    const author = (0, cheerio_1.text)(span).trim();
                    const img = $('.news-source_Xj4Dv .c-img > img')[0];
                    const avatar = img?.attribs['src'];
                    sub.next(new types_1.Detail(href, title, author, avatar));
                }
                catch (e) {
                    console.error(e);
                }
            });
            const children = $('#page>div').children();
            const endIndex = children.length - 1;
            let isEnd = false;
            let lastUrl = ``;
            let current = 0;
            children.map((index, child) => {
                if (child.tagName !== 'a') {
                    current = index;
                }
                if (index === endIndex) {
                    const t = (0, cheerio_1.text)([child]);
                    if (t.includes('下一页')) {
                        isEnd = false;
                    }
                    else {
                        isEnd = true;
                    }
                }
            });
            if (!isEnd) {
                children.map((index, child) => {
                    if (child.tagName === 'a') {
                        const href = child.attribs['href'];
                        if (index !== endIndex && current < index) {
                            sub.next(new types_1.Link(`https://www.baidu.com${decodeURI(href)}`));
                        }
                        if (index === endIndex - 1) {
                            // 最后一个链接
                            lastUrl = `https://www.baidu.com${decodeURI(href)}`;
                        }
                    }
                });
                if (!isEnd && lastUrl) {
                    await page.goto(lastUrl);
                    await this.handler(page, sub);
                }
            }
        }
        else if (this.isHttpResponse(page)) {
            // 
        }
        else {
            page.continue();
        }
    }
    total = 0;
    async createKey(href) {
        const url = new URL(href);
        const entries = url.searchParams.entries();
        const params = [];
        for (let [key, val] of entries) {
            params.push(`${key}=${val}`);
        }
        const key = url.hostname + url.pathname + '?' + params.sort().join(';');
        return this.md5(key);
    }
}
exports.BaiduSearchSpider = BaiduSearchSpider;
