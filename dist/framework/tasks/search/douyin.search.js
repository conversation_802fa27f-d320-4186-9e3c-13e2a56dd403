"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DouyinSearchSpider = void 0;
const spider_1 = require("../../spider");
class DouyinSearchSpider extends spider_1.Spider {
    async dead(msg) {
    }
    platform = 'douyin';
    type = 'search';
    async run(msg) { }
    createKey(url) {
        throw new Error("Method not implemented.");
    }
}
exports.DouyinSearchSpider = DouyinSearchSpider;
