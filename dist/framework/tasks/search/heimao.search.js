"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HeimaoSearchSpider = void 0;
const spider_1 = require("../../spider");
const rxjs_1 = require("rxjs");
class HeimaoSearchSpider extends spider_1.Spider {
    async dead(msg) {
    }
    platform = 'heimao';
    type = 'search';
    async run(msg) {
        const { payload, type } = msg;
        const keyword = encodeURIComponent(payload.keyword);
        const url = `https://tousu.sina.com.cn/index/search/?keywords=${keyword}&t=0`;
        const headers = {};
        const cookies = [];
        const req = new spider_1.BrowserRequest(url, cookies, headers);
        return this.openInBrowser(req, async (page, sub) => {
            if (this.isPage(page)) {
                await page.waitForSelector('#u_search_total');
            }
            else if (this.isHttpResponse(page)) {
                const url = page.url();
                if (url.includes('https://tousu.sina.com.cn/api/index/s')) {
                    await page.text().then(text => {
                        try {
                            const res = text.match(/try\{.*?\((.*)\)\;\}/i);
                            if (res && res.length > 0) {
                                const data = JSON.parse(res[1]);
                                return sub.next({
                                    data,
                                    url
                                });
                            }
                            else {
                                throw new Error('text parse error');
                            }
                        }
                        catch (e) {
                            console.error(e);
                            throw e;
                        }
                    });
                }
            }
            else {
                page.continue();
            }
        }).pipe((0, rxjs_1.switchMap)(source => {
            const data = source.data;
            return (0, rxjs_1.of)(data).pipe((0, rxjs_1.map)((res) => res.result), (0, rxjs_1.map)((res) => {
                const { status, timestamp, data } = res;
                const { code, msg } = status;
                if (code === 0) {
                    const lists = data.lists;
                    const result = lists.map((item) => {
                        const { main, author } = item;
                        const data = {
                            ...main,
                            author
                        };
                        return this.createItem(source.url, data, 'nlp');
                    });
                    return result;
                }
                throw new Error(msg);
            }));
        }));
    }
    createKey(url) {
        throw new Error("Method not implemented.");
    }
}
exports.HeimaoSearchSpider = HeimaoSearchSpider;
