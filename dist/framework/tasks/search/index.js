"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchProvicers = void 0;
const core_1 = require("@nger/core");
const spider_1 = require("../../spider");
const weibo_search_1 = require("./weibo.search");
exports.searchProvicers = [
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new HeimaoSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // },
    {
        provide: spider_1.CoreSpider,
        useFactory: (injector) => {
            return new weibo_search_1.WeiboSearchSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    },
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new BaiduSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // },
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new DouyinSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // },
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new QqNewSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // },
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new ToutiaoSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // },
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new WechatSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // },
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new ZhihuSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // }
];
