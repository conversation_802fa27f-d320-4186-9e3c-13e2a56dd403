"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QqNewSearchSpider = void 0;
const spider_1 = require("../../spider");
class QqNewSearchSpider extends spider_1.Spider {
    async dead(msg) {
    }
    platform = 'qq-new';
    type = 'search';
    async run(action) { }
    createKey(url) {
        throw new Error("Method not implemented.");
    }
}
exports.QqNewSearchSpider = QqNewSearchSpider;
