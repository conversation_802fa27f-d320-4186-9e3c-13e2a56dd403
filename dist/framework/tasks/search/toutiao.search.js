"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToutiaoSearchSpider = void 0;
const cheerio_1 = require("cheerio");
const rxjs_1 = require("rxjs");
const spider_1 = require("../../spider");
const types_1 = require("../types");
class ToutiaoSearchSpider extends spider_1.Spider {
    async dead(msg) {
    }
    platform = 'toutiao';
    type = 'search';
    async run(action) {
        const { payload, type } = action;
        const keyword = encodeURIComponent(payload.keyword);
        const url = `https://so.toutiao.com/search?dvpf=pc&source=search_subtab_switch&keyword=${keyword}&pd=information&action_type=search_subtab_switch&from=news&cur_tab_title=news&page_num=0&search_id=`;
        const headers = {};
        const cookies = [];
        const req = new spider_1.BrowserRequest(url, cookies, headers);
        return this.openInBrowser(req, this.handler.bind(this)).pipe((0, rxjs_1.mergeMap)((link) => {
            if (types_1.Link.isLink(link)) {
                const req = new spider_1.BrowserRequest(link.href, cookies, headers);
                return this.openInBrowser(req, async (page, sub) => {
                    if (this.isPage(page)) {
                        const content = await page.content();
                        const $ = this.load(content);
                        const links = $('.s-result-list .result-content');
                        const end = links.length - 1;
                        links.map((index, item) => {
                            try {
                                if (index === end) {
                                    return;
                                }
                                const $ = (0, cheerio_1.load)(item);
                                const linkElement = $('.cs-header>div>a');
                                const link = linkElement[0]?.attribs['href'];
                                if (link) {
                                    const title = (0, cheerio_1.text)(linkElement).trim();
                                    const userElement = $('.cs-source-content .text-ellipsis');
                                    const avatar = ``;
                                    const author = (0, cheerio_1.text)(userElement).trim();
                                    const detail = new types_1.Detail(`https://so.toutiao.com${link}`, title, author, avatar);
                                    sub.next(detail);
                                }
                            }
                            catch (e) {
                                console.error(e);
                            }
                        });
                    }
                    await this.delay(Math.random() * 1000);
                });
            }
            return (0, rxjs_1.of)(link);
        }), (0, rxjs_1.mergeMap)((link) => {
            this.total += 1;
            return (0, rxjs_1.from)(this.publishDetail(link).catch(e => console.error(e)));
        }));
    }
    total = 0;
    async handler(page, sub) {
        if (this.isPage(page)) {
            await page.waitForSelector('.s-result-list').catch(e => sub.error(e));
            const content = await page.content();
            const $ = this.load(content);
            const links = $('.s-result-list .result-content');
            const end = links.length - 1;
            links.map((index, item) => {
                try {
                    if (index === end) {
                        return;
                    }
                    const $ = (0, cheerio_1.load)(item);
                    // #s-dom-8660d6d0 > div > div > div > div.cs-view.pad-bottom-3.cs-view-block.cs-text.align-items-center.cs-header > div > a
                    const linkElement = $('.cs-header>div>a');
                    const link = linkElement[0]?.attribs['href'];
                    if (link) {
                        const title = (0, cheerio_1.text)(linkElement).trim();
                        const userElement = $('.cs-source-content .text-ellipsis');
                        const avatar = ``;
                        const author = (0, cheerio_1.text)(userElement).trim();
                        const detail = new types_1.Detail(`https://so.toutiao.com${link}`, title, author, avatar);
                        sub.next(detail);
                    }
                }
                catch (e) {
                    console.error(e);
                }
            });
            const children = $('div.cs-pagination').children();
            const endIndex = children.length - 1;
            let isEnd = false;
            let lastUrl = ``;
            let current = 0;
            children.map((index, child) => {
                if (child.attribs['class'].includes('text-darker-inverse')) {
                    current = index;
                }
                if (index === endIndex) {
                    const t = (0, cheerio_1.text)([child]);
                    if (t.includes('下一页')) {
                        isEnd = false;
                    }
                    else {
                        isEnd = true;
                    }
                }
            });
            if (!isEnd) {
                children.map((index, child) => {
                    if (child.tagName === 'a') {
                        const href = child.attribs['href'];
                        if (index !== endIndex && current < index) {
                            sub.next(new types_1.Link(decodeURI(href)));
                        }
                        if (index === endIndex - 1) {
                            // 最后 一个 链接
                            lastUrl = decodeURI(href);
                        }
                    }
                });
                if (!isEnd && lastUrl) {
                    await page.goto(lastUrl);
                    await this.handler(page, sub);
                }
            }
        }
        else if (this.isHttpResponse(page)) { }
        else {
            page.continue();
        }
        await this.delay(Math.random() * 1000);
    }
    async createKey(href) {
        const url = new URL(href);
        const jumpUrl = url.searchParams.get('url');
        if (jumpUrl) {
            const jumpKey = this.getJumpUrlKey(jumpUrl);
            const key = this.md5(jumpKey);
            return key;
        }
        return '';
    }
    getJumpUrlKey(href) {
        const url = new URL(href);
        const key = url.hostname + url.pathname;
        return this.md5(key);
    }
}
exports.ToutiaoSearchSpider = ToutiaoSearchSpider;
