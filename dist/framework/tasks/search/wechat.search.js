"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WechatSearchSpider = void 0;
const cheerio_1 = require("cheerio");
const rxjs_1 = require("rxjs");
const spider_1 = require("../../spider");
const types_1 = require("../types");
class WechatSearchSpider extends spider_1.Spider {
    async dead(msg) {
    }
    platform = 'wechat';
    type = 'search';
    async run(action) {
        const { payload, type } = action;
        const keyword = encodeURIComponent(payload.keyword);
        const url = `https://weixin.sogou.com/weixin?p=01030402&query=${keyword}&type=2&ie=utf8`;
        const user = await this.getUser();
        if (user) {
            const cookies = user.cookies;
            const headers = {};
            const req = new spider_1.BrowserRequest(url, cookies, headers);
            return this.openInBrowser(req, this.handler.bind(this)).pipe((0, rxjs_1.mergeMap)(link => {
                if (types_1.Link.isLink(link)) {
                    const req = new spider_1.BrowserRequest(link.href, cookies, headers);
                    return this.openInBrowser(req, async (page, sub) => {
                        if (this.isPage(page)) {
                            await this.delay(1000 * 60 * 3);
                        }
                    });
                }
                return (0, rxjs_1.of)(link);
            }, 1));
        }
        else {
            return;
        }
    }
    async handler(page, sub) {
        if (this.isPage(page)) {
            const content = await page.content();
            const $ = this.load(content);
            const children = $('#pagebar_container').children();
            const endIndex = children.length - 1;
            let isEnd = false;
            let lastUrl = ``;
            let current = 0;
            children.map((index, child) => {
                if (child.tagName === 'span') {
                    current = index;
                }
                if (index === endIndex) {
                    const t = (0, cheerio_1.text)([child]);
                    if (t.includes('下一页')) {
                        isEnd = false;
                    }
                    else {
                        isEnd = true;
                    }
                }
            });
            if (!isEnd) {
                children.map((index, child) => {
                    if (child.tagName === 'a') {
                        const href = child.attribs['href'];
                        if (index !== endIndex && current < index) {
                            sub.next(new types_1.Link(`https://weixin.sogou.com/weixin${decodeURI(href)}`));
                        }
                        if (index === endIndex - 1) {
                            // 最后 一个 链接
                            lastUrl = decodeURI(href);
                        }
                    }
                });
                if (!isEnd && lastUrl) {
                    await page.goto(lastUrl);
                    await this.handler(page, sub);
                }
            }
        }
        else if (this.isHttpResponse(page)) { }
        else {
            page.continue();
        }
        await this.delay(Math.random() * 1000);
    }
    async createKey(url) {
        return ``;
    }
}
exports.WechatSearchSpider = WechatSearchSpider;
