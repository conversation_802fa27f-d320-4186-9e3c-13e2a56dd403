"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeiboSearchSpider = void 0;
const cheerio_1 = require("cheerio");
const rxjs_1 = require("rxjs");
const spider_1 = require("../../spider");
const list_task_1 = require("../list.task");
const types_1 = require("../types");
const const_1 = require("../const");
const error_1 = require("../error");
const promises_1 = require("fs/promises");
const path_1 = require("path");
const core_1 = require("@nger/core");
const fs_extra_1 = require("fs-extra");
const search_task_1 = require("../search.task");
const message_1 = require("../../../v2/message");
class WeiboSearchSpider extends spider_1.Spider {
    async dead(msg) {
        const task = this.injector.get(search_task_1.SearchTask);
        return (0, rxjs_1.from)(task.publish(msg));
    }
    bf = const_1.ARTICLE_DETAIL_KEY;
    v = const_1.ARTICLE_DETAIL_V;
    total = const_1.ARTICLE_DETAIL_TOTAL;
    platform = 'weibo';
    type = 'search';
    get list() {
        return this.injector.get(list_task_1.ListTask);
    }
    formatDate(startDate) {
        return this.format(startDate, 'yyyy-MM-dd-hh');
    }
    format(date, fmt) {
        const o = {
            "M+": date.getMonth() + 1, // 月份 
            "d+": date.getDate(), // 日 
            "h+": date.getHours(), // 小时 
            "m+": date.getMinutes(), // 分 
            "s+": date.getSeconds(), // 秒 
            "q+": Math.floor((date.getMonth() + 3) / 3), // 季度 
            "S": date.getMilliseconds() // 毫秒 
        };
        if (/(y+)/.test(fmt))
            fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (let k in o)
            if (new RegExp("(" + k + ")").test(fmt))
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    createUrl(keyword, startDate, endDate) {
        let url = `https://s.weibo.com/weibo?q=${encodeURIComponent(keyword)}&Refer=g&typeall=1&`;
        if (startDate) {
            let timescope = `custom:${startDate}`;
            if (endDate) {
                timescope += `:${endDate}`;
            }
            url += `&timescope=${timescope}`;
        }
        else {
            if (endDate) {
                let timescope = `custom:`;
                timescope += `:${endDate}`;
                url += `&timescope=${timescope}`;
            }
        }
        return url;
    }
    get systemMessage() {
        return this.injector.get(message_1.SystemMessage);
    }
    async run(action) {
        const { payload, type } = action;
        if (!payload.keyword) {
            return;
        }
        if (payload.keyword.length === 0) {
            return;
        }
        const keyword = payload.keyword;
        const urls = [];
        const step = payload.step || 12;
        if (payload.time) {
            let [start, end] = payload.time;
            let startDate = new Date(start);
            const maxEndDate = new Date(end);
            if (step > 0) {
                let endDate = new Date(startDate.getTime() + step * 60 * 60 * 1000);
                while (endDate < maxEndDate) {
                    urls.push(this.createUrl(keyword, this.formatDate(startDate), this.formatDate(endDate)));
                    startDate = endDate;
                    endDate = new Date(startDate.getTime() + step * 60 * 60 * 1000);
                }
                urls.push(this.createUrl(keyword, this.formatDate(startDate), this.formatDate(endDate)));
            }
            else {
                urls.push(this.createUrl(keyword, this.formatDate(startDate), this.formatDate(maxEndDate)));
            }
        }
        else {
            urls.push(this.createUrl(keyword));
        }
        this.systemMessage.send({
            type: 'search',
            data: { urls, payload }
        }).subscribe();
        const listTask = this.injector.get(list_task_1.ListTask);
        await listTask.publishs(urls.map(url => {
            return { type: 'list', payload: { url, platform: this.platform, keyword: payload.keyword } };
        }));
        return;
    }
    async handler(page, sub, user, hasPage = false, payload) {
        if (this.isPage(page)) {
            const url = page.url();
            if (url.startsWith('search_need_login')) {
                await this.weiboUserPool.destroy(user);
                throw new error_1.UserLogoutError();
            }
            const content = await page.content();
            if (content.includes('404 Page not found')) {
                throw new error_1.Error400();
            }
            const hash = this.md5(url);
            const root = this.injector.get(core_1.APP_ROOT);
            (0, fs_extra_1.ensureDirSync)((0, path_1.join)(root, 'data', this.platform));
            await (0, promises_1.writeFile)((0, path_1.join)(root, 'data', this.platform, `${hash}`), content);
            const $ = this.load(content);
            const element = $('.loginBtn');
            if (element.length > 0) {
                await this.weiboUserPool.destroy(user);
                throw new error_1.UserLogoutError();
            }
            // #pl_feedlist_index > div:nth-child(2) > div:nth-child(1) > div > div.card-feed > div.content > div.from > a:nth-child(1)
            const links = $('#pl_feedlist_index .card-feed');
            const waitSend = [];
            links.map((index, item) => {
                try {
                    const $ = (0, cheerio_1.load)(item);
                    const linkElement = $('div.content > .from > a');
                    const link = linkElement[0]?.attribs['href'];
                    const d = new types_1.DetailLink();
                    const url = this.parseUrl(link);
                    if (url) {
                        d.platform = this.platform;
                        d.href = url;
                        d.keyword = payload.keyword;
                        waitSend.push(d);
                    }
                }
                catch (e) {
                    // console.error(e)
                }
            });
            sub.next(waitSend);
            if (hasPage) {
                const moreList = $('ul[node-type="feed_list_page_morelist"] > li');
                moreList.map((index, el) => {
                    const cur = el.attribs['class'] && el.attribs['class'].includes('cur');
                    if (!cur) {
                        const $ = (0, cheerio_1.load)(el);
                        const a = $('a')[0];
                        const href = a.attribs['href'];
                        const url = this.parseUrl(`https://s.weibo.com${href}`);
                        sub.next(new types_1.Link(url));
                    }
                });
            }
            await this.delay(1000 * Math.random() * 6);
        }
        else if (this.isHttpResponse(page)) {
        }
        else {
            page.continue();
        }
    }
    parseUrl(url) {
        if (!url) {
            return;
        }
        if (url.startsWith('https')) {
            return url;
        }
        else {
            return `https:${url}`;
        }
    }
    createKey(href) {
        const url = new URL(href);
        const key = url.hostname + url.pathname;
        return this.md5(key);
    }
}
exports.WeiboSearchSpider = WeiboSearchSpider;
