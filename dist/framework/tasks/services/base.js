"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseService = exports.ResponseError = void 0;
const puppeteer_1 = require("@nger/puppeteer");
const axios_1 = __importDefault(require("axios"));
const crypto_1 = require("crypto");
const puppeteer_2 = require("puppeteer");
const cheerio_1 = require("cheerio");
class ResponseError extends Error {
    res;
    constructor(res) {
        super(res.statusText);
        this.res = res;
    }
}
exports.ResponseError = ResponseError;
let browser;
async function getBrowser(config) {
    if (browser)
        return browser;
    browser = (0, puppeteer_2.launch)(config);
    return browser;
}
class BaseService {
    injector;
    get config() {
        return this.injector.get(puppeteer_1.CONFIG);
    }
    browser;
    constructor(injector) {
        this.injector = injector;
    }
    async open(url, handler) {
        this.browser = await getBrowser(this.config);
        const ctx = this.browser.defaultBrowserContext();
        await ctx.overridePermissions(url, ['geolocation']);
        const page = await this.browser.newPage();
        const cookies = await this.getCanUseCookies();
        page.setCookie(...cookies);
        page.setJavaScriptEnabled(true);
        page.setViewport({
            width: 1325,
            height: 768
        });
        await page.goto(url, {
            waitUntil: ['networkidle2']
        });
        const result = await handler(page);
        await page.close();
        return result;
    }
    async parseHtml(content) {
        return (0, cheerio_1.load)(content);
    }
    async getCanUseCookies() {
        return [];
    }
    async get(url, params) {
        return axios_1.default.get(url, {
            params: params,
            headers: {}
        }).then(res => {
            if (res.status === 200) {
                return res.data;
            }
            else {
                throw new ResponseError(res);
            }
        });
    }
    async post(url, body) {
        return axios_1.default.post(url, body, {
            headers: {}
        }).then(res => {
            if (res.status === 200) {
                return res.data;
            }
            else {
                throw new ResponseError(res);
            }
        });
    }
    sha256() {
        return (0, crypto_1.createHash)('sha256');
    }
    randomStr(max) {
        let random_str = '';
        const base_str = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const length = base_str.length;
        for (let i = 0; i < max; i++) {
            const index = Math.floor(Math.random() * length);
            random_str += base_str[index];
        }
        return random_str;
    }
    formatUrl(url, pre = 'https:') {
        if (url.startsWith('http')) {
            return url;
        }
        return `${pre}${url}`;
    }
}
exports.BaseService = BaseService;
