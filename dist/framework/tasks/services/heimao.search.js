"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HeimaoSearchSpider = void 0;
const spider_1 = require("../../spider");
const rxjs_1 = require("rxjs");
class HeimaoSearchSpider extends spider_1.Spider {
    platform = 'heimao';
    type = 'search';
    run(action) {
        if (action.data) {
            const { payload, type } = action.data;
            const keyword = encodeURIComponent(payload.keyword);
            const url = `https://tousu.sina.com.cn/index/search/?keywords=${keyword}&t=0`;
            const headers = {};
            const cookies = [];
            const req = new spider_1.BrowserRequest(url, cookies, headers);
            this.openInBrowser(req, async (page) => {
                await page.waitForSelector('#u_search_total');
            }).pipe((0, rxjs_1.filter)(res => res.url.includes('https://tousu.sina.com.cn/api/index/s')), (0, rxjs_1.map)(source => {
                const text = source.text;
                const url = source.url;
                try {
                    const res = text.match(/try\{.*?\((.*)\)\;\}/i);
                    if (res && res.length > 0) {
                        const data = JSON.parse(res[1]);
                        return {
                            data,
                            url
                        };
                    }
                    else {
                        throw new Error('text parse error');
                    }
                }
                catch (e) {
                    console.error(e);
                    throw e;
                }
            }), (0, rxjs_1.switchMap)(source => {
                const data = source.data;
                return (0, rxjs_1.of)(data).pipe((0, rxjs_1.map)((res) => res.result), (0, rxjs_1.map)((res) => {
                    const { status, timestamp, data } = res;
                    const { code, msg } = status;
                    if (code === 0) {
                        const lists = data.lists;
                        const result = lists.map((item) => {
                            const { main, author } = item;
                            const data = {
                                ...main,
                                author
                            };
                            return this.createItem(source.url, data, 'nlp');
                        });
                        return result;
                    }
                    throw new Error(msg);
                }));
            })).subscribe({
                complete: () => {
                    action.ack();
                },
                error: (e) => {
                    console.error(e.message);
                    action.reject();
                }
            });
        }
    }
}
exports.HeimaoSearchSpider = HeimaoSearchSpider;
