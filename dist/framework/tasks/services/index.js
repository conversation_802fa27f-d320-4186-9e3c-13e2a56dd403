"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceProvicers = void 0;
const core_1 = require("@nger/core");
const spider_1 = require("../../spider");
const baidu_search_1 = require("./baidu.search");
const douyin_search_1 = require("./douyin.search");
const heimao_search_1 = require("./heimao.search");
const qq_new_search_1 = require("./qq-new.search");
const toutiao_search_1 = require("./toutiao.search");
const wechat_search_1 = require("./wechat.search");
const weibo_search_1 = require("./weibo.search");
const zhihu_search_1 = require("./zhihu.search");
exports.serviceProvicers = [
    {
        provide: spider_1.Spider,
        useFactory: (injector) => {
            return new heimao_search_1.HeimaoSearchSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    },
    {
        provide: spider_1.Spider,
        useFactory: (injector) => {
            return new weibo_search_1.WeiboSearchSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    },
    {
        provide: spider_1.Spider,
        useFactory: (injector) => {
            return new baidu_search_1.BaiduSearchSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    },
    {
        provide: spider_1.Spider,
        useFactory: (injector) => {
            return new douyin_search_1.DouyinSearchSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    },
    {
        provide: spider_1.Spider,
        useFactory: (injector) => {
            return new qq_new_search_1.QqNewSearchSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    },
    {
        provide: spider_1.Spider,
        useFactory: (injector) => {
            return new toutiao_search_1.ToutiaoSearchSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    },
    {
        provide: spider_1.Spider,
        useFactory: (injector) => {
            return new wechat_search_1.WechatSearchSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    },
    {
        provide: spider_1.Spider,
        useFactory: (injector) => {
            return new zhihu_search_1.ZhihuSearchSpider(injector);
        },
        deps: [core_1.Injector],
        multi: true
    }
];
