"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Task = void 0;
const util_1 = require("./util");
const core_1 = require("../spider/core");
const error_1 = require("./error");
const message_1 = require("../../v2/message");
class Task {
    injector;
    name;
    mq;
    prefetch = 1;
    get systemMessage() {
        return this.injector.get(message_1.SystemMessage);
    }
    constructor(injector, name) {
        this.injector = injector;
        this.name = name;
    }
    async onInit() {
        this.mq = await (0, util_1.createMq)(this.injector, this.name, this.prefetch);
    }
    async consumer() {
        await this.onInit();
        const consumer = await this.mq.consumer();
        consumer.pipe().subscribe((res) => {
            this.handler(res, 'consumer');
        });
    }
    async dead() {
        await this.onInit();
        const dead = await this.mq.dead();
        dead.pipe().subscribe((res) => {
            this.getDead(res, 'dead');
        });
    }
    async publish(msg, ttl = 1000 * 60 * 30) {
        const sender = await (0, util_1.createMq)(this.injector, this.name);
        await sender.publish(msg, ttl);
    }
    async publishs(msg, ttl = 1000 * 60 * 30) {
        const sender = await (0, util_1.createMq)(this.injector, this.name);
        return sender.publishs(msg, ttl);
    }
    async getDead(action, type) {
        const spiders = this.injector.getMulti(core_1.CoreSpider);
        action.from = type;
        if (action.data) {
            const { payload } = action.data;
            const platform = payload.platform;
            const spider = spiders.find(s => s.type === this.name && s.platform === platform);
            if (spider) {
                await spider.dead(action.data).then(res => {
                    if (res) {
                        res.subscribe({
                            error: (e) => {
                                action.ack();
                            },
                            complete: () => action.ack()
                        });
                        return;
                    }
                    else {
                        action.ack();
                    }
                });
                return;
            }
        }
        action.reject();
    }
    handler(action, type) {
        const spiders = this.injector.getMulti(core_1.CoreSpider);
        action.from = type;
        const data = action.data;
        if (data) {
            const { payload } = data;
            const { open_schedule } = payload;
            const platform = payload.platform;
            const spider = spiders.find(s => s.type === this.name && s.platform === platform);
            if (spider) {
                spider.run(data).then(res => {
                    if (res) {
                        res.pipe().subscribe({
                            error: (e) => {
                                if (e instanceof error_1.UserLogoutError) {
                                    // action.reject();
                                    setTimeout(() => {
                                        action.noAck(true, true);
                                    }, 1000 * Math.random() * 30);
                                }
                                else if (e instanceof error_1.Error414) {
                                    this.systemMessage.sendText('414', {
                                        title: '414',
                                        content: '系统繁忙，采集频率过高'
                                    }).subscribe();
                                    setTimeout(() => {
                                        action.noAck(true, true);
                                    }, 1000 * Math.random() * 30);
                                }
                                else if (e instanceof error_1.Error400) {
                                    setTimeout(() => {
                                        action.ack();
                                    }, 1000 * Math.random() * 3);
                                }
                                else {
                                    const dataStr = JSON.stringify(data);
                                    this.systemMessage.sendText(`error`, {
                                        title: e.message,
                                        content: dataStr,
                                    }).subscribe();
                                    setTimeout(() => {
                                        action.noAck(true, true);
                                    }, 1000 * Math.random() * 30);
                                }
                            },
                            complete: () => {
                                action.ack();
                            }
                        });
                    }
                    else {
                        action.ack();
                    }
                }).catch(e => {
                    if (e instanceof error_1.UserLogoutError) {
                        setTimeout(() => {
                            action.noAck(true, true);
                        }, 1000 * Math.random() * 30);
                    }
                    else if (e instanceof error_1.Error414) {
                        this.systemMessage.sendText(`414`, {
                            title: '414',
                            content: '系统繁忙，采集频率过高'
                        }).subscribe();
                        setTimeout(() => {
                            action.noAck(true, true);
                        }, 1000 * Math.random() * 30);
                    }
                    else if (e instanceof error_1.Error400) {
                        this.systemMessage.sendText(`400`, {
                            title: '400',
                            content: '资源已删除或不存在'
                        }).subscribe();
                        action.ack();
                    }
                    else {
                        console.log(e);
                        const dataStr = JSON.stringify(data);
                        this.systemMessage.sendText(`error`, {
                            title: e.message,
                            content: dataStr,
                        }).subscribe();
                        setTimeout(() => {
                            action.noAck(true, true);
                        }, 1000 * Math.random() * 30);
                    }
                });
                return;
            }
        }
        if (action.data) {
            const { payload } = action.data;
            const platform = payload.platform;
            console.error(`reject data error or not found spider ${platform} ${this.name}`);
            action.ack();
            return;
        }
        action.ack();
    }
}
exports.Task = Task;
