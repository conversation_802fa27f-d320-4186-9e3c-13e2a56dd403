"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var TasksModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TasksModule = void 0;
const core_1 = require("@nger/core");
const search_controller_1 = require("./search.controller");
const search_task_1 = require("./search.task");
const search_1 = require("./search");
const task_1 = require("./task");
const detail_task_1 = require("./detail.task");
const list_task_1 = require("./list.task");
const account_task_1 = require("./account.task");
const comment_task_1 = require("./comment.task");
const account_1 = require("./account");
const weibo_account_search_1 = require("./account-search/weibo.account-search");
const zhihu_account_search_1 = require("./account-search/zhihu.account-search");
const topic_task_1 = require("./topic.task");
const detail_1 = require("./detail");
const hot_task_1 = require("./hot.task");
const user_service_1 = require("./user.service");
const nlp_service_1 = require("./nlp.service");
const like_task_1 = require("./like.task");
const repostTimeline_task_1 = require("./repostTimeline.task");
const comment_1 = require("./comment");
const like_1 = require("./like");
const repost_timeline_1 = require("./repost-timeline");
const list_1 = require("./list");
const page_1 = require("./page");
const page_task_1 = require("./page.task");
let TasksModule = TasksModule_1 = class TasksModule {
    static forRoot() {
        return {
            module: TasksModule_1,
            providers: [
                {
                    provide: task_1.Task,
                    useExisting: search_task_1.SearchTask,
                    multi: true
                },
                {
                    provide: task_1.Task,
                    useExisting: detail_task_1.DetailTask,
                    multi: true
                },
                {
                    provide: task_1.Task,
                    useExisting: hot_task_1.HotTask,
                    multi: true
                },
                {
                    provide: task_1.Task,
                    useExisting: list_task_1.ListTask,
                    multi: true
                },
                {
                    provide: task_1.Task,
                    useExisting: page_task_1.PageTask,
                    multi: true
                },
                {
                    provide: task_1.Task,
                    useExisting: account_task_1.AccountTask,
                    multi: true
                },
                {
                    provide: task_1.Task,
                    useExisting: comment_task_1.CommentTask,
                    multi: true
                },
                {
                    provide: task_1.Task,
                    useExisting: topic_task_1.TopicTask,
                    multi: true
                },
                {
                    provide: task_1.Task,
                    useExisting: like_task_1.LikeTask,
                    multi: true
                },
                {
                    provide: task_1.Task,
                    useExisting: repostTimeline_task_1.RepostTimelineTask,
                    multi: true
                }
            ]
        };
    }
};
exports.TasksModule = TasksModule;
exports.TasksModule = TasksModule = TasksModule_1 = __decorate([
    (0, core_1.Module)({
        providers: [
            ...search_1.searchProvicers,
            ...account_1.accountProvicers,
            ...detail_1.detailProvicers,
            ...comment_1.commentProvicers,
            ...like_1.likeProvicers,
            ...repost_timeline_1.repostTimelineProvicers,
            ...list_1.listProvicers,
            ...page_1.pageProvicers,
            nlp_service_1.NlpService,
            search_task_1.SearchTask,
            detail_task_1.DetailTask,
            list_task_1.ListTask,
            account_task_1.AccountTask,
            comment_task_1.CommentTask,
            page_task_1.PageTask,
            like_task_1.LikeTask,
            repostTimeline_task_1.RepostTimelineTask,
            topic_task_1.TopicTask,
            hot_task_1.HotTask,
            user_service_1.UserService,
            {
                provide: weibo_account_search_1.WeiboAccountSearch,
                useFactory: (injector) => new weibo_account_search_1.WeiboAccountSearch(injector),
                deps: [core_1.Injector]
            },
            {
                provide: zhihu_account_search_1.ZhihuAccountSearch,
                useFactory: (injector) => new zhihu_account_search_1.ZhihuAccountSearch(injector),
                deps: [core_1.Injector]
            },
        ],
        imports: [],
        controllers: [
            search_controller_1.SearchController
        ]
    })
], TasksModule);
