"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Article = exports.Detail = exports.DetailLink = exports.Link = void 0;
class Link {
    href;
    type = 'link';
    constructor(href) {
        this.href = href;
    }
    static isLink(val) {
        return val && val.type === 'link';
    }
}
exports.Link = Link;
class DetailLink {
    type = 'detail';
    href;
    platform;
    keyword;
    static is(val) {
        return val && val.type === 'detail';
    }
}
exports.DetailLink = DetailLink;
class Detail {
    href;
    avatar;
    type = 'detail';
    title;
    author;
    constructor(href, title, author, avatar) {
        this.href = href;
        this.title = title;
        this.author = author;
        this.avatar = avatar;
    }
    static isDetail(val) {
        return val && val.type === 'detail';
    }
}
exports.Detail = Detail;
class Article {
    type = 'article';
    // 平台
    platform;
    // 链接 唯一
    url;
    // 更新时间
    updated_time;
    // 创建时间
    created_time;
    // 分享
    share_count;
    share = [];
    // 赞同
    voteup_count;
    voteup = [];
    // 评论
    comment_count;
    comment = [];
    // 文章id
    id;
    // 标题
    title;
    // 内容
    content;
    // 简介
    excerpt;
    // 图片
    thumbnails;
    // 作者
    author;
    // 作者头像
    author_avatar;
    // 作者性别
    author_gender;
    // 作者id
    author_id;
    // 作者粉丝
    author_follower_count;
    // 作者链接
    author_url;
    // 点赞
    author_voteup_count;
    // 作者类型
    author_type;
    // 作者性质
    author_user_type;
    // 文章类型
    article_type = 'article';
    // 问题
    question_name;
    // 问题链接
    question_url;
    // 问题id
    question_id;
    is_plus;
    is_plus_content;
    static is(val) {
        return val && val.type === 'article';
    }
}
exports.Article = Article;
