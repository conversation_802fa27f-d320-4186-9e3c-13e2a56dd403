"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const core_1 = require("@nger/core");
const typeorm_1 = require("@nger/typeorm");
const spider_user_1 = require("../../entities/spider_user");
let UserService = class UserService {
    injector;
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    constructor(injector) {
        this.injector = injector;
    }
    users = [];
    user;
    async getRandomUser(platform) {
        const users = await this.db.manager.find(spider_user_1.SpiderUser, {
            where: { platform: platform, status: 2 }
        });
        if (users.length === 0) {
            return undefined;
        }
        const userIndex = Math.floor(Math.random() * users.length);
        const user = users[userIndex];
        return user;
    }
    async getUser(platform) {
        if (this.user)
            return this.user;
        if (this.users.length <= 0) {
            this.users = await this.db.manager.find(spider_user_1.SpiderUser, {
                where: { platform: platform, status: 2 }
            });
            if (this.users.length === 0) {
                return undefined;
            }
        }
        const userIndex = Math.floor(Math.random() * this.users.length);
        this.user = this.users[userIndex];
        return this.user;
    }
    async logout(user) {
        await this.db.manager.update(spider_user_1.SpiderUser, user.id, { status: 1 }).catch(e => console.log(e.message));
        this.user = undefined;
        this.users = [];
        await this.getUser(user.platform);
    }
};
exports.UserService = UserService;
exports.UserService = UserService = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], UserService);
