"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMq = createMq;
const rabbitmq_1 = require("../rabbitmq");
function createMq(injector, name, prefetch = 1) {
    const mqFactory = injector.get(rabbitmq_1.MqFactory);
    return mqFactory.create({
        exchange: `${name}.exchange`,
        deadExchange: `dead.${name}.exchange`,
        routingKey: `${name}.routingKey`,
        deadRoutingKey: `dead.${name}.routingKey`,
        queue: `${name}.queue`,
        deadQueue: `dead.${name}.queue`,
        prefetch
    });
}
