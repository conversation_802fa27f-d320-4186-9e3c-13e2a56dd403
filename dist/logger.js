"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loggerProviders = exports.Logger = exports.LOGGER_TAGS = exports.LOGGER_LEVEL = exports.LOGGER_URL = void 0;
const core_1 = require("@nger/core");
exports.LOGGER_URL = new core_1.InjectionToken(`LOGGER URL`);
exports.LOGGER_LEVEL = new core_1.InjectionToken(`LOGGER LEVEL`);
exports.LOGGER_TAGS = new core_1.InjectionToken(`LOGGER TAGS`);
class Logger {
}
exports.Logger = Logger;
/**
 * logstash logger
 */
exports.loggerProviders = [{
        provide: exports.LOGGER_URL,
        useValue: process.env.LOGSTASH_URL
    }, {
        provide: exports.LOGGER_TAGS,
        useValue: ['production', 'api']
    }, {
        provide: exports.LOGGER_LEVEL,
        useValue: 'info'
    }, {
        provide: Logger,
        useFactory: (injector) => {
            const tags = injector.get(exports.LOGGER_TAGS);
            const url = injector.get(exports.LOGGER_URL);
            const level = injector.get(exports.LOGGER_URL);
            require('logstash')(url, tags, level);
        },
        deps: [
            core_1.Injector
        ]
    }];
