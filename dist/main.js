#!/usr/bin/env node
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppNodeModule = void 0;
const cluster_1 = __importDefault(require("cluster"));
const os_1 = __importDefault(require("os"));
require("reflect-metadata");
const core_1 = require("@nger/core");
const zookeeper_1 = require("@nger/zookeeper");
const typeorm_1 = require("@nger/typeorm");
const utils_1 = require("@nger/utils");
const rabbitmq_1 = require("@nger/rabbitmq");
const weibo_node_module_1 = require("./weibo-node.module");
let AppNodeModule = class AppNodeModule {
};
exports.AppNodeModule = AppNodeModule;
exports.AppNodeModule = AppNodeModule = __decorate([
    (0, core_1.Module)({
        imports: [
            typeorm_1.TypeormModule.forRoot(),
            zookeeper_1.ZookeeperModule.forRoot(),
            rabbitmq_1.RabbitMqModule.forRoot(),
            utils_1.UtilsModule,
            weibo_node_module_1.WeiboNodeModule,
        ]
    })
], AppNodeModule);
if (cluster_1.default.isPrimary) {
    const cpus = os_1.default.cpus().length;
    for (let i = 0; i < cpus; i++) {
        cluster_1.default.fork();
    }
    cluster_1.default.on("exit", (worker, code, signal) => {
        console.log("工作进程" + worker.process.pid + "已退出");
    });
    console.log(`主节点启动成功，共计${cpus}个爬虫节点`);
}
else {
    (0, core_1.platformCore)([{
            provide: core_1.APP_ROOT,
            useValue: process.cwd()
        }]).bootstrap(AppNodeModule).then(async (injector) => {
        try {
            const rabbitmqStarter = injector.get(rabbitmq_1.RabbitmqStarter);
            await rabbitmqStarter.start(true);
            console.log(`爬虫节点${cluster_1.default.worker?.process.pid}启动成功`);
        }
        catch (e) {
            console.log(e.message);
        }
    });
}
