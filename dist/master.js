#!/usr/bin/env node
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const dotenv = require('dotenv');
dotenv.config();
require("reflect-metadata");
process.env.TZ = 'Asia/Shanghai';
const core_1 = require("@nger/core");
const http_1 = require("@nger/http");
const zookeeper_1 = require("@nger/zookeeper");
const install_1 = require("@nger/install");
const typeorm_1 = require("@nger/typeorm");
const utils_1 = require("@nger/utils");
const weibo_module_1 = require("./weibo.module");
const rest_1 = require("@nger/rest");
const rabbitmq_1 = require("./framework/rabbitmq");
const sirv_1 = require("@nger/sirv");
const oauth2_1 = require("@nger/oauth2");
const path_1 = require("path");
const framework_1 = require("./framework");
const tokens_1 = require("./tokens");
const events_1 = __importDefault(require("events"));
const root = (0, path_1.join)(__dirname, '..');
events_1.default.setMaxListeners(1000);
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, core_1.Module)({
        imports: [
            typeorm_1.TypeormModule.forRoot(),
            rabbitmq_1.RabbitmqModule.forRoot(),
            zookeeper_1.ZookeeperModule.forRoot(),
            oauth2_1.Oauth2ServerModule,
            install_1.InstallModule.forRoot(),
            sirv_1.SirvModule.forChild(root, '@nger/weibo', [
                'big', 'home', 'loginAdmin',
                'rest', 'gonggao', 'search',
                'search/weibo', 'search/zhihu',
                'search/toutiao', 'search/heimao',
                'search/wechat', 'search/qq-new',
                'nlp', 'bind-user', 'search/all',
                'account/search', 'task'
            ]),
            utils_1.UtilsModule,
            weibo_module_1.WeiboModule,
            http_1.HttpModule,
            rest_1.RestModule,
            framework_1.TasksModule,
            framework_1.RedisModule
        ],
        providers: [
            ...tokens_1.coreProviders
        ]
    })
], AppModule);
(0, core_1.platformCore)([{
        provide: core_1.APP_ROOT,
        useValue: process.cwd()
    }]).bootstrap(AppModule)
    .then(injector => {
    console.info('恭喜您主节点启动成功');
});
