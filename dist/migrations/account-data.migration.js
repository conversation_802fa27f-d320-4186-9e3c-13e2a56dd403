"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Account************* = void 0;
const typeorm_1 = require("@nger/typeorm");
const core_1 = require("@nger/core");
const path_1 = require("path");
const node_xlsx_1 = require("node-xlsx");
const spilder_account_1 = require("../entities/spilder-account");
let Account************* = class Account************* extends typeorm_1.Migration {
    constructor(injector) {
        super(`*************`);
        this.injector = injector;
    }
    async up(queryRunner) {
        const dir = (0, path_1.join)(__dirname, '../../static/account.xlsx');
        const list = (0, node_xlsx_1.parse)(dir);
        if (list.length === 1) {
            const obj = list[0];
            const data = obj.data;
            await Promise.all(data.map(async (it, index) => {
                if (index === 0) {
                    return;
                }
                else {
                    const title = it[3];
                    const old = await this.db.manager.findOne(spilder_account_1.SpilderAccount, { where: { nickname: title } });
                    if (old) {
                        old.type = it[4];
                        old.verified_reason = it[6];
                        old.from = it[7];
                        old.url = it[8];
                        await this.db.manager.save(spilder_account_1.SpilderAccount, old);
                    }
                }
            }));
        }
    }
    async down(queryRunner) {
    }
};
exports.Account************* = Account*************;
exports.Account************* = Account************* = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], Account*************);
