"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.comment1657763831227 = void 0;
const typeorm_1 = require("@nger/typeorm");
const core_1 = require("@nger/core");
const comment_1 = require("../entities/comment");
let comment1657763831227 = class comment1657763831227 extends typeorm_1.Migration {
    constructor(injector) {
        super(`1657763831227`);
        this.injector = injector;
    }
    async up(queryRunner) {
        await queryRunner.createTable(this.createTable(comment_1.WbComment));
    }
    async down(queryRunner) {
        await queryRunner.dropTable(this.createTable(comment_1.WbComment));
    }
};
comment1657763831227 = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], comment1657763831227);
exports.comment1657763831227 = comment1657763831227;
