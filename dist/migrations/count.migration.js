"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.count1662172062070 = void 0;
const typeorm_1 = require("@nger/typeorm");
const core_1 = require("@nger/core");
const count_1 = require("../entities/count");
let count1662172062070 = class count1662172062070 extends typeorm_1.Migration {
    constructor(injector) {
        super(`1662172062070`);
        this.injector = injector;
    }
    async up(queryRunner) {
        await queryRunner.createTable(this.createTable(count_1.CountArticle));
        await queryRunner.createTable(this.createTable(count_1.CountArticleComment));
    }
    async down(queryRunner) {
        // await queryRunner.createTable(this.createTable(WbCity))
    }
};
exports.count1662172062070 = count1662172062070;
exports.count1662172062070 = count1662172062070 = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], count1662172062070);
