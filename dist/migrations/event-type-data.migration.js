"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WbEventTypeData1658900374793 = void 0;
const typeorm_1 = require("@nger/typeorm");
const core_1 = require("@nger/core");
const event_type_1 = require("../entities/event-type");
let WbEventTypeData1658900374793 = class WbEventTypeData1658900374793 extends typeorm_1.Migration {
    constructor(injector) {
        super(`1658900374793`);
        this.injector = injector;
    }
    async up(queryRunner) {
        const list = [{
                title: '社会民生类-自然灾害',
                code: 'A1'
            }, {
                title: '社会民生类-事故灾难',
                code: 'A2'
            }, {
                title: '社会民生类-公共卫生事件',
                code: 'A3'
            }, {
                title: '社会民生类-社会安全事件',
                code: 'A4'
            }, {
                title: '社会民生类-公共安全',
                code: 'A5'
            }, {
                title: '社会民生类-社会道德',
                code: 'A6'
            }, {
                title: '社会民生类-医患纠纷',
                code: 'A7'
            }, {
                title: '社会民生类-苦难生活',
                code: 'A8'
            }, {
                title: '社会民生类-慈善救助',
                code: 'A9'
            }, {
                title: '社会民生类-物价监管',
                code: 'A10'
            }, {
                title: '政府行为处置后果类事件',
                code: 'B'
            }, {
                title: '公职人员自身行为类事件',
                code: 'C'
            }];
        await this.db.manager.save(event_type_1.WbEventType, list);
    }
    async down(queryRunner) {
    }
};
exports.WbEventTypeData1658900374793 = WbEventTypeData1658900374793;
exports.WbEventTypeData1658900374793 = WbEventTypeData1658900374793 = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], WbEventTypeData1658900374793);
