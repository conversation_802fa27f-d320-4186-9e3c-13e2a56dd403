"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.platform1658554213740 = void 0;
const typeorm_1 = require("@nger/typeorm");
const core_1 = require("@nger/core");
const platform_1 = require("../entities/platform");
/**
 * 平台及平台用户
 */
let platform1658554213740 = class platform1658554213740 extends typeorm_1.Migration {
    constructor(injector) {
        super(`1658554213740`);
        this.injector = injector;
    }
    async up(queryRunner) {
        const weibo = new platform_1.WbPlatform();
        weibo.icon = 'icon-xinlangweibo';
        weibo.name = 'weibo';
        weibo.title = '微博';
        weibo.loginUrl = `https://weibo.com/`;
        await this.db.manager.save(platform_1.WbPlatform, weibo);
        const zhihu = new platform_1.WbPlatform();
        zhihu.icon = 'icon-shejiaotubiao-46';
        zhihu.name = 'zhihu';
        zhihu.title = '知乎';
        zhihu.loginUrl = `https://www.zhihu.com/`;
        await this.db.manager.save(platform_1.WbPlatform, zhihu);
        const toutiao = new platform_1.WbPlatform();
        toutiao.icon = 'icon-toutiaoyangshi';
        toutiao.name = 'toutiao';
        toutiao.title = '头条';
        toutiao.loginUrl = `https://www.toutiao.com/`;
        await this.db.manager.save(platform_1.WbPlatform, toutiao);
    }
    async down(queryRunner) {
    }
};
exports.platform1658554213740 = platform1658554213740;
exports.platform1658554213740 = platform1658554213740 = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], platform1658554213740);
