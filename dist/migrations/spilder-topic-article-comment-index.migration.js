"use strict";
/**
 *

CREATE INDEX spilder_topic_article_comment_aid_idx ON public.spilder_topic_article_comment (aid)
CREATE INDEX spilder_topic_article_comment_tid_idx ON public.spilder_topic_article_comment (tid)
CREATE INDEX spilder_topic_article_comment_eid_idx ON public.spilder_topic_article_comment (eid)
CREATE INDEX spilder_topic_article_comment_pid_idx ON public.spilder_topic_article_comment (pid)
CREATE INDEX spilder_topic_article_comment_uid_idx ON public.spilder_topic_article_comment (uid)


 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.spilderTopicArticleCommentIndex1659188827677 = void 0;
const typeorm_1 = require("@nger/typeorm");
const core_1 = require("@nger/core");
let spilderTopicArticleCommentIndex1659188827677 = class spilderTopicArticleCommentIndex1659188827677 extends typeorm_1.Migration {
    constructor(injector) {
        super(`1659188827677`);
        this.injector = injector;
    }
    async up(queryRunner) {
        try {
            await queryRunner.query(`CREATE INDEX spilder_topic_article_comment_aid_idx ON public.spilder_topic_article_comment (aid)`);
        }
        catch (e) { }
        try {
            await queryRunner.query(`CREATE INDEX spilder_topic_article_comment_tid_idx ON public.spilder_topic_article_comment (tid)`);
        }
        catch (e) { }
        try {
            await queryRunner.query(`CREATE INDEX spilder_topic_article_comment_eid_idx ON public.spilder_topic_article_comment (eid)`);
        }
        catch (e) { }
        try {
            await queryRunner.query(`CREATE INDEX spilder_topic_article_comment_pid_idx ON public.spilder_topic_article_comment (pid)`);
        }
        catch (e) { }
        try {
            await queryRunner.query(`CREATE INDEX spilder_topic_article_comment_uid_idx ON public.spilder_topic_article_comment (uid)`);
        }
        catch (e) { }
    }
    async down(queryRunner) {
    }
};
spilderTopicArticleCommentIndex1659188827677 = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], spilderTopicArticleCommentIndex1659188827677);
exports.spilderTopicArticleCommentIndex1659188827677 = spilderTopicArticleCommentIndex1659188827677;
