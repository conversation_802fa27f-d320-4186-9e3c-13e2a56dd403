#!/usr/bin/env node
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
require("reflect-metadata");
process.env.TZ = 'Asia/Shanghai';
const core_1 = require("@nger/core");
const zookeeper_1 = require("@nger/zookeeper");
const typeorm_1 = require("@nger/typeorm");
const utils_1 = require("@nger/utils");
const index_1 = require("./framework/index");
const weibo_node_module_1 = require("./weibo-node.module");
const tokens_1 = require("./tokens");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, core_1.Module)({
        imports: [
            typeorm_1.TypeormModule.forRoot(),
            zookeeper_1.ZookeeperModule.forRoot(),
            index_1.RabbitmqModule.forRoot(),
            utils_1.UtilsModule,
            weibo_node_module_1.WeiboNodeModule,
        ],
        providers: [
            ...tokens_1.coreProviders
        ]
    })
], AppModule);
(0, core_1.platformCore)([{
        provide: core_1.APP_ROOT,
        useValue: process.cwd()
    }]).bootstrap(AppModule).then(async (injector) => {
    try {
    }
    catch (e) {
        console.log(e.message);
    }
});
process.on('uncaughtException', (error, origin) => {
    console.error(error.message);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error(reason);
    promise.catch(e => console.error(e.message));
});
