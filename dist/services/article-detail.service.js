"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticleDetailService = void 0;
const core_1 = require("@nger/core");
const puppeteer_1 = require("@nger/puppeteer");
const typeorm_1 = require("@nger/typeorm");
const axios_1 = __importDefault(require("axios"));
let ArticleDetailService = class ArticleDetailService {
    injector;
    get browser() {
        return this.injector.get(puppeteer_1.BROWSER);
    }
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    constructor(injector) {
        this.injector = injector;
    }
    async search(link, uid) {
        const d = (0, core_1.defer)();
        const p = await this.browser.newPage();
        await p.setRequestInterception(true);
        p.on('request', (res) => {
            const url = res.url();
            if (url.includes('/ajax/statuses/show')) {
                const headers = res.headers();
                axios_1.default.get(url, {
                    headers
                }).then(res => res.data).then(data => d.resolve(data)).catch(async (e) => {
                    d.resolve({});
                });
            }
            res.continue();
        });
        await p.goto(link, { waitUntil: 'networkidle2' });
        await p.close();
        return d;
    }
};
ArticleDetailService = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], ArticleDetailService);
exports.ArticleDetailService = ArticleDetailService;
