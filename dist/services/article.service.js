"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticleService = void 0;
const core_1 = require("@nger/core");
const puppeteer_1 = require("@nger/puppeteer");
const typeorm_1 = require("@nger/typeorm");
let ArticleService = class ArticleService {
    injector;
    get browser() {
        return this.injector.get(puppeteer_1.BROWSER);
    }
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    constructor(injector) {
        this.injector = injector;
    }
    async search(url, uid) {
        const p = await this.browser.newPage();
        await p.goto(url, { waitUntil: ['networkidle2'] });
        await p.waitForSelector('#pl_feedlist_index');
        const data = await p.$eval('#pl_feedlist_index', feedlist => {
            const pageElement = feedlist.querySelector('div.m-page > div > span > ul > li:last-child > a');
            const pageText = pageElement ? pageElement.innerText : '';
            const re = /第(.*?)页/;
            const matchRes = pageText.match(re);
            let count = 0;
            if (matchRes) {
                count = Number(matchRes[1]);
            }
            const items = feedlist.querySelectorAll('.card');
            let list = [];
            items.forEach(item => {
                const aidLink = item.querySelector('div.card-feed > div.content > p.from > a:nth-child(1)');
                if (aidLink) {
                    const link = aidLink.href;
                    list.push(link);
                }
            });
            return {
                total: count,
                list
            };
        });
        await p.close();
        return data;
    }
};
ArticleService = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], ArticleService);
exports.ArticleService = ArticleService;
