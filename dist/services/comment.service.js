"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommentService = void 0;
const core_1 = require("@nger/core");
const typeorm_1 = require("@nger/typeorm");
const typeorm_2 = require("typeorm");
const spilder_topic_article_comment_1 = require("../entities/spilder-topic-article-comment");
const spilder_topic_article_comment_tend_1 = require("../entities/spilder-topic-article-comment-tend");
const spilder_topic_article_comment_unique_1 = require("../entities/spilder-topic-article-comment-unique");
let CommentService = class CommentService {
    injector;
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    get dataSource() {
        return this.injector.get(typeorm_2.DataSource);
    }
    constructor(injector) {
        this.injector = injector;
    }
    createQueryRunner() {
        return this.db.createQueryRunner();
    }
    upgrades(comments) {
        return [
            ...new Set(comments.map(c => `${c.platform}$-$${c.cid}`))
        ].map(a => a.split('$-$'));
    }
    async upgrade(comment, needDeletes = []) {
        const runner = this.createQueryRunner();
        await runner.connect();
        await runner.startTransaction();
        const entity = new typeorm_2.EntityManager(this.dataSource, runner);
        try {
            try {
                await entity.insert(spilder_topic_article_comment_unique_1.SpilderTopicArticleCommentUnique, { platform: comment.platform, cid: comment.cid });
            }
            catch (e) {
                // remove 
            }
            await runner.commitTransaction();
        }
        catch (e) {
            // 插入截面数据
            needDeletes.push(comment.id);
            await runner.rollbackTransaction();
        }
        finally {
            await runner.release();
        }
    }
    async insertOrUpdateComment(comment) {
        const runner = this.createQueryRunner();
        await runner.connect();
        await runner.startTransaction();
        const entity = new typeorm_2.EntityManager(this.dataSource, runner);
        try {
            await entity.insert(spilder_topic_article_comment_unique_1.SpilderTopicArticleCommentUnique, { platform: comment.platform, cid: comment.cid });
            await entity.insert(spilder_topic_article_comment_1.SpilderTopicArticleComment, comment);
            await runner.commitTransaction();
        }
        catch (e) {
            // 插入截面数据
            const tend = new spilder_topic_article_comment_tend_1.SpilderTopicArticleCommentTend();
            tend.cid = comment.id;
            tend.like_counts = comment.like_counts;
            await entity.save(spilder_topic_article_comment_tend_1.SpilderTopicArticleCommentTend, tend);
        }
        finally {
            await runner.release();
        }
    }
};
exports.CommentService = CommentService;
exports.CommentService = CommentService = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], CommentService);
