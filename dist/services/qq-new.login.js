"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QqNewLoginService = void 0;
const core_1 = require("@nger/core");
const utils_1 = require("@nger/utils");
const tokens_1 = require("../tokens");
let QqNewLoginService = class QqNewLoginService {
    injector;
    get crypto() {
        return this.injector.get(utils_1.CryptoService);
    }
    constructor(injector) {
        this.injector = injector;
    }
    delay(time) {
        const d = (0, core_1.defer)();
        setTimeout(() => {
            d.resolve();
        }, time);
        return d;
    }
    get puppeteerPool() {
        return this.injector.get(tokens_1.PUPPETEER_POOL);
    }
    async login(login_state) {
        const browser = await this.puppeteerPool.acquire();
        const url = 'https://news.qq.com/';
        const ctx = browser.defaultBrowserContext();
        await ctx.overridePermissions(url, ['geolocation']);
        const page = await browser.newPage();
        page.setJavaScriptEnabled(true);
        page.setViewport({
            width: 1325,
            height: 768
        });
        await page.goto(url, {
            waitUntil: ['networkidle2']
        });
        const href = page.url();
        login_state.next({
            action: 'open',
            data: href
        });
        try {
            const loginBtn = await page.waitForSelector('#login');
            login_state.next({
                action: 'get_login_btn',
                data: href
            });
            if (loginBtn) {
                await loginBtn.hover();
                const sel = await page.waitForSelector('.agreementlogin .sel');
                if (sel) {
                    await sel.click();
                }
                await loginBtn.click();
                login_state.next({
                    action: 'click_login_btn',
                    data: href
                });
            }
            await page.waitForNavigation();
            let iframUrl = await page.$eval('iframe', (img) => img.src);
            login_state.next({
                action: 'get_login_iframe_url',
                data: iframUrl
            });
            console.log(iframUrl);
            await page.goto(iframUrl);
            // let img = await page.waitForSelector('#qrlogin_img').catch(e => {
            //     return null;
            // });
            // while(!img){
            //     img = await page.waitForSelector('#qrlogin_img').catch(e => {
            //         return null;
            //     });
            // }
            // let loginImage = await page.$eval('img#qrlogin_img', (img) => (img as HTMLImageElement).src);
            // login_state.next({
            //     action: 'get_login_qrcode',
            //     data: loginImage
            // });
            let tip = await page.waitForSelector('div.step2_outer>div.qr_h3').catch(e => {
                return null;
            });
            while (!tip) {
                tip = await page.waitForSelector('div.step2_outer>div.qr_h3').catch(e => {
                    return null;
                });
            }
            const txt = await page.$eval('div.step2_outer>div.qr_h3', (div) => {
                return div.innerText;
            });
            login_state.next({
                action: 'get_scan_qrcode',
                data: txt
            });
            // 扫描成功
            await page.waitForNavigation();
            // await page.setRequestInterception(false);
            const cookies = await page.cookies('https://news.qq.com/');
            login_state.next({
                action: 'login_success',
                data: url,
                cookies
            });
            let userDiv = await page.waitForSelector('div.logined').catch(e => {
                return null;
            });
            while (!userDiv) {
                userDiv = await page.waitForSelector('div.logined').catch(e => {
                    return null;
                });
            }
            const avatar = await page.$eval('div.user-icon > a > img', a => {
                const href = a.src;
                return href;
            });
            login_state.next({
                action: 'get_userinfo',
                data: {
                    cookies,
                    avatar
                }
            });
        }
        catch (e) {
            login_state.next({
                action: 'get_login_fail',
                data: e.message
            });
            login_state.error(e);
        }
        finally {
            await page.close();
            this.puppeteerPool.release(browser);
        }
    }
};
exports.QqNewLoginService = QqNewLoginService;
exports.QqNewLoginService = QqNewLoginService = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], QqNewLoginService);
