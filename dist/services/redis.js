"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.del = del;
exports.get = get;
exports.gets = gets;
exports.lock = lock;
exports.set = set;
exports.setExp = setExp;
exports.unLock = unLock;
exports.useRedis = useRedis;
exports.waitFor = waitFor;
exports.redis = redis;
exports.waitUnLock = waitUnLock;
exports.autoLock = autoLock;
exports.zAdd = zAdd;
exports.zRem = zRem;
exports.delay = delay;
const redis_1 = require("redis");
let redisMap = {};
async function useRedis(databsae = 0) {
    if (redisMap[databsae]) {
        return redisMap[databsae];
    }
    const redisUrl = process.env.REDIS_URL || ``;
    const redis = (0, redis_1.createClient)({
        url: redisUrl,
        database: databsae
    });
    await redis.connect();
    return redis;
}
async function set(key, val, database = 0) {
    return await redis(async (client) => {
        return await client.set(key, JSON.stringify(val)).catch(e => {
            redisMap[database] = null;
            throw e;
        });
    });
}
async function delay(n) {
    return new Promise((resolve, reject) => {
        setTimeout(() => resolve(), n);
    });
}
async function setExp(key, val, exip = 10, database = 0) {
    return await redis(async (client) => {
        await client.set(key, JSON.stringify(val)).catch(e => {
            redisMap[database] = null;
            throw e;
        });
        ;
        await client.expire(key, exip).catch(e => {
            redisMap[database] = null;
            throw e;
        });
    });
}
async function del(key, database = 0) {
    return await redis(async (client) => {
        return await client.del(key).catch(e => {
            redisMap[database] = null;
            throw e;
        });
        ;
    });
}
function safeParseJson(str) {
    if (!str)
        return;
    try {
        return JSON.parse(str);
    }
    catch (e) {
        return str;
    }
}
async function get(key, database = 0) {
    return await redis(async (client) => {
        const str = await client.get(key).catch(e => {
            redisMap[database] = null;
            throw e;
        });
        const res = safeParseJson(str);
        return res;
    });
}
async function gets(key, database = 0) {
    return await redis(async (client) => {
        const keys = await client.keys(key);
        if (!keys)
            return {};
        return Promise.all(keys.map(async (key) => {
            const val = await get(key, database).catch(e => {
                redisMap[database] = null;
                throw e;
            });
            ;
            return { key, val };
        }));
    });
}
async function lock(key, value = `1`, database = 0) {
    return await redis(async (client) => {
        const res = await client.set(key, value);
        const success = res === 'OK';
        return success;
    });
}
async function autoLock(key, value, expire, database = 0) {
    return await redis(async (client) => {
        const res = await client.set(key, value).catch(e => {
            redisMap[database] = null;
            throw e;
        });
        const success = res === 'OK';
        await client.expire(key, expire).catch(e => {
            redisMap[database] = null;
            throw e;
        });
        return success;
    });
}
async function unLock(key, database = 0) {
    await redis(async (client) => {
        await client.del(key);
    });
    return true;
}
async function waitFor(key, database = 0) {
    await redis(async (client) => {
        let lock = await client.get(key);
        while (!lock) {
            await new Promise((resolve, reject) => {
                setTimeout(() => resolve(), 1000);
            });
            lock = await client.get(key);
        }
    });
    return get(key, database);
}
async function waitUnLock(key, database = 0) {
    return redis(async (client) => {
        let lock = await client.get(key);
        while (lock) {
            await new Promise((resolve, reject) => {
                setTimeout(() => resolve(), 1000);
            });
            lock = await client.get(key);
        }
    });
}
async function zAdd(key, score, value, database = 0) {
    return redis(async (c) => {
        return await c.zAdd(key, { score: score, value: value });
    });
}
async function zRem(key, value, database = 0) {
    return redis(async (c) => {
        return await c.zRem(key, value);
    });
}
async function redis(handler, database = 0) {
    const redisUrl = process.env.REDIS_URL || ``;
    const redis = (0, redis_1.createClient)({
        url: redisUrl,
        database: database
    });
    await redis.connect();
    return handler(redis).catch(e => {
        throw e;
    }).finally(() => {
        redis.disconnect();
    });
}
