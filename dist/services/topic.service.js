"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TopicService = void 0;
const core_1 = require("@nger/core");
const puppeteer_1 = require("@nger/puppeteer");
const typeorm_1 = require("@nger/typeorm");
const user_1 = require("../entities/user");
const article_service_1 = require("./article.service");
let TopicService = class TopicService {
    injector;
    get browser() {
        return this.injector.get(puppeteer_1.BROWSER);
    }
    get db() {
        return this.injector.get(typeorm_1.Db);
    }
    get article() {
        return this.injector.get(article_service_1.ArticleService);
    }
    constructor(injector) {
        this.injector = injector;
    }
    async search(uid, eid, keyword, page = 1) {
        const user = await this.db.manager.findOne(user_1.WbUser, { where: { id: uid } });
        if (!user) {
            throw new Error(`用户不存在或已删除`);
        }
        const cookies = user.cookies;
        const p = await this.browser.newPage();
        await p.setCookie(...cookies);
        await p.goto(`https://s.weibo.com/topic?q=${encodeURIComponent(keyword)}&pagetype=topic&topic=1&Refer=weibo_topic&page=${page}`, { waitUntil: 'networkidle0' });
        await p.waitForSelector('#pl_feedlist_index');
        const list = await p.$eval('#pl_feedlist_index', (feedlist) => {
            const cards = feedlist.querySelectorAll('.card');
            const pageElement = feedlist.querySelector('div.m-page > div > span > ul > li:last-child > a');
            const pageText = pageElement ? pageElement.innerText : '';
            let list = [];
            function stringToCount(str) {
                let count = 0;
                const result = str.match(/([\d\.]+)([\u4E00-\u9FA5])[\u4E00-\u9FA5]+/);
                if (result) {
                    const countStr = result[1];
                    const unit = result[2];
                    count = Number(countStr);
                    switch (unit) {
                        case '万':
                            count *= 10000;
                            break;
                        case '亿':
                            count *= 10000 * 10000;
                            break;
                    }
                }
                return Math.round(count);
            }
            cards.forEach(card => {
                const titleElement = card.querySelector('div.info > div > a');
                const descElement = card.querySelector('div.info > p:nth-child(2)');
                const tagElement = card.querySelector('div.info > p:nth-child(3)');
                const logoElement = card.querySelector('div.pic-l > a > img');
                const tag = tagElement ? tagElement.innerText : '';
                const result = tag.match(/([\d\.]+[\u4E00-\u9FA5]+)\s([\d\.]+[\u4E00-\u9FA5]+)/);
                let readCount = 0;
                let articleCount = 0;
                if (result) {
                    const read = result[2];
                    const article = result[1];
                    readCount = stringToCount(read);
                    articleCount = stringToCount(article);
                }
                const topic = {};
                topic.title = titleElement ? titleElement.innerText : '';
                topic.desc = descElement ? descElement.innerText : '';
                topic.readCount = readCount;
                topic.articleCount = articleCount;
                topic.link = titleElement ? titleElement.href : '';
                topic.logo = logoElement ? logoElement.src : '';
                list.push(topic);
            });
            const re = /第(.*?)页/;
            const matchRes = pageText.match(re);
            let count = 0;
            if (matchRes) {
                count = Number(matchRes[1]);
            }
            return { total: count, list };
        });
        await p.close();
        return list;
    }
};
TopicService = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], TopicService);
exports.TopicService = TopicService;
