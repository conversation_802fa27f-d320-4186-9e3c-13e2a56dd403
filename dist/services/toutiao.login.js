"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToutiaoLoginService = void 0;
const core_1 = require("@nger/core");
const utils_1 = require("@nger/utils");
const tokens_1 = require("../tokens");
let ToutiaoLoginService = class ToutiaoLoginService {
    injector;
    get crypto() {
        return this.injector.get(utils_1.CryptoService);
    }
    constructor(injector) {
        this.injector = injector;
    }
    delay(time) {
        const d = (0, core_1.defer)();
        setTimeout(() => {
            d.resolve();
        }, time);
        return d;
    }
    get puppeteerPool() {
        return this.injector.get(tokens_1.PUPPETEER_POOL);
    }
    async login(login_state) {
        const browser = await this.puppeteerPool.acquire();
        const url = 'https://www.toutiao.com/';
        const ctx = browser.defaultBrowserContext();
        await ctx.overridePermissions(url, ['geolocation']);
        const page = await browser.newPage();
        page.setJavaScriptEnabled(true);
        page.setViewport({
            width: 1325,
            height: 768
        });
        await page.goto(url, {
            waitUntil: ['networkidle2']
        });
        const href = page.url();
        login_state.next({
            action: 'open',
            data: href
        });
        try {
            const loginBtn = await page.waitForSelector('.login-button');
            login_state.next({
                action: 'get_login_btn',
                data: href
            });
            if (loginBtn) {
                await loginBtn.click();
                login_state.next({
                    action: 'click_login_btn',
                    data: href
                });
            }
            let img = await page.waitForSelector('img.web-login-scan-code__content__qrcode-wrapper__qrcode').catch(e => {
                return null;
            });
            while (!img) {
                if (loginBtn) {
                    await loginBtn.click();
                    login_state.next({
                        action: 'click_login_btn',
                        data: href
                    });
                    img = await page.waitForSelector('img.web-login-scan-code__content__qrcode-wrapper__qrcode').catch(e => {
                        return null;
                    });
                }
            }
            let loginImage = await page.$eval('img.web-login-scan-code__content__qrcode-wrapper__qrcode', (img) => img.src);
            login_state.next({
                action: 'get_login_qrcode',
                data: loginImage
            });
            let tip = await page.waitForSelector('p.web-login-scan-code__content__qrcode-wrapper__mask__toast__text').catch(e => {
                return null;
            });
            while (!tip) {
                tip = await page.waitForSelector('p.web-login-scan-code__content__qrcode-wrapper__mask__toast__text').catch(e => {
                    return null;
                });
            }
            const txt = await page.$eval('p.web-login-scan-code__content__qrcode-wrapper__mask__toast__text', (div) => {
                return div.innerText;
            });
            login_state.next({
                action: 'get_scan_qrcode',
                data: txt
            });
            // 扫描成功
            await page.waitForNavigation();
            // await page.setRequestInterception(false);
            const cookies = await page.cookies('https://toutiao.com/');
            login_state.next({
                action: 'login_success',
                data: url,
                cookies
            });
            let userDiv = await page.waitForSelector('div.user-icon').catch(e => {
                return null;
            });
            while (!userDiv) {
                userDiv = await page.waitForSelector('div.user-icon').catch(e => {
                    return null;
                });
            }
            const link = await page.$eval('div.user-icon > a', a => {
                const href = a.href;
                return href;
            });
            const name = await page.$eval('div.user-icon > a > span', a => {
                const href = a.innerText;
                return href;
            });
            const avatar = await page.$eval('div.user-icon > a > img', a => {
                const href = a.src;
                return href;
            });
            // https://www.toutiao.com/c/user/token/MS4wLjABAAAAAQoIjXdnz07Qc--kUzH-vG8zwuxpC4zveo0dHEtl-7npUZOUJTs8uPFMPXRg8U-V/?source=feed
            const match = /^https:.*?\/c\/user\/token\/(.*)\//.exec(link);
            let uid = '';
            if (match && match.length > 0) {
                uid = match[1];
            }
            login_state.next({
                action: 'get_userinfo',
                data: {
                    cookies,
                    uid,
                    name,
                    avatar
                }
            });
        }
        catch (e) {
            login_state.next({
                action: 'get_login_fail',
                data: e.message
            });
            login_state.error(e);
        }
        finally {
            await page.close();
            this.puppeteerPool.release(browser);
        }
    }
};
exports.ToutiaoLoginService = ToutiaoLoginService;
exports.ToutiaoLoginService = ToutiaoLoginService = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], ToutiaoLoginService);
