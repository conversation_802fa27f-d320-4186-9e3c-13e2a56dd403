"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZhihuLoginService = void 0;
const core_1 = require("@nger/core");
const utils_1 = require("@nger/utils");
const tokens_1 = require("../tokens");
let ZhihuLoginService = class ZhihuLoginService {
    injector;
    get crypto() {
        return this.injector.get(utils_1.CryptoService);
    }
    constructor(injector) {
        this.injector = injector;
    }
    delay(time) {
        const d = (0, core_1.defer)();
        setTimeout(() => {
            d.resolve();
        }, time);
        return d;
    }
    get puppeteerPool() {
        return this.injector.get(tokens_1.PUPPETEER_POOL);
    }
    async login(login_state) {
        const browser = await this.puppeteerPool.acquire();
        const url = 'https://www.zhihu.com/signin?next=%2F';
        const ctx = browser.defaultBrowserContext();
        await ctx.overridePermissions(url, ['geolocation']);
        const page = await browser.newPage();
        page.setJavaScriptEnabled(true);
        page.setViewport({
            width: 1920,
            height: 768
        });
        await page.goto(url, {
            waitUntil: ['networkidle2']
        });
        this.delay(20);
        const href = page.url();
        login_state.next({
            action: 'open',
            data: href
        });
        try {
            let loginImage = await page.$eval('div.Qrcode-img > img', (img) => img.src);
            login_state.next({
                action: 'get_login_qrcode',
                data: loginImage
            });
            const tip = await page.waitForSelector('p.Qrcode-scanResultTips', { timeout: 1000 * 60 });
            if (tip) {
                const txt = await page.$eval('p.Qrcode-scanResultTips', (div) => {
                    return div.innerText;
                });
                login_state.next({
                    action: 'get_scan_qrcode',
                    data: txt
                });
                // 扫描成功
                await page.waitForNavigation();
                // await page.setRequestInterception(false);
                const cookies = await page.cookies('https://zhihu.com/');
                const initialData = await page.$eval('script#js-initialData', script => {
                    const json = script.innerHTML;
                    return JSON.parse(json);
                });
                login_state.next({
                    action: 'login_success',
                    data: url,
                    cookies,
                    initialData
                });
                const response = await page.goto('https://www.zhihu.com/api/v4/me?include=is_active');
                if (response) {
                    const user = await response.json();
                    const uid = user.id;
                    const name = user.name;
                    const avatar = user.avatar_url;
                    login_state.next({
                        action: 'get_userinfo',
                        data: {
                            uid,
                            cookies,
                            name,
                            avatar
                        }
                    });
                }
            }
        }
        catch (e) {
            login_state.next({
                action: 'get_login_fail',
                data: e.message
            });
            login_state.error(e);
        }
        finally {
            await page.close();
            this.puppeteerPool.release(browser);
        }
    }
};
exports.ZhihuLoginService = ZhihuLoginService;
exports.ZhihuLoginService = ZhihuLoginService = __decorate([
    (0, core_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Injector])
], ZhihuLoginService);
