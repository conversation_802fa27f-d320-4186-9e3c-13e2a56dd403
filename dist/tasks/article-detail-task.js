"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticleDetailTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const account_1 = require("../entities/account");
const article_1 = require("../entities/article");
const article_detail_service_1 = require("../services/article-detail.service");
const actions_1 = require("./actions");
class ArticleDetailTask extends rabbitmq_1.Task {
    constructor() {
        super(actions_1.ARTICLE_DETAIL_TASK);
        this.count = 10;
    }
    async handle(injector, next) {
        const db = injector.get(typeorm_1.Db);
        const task = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        const manager = injector.get(rabbitmq_1.TaskManager);
        const { link, eid, tid, uid } = task;
        if (!(link && eid && tid && uid)) {
            await complete();
            return next && next();
        }
        const service = injector.get(article_detail_service_1.ArticleDetailService);
        let data = await service.search(link, uid);
        if (!data) {
            await fail();
            return next && next();
        }
        const account = data.user;
        if (!account) {
            await fail();
            return next && next();
        }
        let a = new account_1.WbAccount();
        if (!account.id) {
            console.log(`登录失效`);
            await fail();
            return next && next();
        }
        a.avatar_hd = account.avatar_hd;
        a.avatar_large = account.avatar_large;
        a.cover_image_phone = account.cover_image_phone;
        a.description = account.description;
        a.followers_count = account.followers_count;
        a.followers_count_str = account.followers_count_str;
        a.friends_count = account.friends_count;
        a.gender = account.gender;
        a.location = account.location;
        a.mbrank = account.mbrank;
        a.mbtype = account.mbtype;
        a.name = account.idstr;
        a.title = account.screen_name;
        a.verified = account.verified;
        a.verified_reason = account.verified_reason;
        a.verified_type = account.verified_type;
        a.verified_type_ext = account.verified_type_ext;
        const oldUser = await db.manager.findOne(account_1.WbAccount, { where: { name: a.name }, select: ['aid'] });
        if (oldUser) {
            a.aid = oldUser.aid;
        }
        else {
            const oldUser = await db.manager.findOne(account_1.WbAccount, { where: { title: a.title }, select: ['aid'] });
            if (oldUser) {
                a.aid = oldUser.aid;
            }
        }
        a.pc_new = account.pc_new;
        a.planet_video = account.planet_video;
        a.platform = 'weibo';
        a.profile_image_url = account.profile_image_url;
        a.profile_url = account.profile_url;
        a.statuses_count = account.statuses_count;
        a.weihao = account.weihao;
        a = await db.manager.save(account_1.WbAccount, a);
        let t = new article_1.WbArticle();
        t.code = data.idstr;
        const old = await db.manager.findOne(article_1.WbArticle, { where: { code: t.code }, select: ['id'] });
        if (old) {
            t.id = old.id;
        }
        t.account = a.title;
        t.accountId = a.name;
        t.address = data.region_name || '';
        t.content = data.text_raw;
        t.aid = data.mblogid;
        t.code = data.idstr;
        t.commentCount = data.comments_count;
        t.shareCount = data.reposts_count;
        t.goodCount = data.attitudes_count;
        t.eid = eid;
        t.from = data.source;
        t.postAt = data.created_at;
        t.tid = tid;
        const title = data.title;
        t.tag = title.text;
        const pic_infos = data.pic_infos;
        if (pic_infos) {
            t.images = Object.keys(pic_infos).map(k => pic_infos[k]).map(item => item.original).map(it => it.url);
        }
        else {
            t.images = [];
        }
        const page_info = data.page_info;
        if (page_info) {
            const media_info = page_info.media_info;
            t.video = media_info ? media_info.mp4_720p_mp4 : '';
        }
        else {
            t.video = ``;
        }
        t.video = t.video || '';
        t = await db.manager.save(article_1.WbArticle, t);
        await manager.send({
            topic: actions_1.COMMENT_TASK,
            data: {
                code: t.code,
                uid: uid,
                aid: t.id,
                eid: t.eid,
                tid: t.tid,
                accountId: t.accountId
            }
        });
        await complete();
        next && await next();
    }
}
exports.ArticleDetailTask = ArticleDetailTask;
