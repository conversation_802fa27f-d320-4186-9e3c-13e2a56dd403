"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticleTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const article_service_1 = require("../services/article.service");
const actions_1 = require("./actions");
const user_1 = require("../entities/user");
class ArticleTask extends rabbitmq_1.Task {
    constructor() {
        super(actions_1.ARTICLE_TASK);
        this.count = 20;
    }
    async handle(injector, next) {
        const db = injector.get(typeorm_1.Db);
        const task = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        const manager = injector.get(rabbitmq_1.TaskManager);
        const { link, eid, tid, page, uid } = task;
        if (!(link && eid && tid && page)) {
            await complete();
            return next && next();
        }
        const user = await db.manager.findOne(user_1.WbUser, { where: { id: uid } });
        if (!user) {
            await complete();
            return next && next();
        }
        // 抓取主题帖子
        const articleService = injector.get(article_service_1.ArticleService);
        const data = await articleService.search(`${link}&page=${page}`, uid);
        let allArticles = data.list;
        await Promise.all(allArticles.map(async (link) => {
            // comment task
            await manager.send({
                topic: actions_1.ARTICLE_DETAIL_TASK,
                data: {
                    link: link,
                    uid,
                    tid,
                    eid
                }
            });
        }));
        // 创建文章评论抓取任务
        await complete();
        if (next) {
            return await next();
        }
    }
}
exports.ArticleTask = ArticleTask;
