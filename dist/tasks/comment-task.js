"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommentTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const comment_1 = require("../entities/comment");
const comment_service_1 = require("../services/comment.service");
const actions_1 = require("./actions");
class CommentTask extends rabbitmq_1.Task {
    constructor() {
        super(actions_1.COMMENT_TASK);
        this.count = 40;
    }
    async handle(injector, next) {
        const db = injector.get(typeorm_1.Db);
        const task = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        const comment = injector.get(comment_service_1.CommentService);
        const list = await comment.search(task.code, task.uid, task.accountId);
        Promise.all(list.map(async (li) => {
            if (!li.id) {
                return;
            }
            const c = new comment_1.WbComment();
            c.aid = task.aid;
            c.eid = task.eid;
            c.tid = task.tid;
            c.source = li.source || '';
            c.created_at = li.created_at || '';
            c.content = li.text_raw || '';
            c.like_counts = li.like_counts || 0;
            c.isLikedByMblogAuthor = !!li.isLikedByMblogAuthor;
            c.cid = li.id;
            const parent = await db.manager.save(comment_1.WbComment, c);
            // 有子评论
            if (li.comments.length > 0) {
                const children = await comment.children(task.uid, `${c.cid}`, task.accountId);
                const allChildren = children.map(children => {
                    if (!children.id) {
                        return;
                    }
                    const c = new comment_1.WbComment();
                    c.pid = parent.id;
                    c.aid = task.aid;
                    c.eid = task.eid;
                    c.tid = task.tid;
                    c.source = children.source || '';
                    c.created_at = children.created_at || '';
                    c.content = children.text_raw || '';
                    c.like_counts = children.like_counts || 0;
                    c.isLikedByMblogAuthor = !!children.isLikedByMblogAuthor;
                    c.cid = children.id;
                    return c;
                }).filter(it => !!it);
                await db.manager.save(comment_1.WbComment, allChildren);
            }
        }));
        await complete();
        next && next();
    }
}
exports.CommentTask = CommentTask;
