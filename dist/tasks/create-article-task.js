"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateArticleTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const article_service_1 = require("../services/article.service");
const actions_1 = require("./actions");
class CreateArticleTask extends rabbitmq_1.Task {
    constructor() {
        super(actions_1.CREATE_ARTICLE_TASK);
        this.count = 5;
    }
    async handle(injector, next) {
        const task = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        const manager = injector.get(rabbitmq_1.TaskManager);
        const { link, eid, tid, uid } = task;
        if (!(link && eid && tid)) {
            await complete();
            return next && next();
        }
        // 抓取主题帖子
        const articleService = injector.get(article_service_1.ArticleService);
        const data = await articleService.search(link, uid);
        if (data.total && data.total > 1) {
            for (let i = 1; i <= data.total; i++) {
                await manager.send({
                    topic: actions_1.ARTICLE_TASK,
                    data: {
                        link: link,
                        eid,
                        tid,
                        page: i,
                        uid
                    }
                });
            }
        }
        await complete();
        next && next();
    }
}
exports.CreateArticleTask = CreateArticleTask;
