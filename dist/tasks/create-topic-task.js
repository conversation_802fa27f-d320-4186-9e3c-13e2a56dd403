"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTopicTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const topic_service_1 = require("../services/topic.service");
const actions_1 = require("./actions");
class CreateTopicTask extends rabbitmq_1.Task {
    constructor() {
        super(actions_1.CREATE_TOPIC_TASK);
        this.count = 1;
    }
    async handle(injector, next) {
        const task = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        const topicService = injector.get(topic_service_1.TopicService);
        const { uid, eid, keyword } = task;
        if (!(uid && eid && keyword)) {
            await complete();
            return next && next();
        }
        const data = await topicService.search(task.uid, task.eid, task.keyword);
        const total = data.total;
        const manager = injector.get(rabbitmq_1.TaskManager);
        for (let i = 1; i <= total; i++) {
            await manager.send({
                topic: actions_1.TOPIC_TASK,
                data: {
                    uid: task.uid,
                    eid: task.eid,
                    keyword: task.keyword,
                    page: i
                }
            });
        }
        await complete();
        next && next();
    }
}
exports.CreateTopicTask = CreateTopicTask;
