"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FenciTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const count_1 = require("../entities/count");
class FenciTask extends rabbitmq_1.Task {
    constructor() {
        super(`@nger/weibo/fenci-result`);
        this.count = 1;
    }
    async handle(injector, next) {
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        const data = injector.get(rabbitmq_1.DATA);
        const db = injector.get(typeorm_1.Db);
        try {
            const { id, type } = data;
            switch (type) {
                case 'article':
                    const article = await this.handleArticle(data);
                    await db.manager.save(article);
                    await complete();
                    break;
                case 'comment':
                    const comment = await this.handleComment(data);
                    await db.manager.save(comment);
                    await complete();
                    break;
                default:
                    break;
            }
        }
        catch (e) {
            console.log(e.message);
            fail();
        }
        next && next();
    }
    async handleArticle(data) {
        const count = new count_1.CountArticle();
        count.eid = data.eid;
        count.postAt = new Date(data.postAt);
        count.aid = data.id;
        const [negative, neutral, positive, negativeWords, neutralWords, positiveWords] = this.getSentiment(data.sentiment_word);
        count.negative = negative;
        count.neutral = neutral;
        count.positive = positive;
        count.negativeWords = negativeWords;
        count.neutralWords = neutralWords;
        count.positiveWords = positiveWords;
        count.readability = Number(data.readability);
        const [acount, awords] = this.getA(data.results);
        count.a = acount;
        count.aWords = awords;
        const [vcount, vwords] = this.getV(data.results);
        count.v = vcount;
        count.vWords = vwords;
        const [dcount, dwords] = this.getD(data.results);
        count.d = dcount;
        count.dWords = dwords;
        const [ncount, nwords] = this.getN(data.results);
        count.n = ncount;
        count.nWords = nwords;
        count.l = data.text.length;
        count.emotion = Number(data.sentiment_conf);
        count.platform = data.platform;
        // 词云
        return count;
    }
    getWords(start, results) {
        const words = [];
        const list = results[0][1].filter((it, index) => {
            if (it.startsWith(start)) {
                words.push(results[0][0][index]);
                return true;
            }
            return false;
        });
        return [list.length, words];
    }
    getN(results) {
        return this.getWords('n', results);
    }
    getA(results) {
        return this.getWords('a', results);
    }
    getV(results) {
        return this.getWords('v', results);
    }
    getD(results) {
        return this.getWords('v', results);
    }
    getSentiment(results) {
        const list = results[0];
        const types = results[1];
        // negative 负面
        // neutral 中立
        // positive 正面
        let negative = 0;
        let negativeWords = [];
        let neutral = 0;
        let neutralWords = [];
        let positive = 0;
        let positiveWords = [];
        types.map((type, index) => {
            if (type === 'positive') {
                positive += 1;
                positiveWords.push(list[index]);
            }
            else if (type === 'negative') {
                negative += 1;
                negativeWords.push(list[index]);
            }
            else {
                neutral += 1;
                neutralWords.push(list[index]);
            }
        });
        return [
            negative,
            neutral,
            positive,
            negativeWords,
            neutralWords,
            positiveWords
        ];
    }
    async handleComment(data) {
        const count = new count_1.CountArticleComment();
        count.eid = data.eid;
        count.postAt = new Date(data.postAt);
        count.cid = data.id;
        const [negative, neutral, positive, negativeWords, neutralWords, positiveWords] = this.getSentiment(data.sentiment_word);
        count.negative = negative;
        count.neutral = neutral;
        count.positive = positive;
        count.negativeWords = negativeWords;
        count.neutralWords = neutralWords;
        count.positiveWords = positiveWords;
        count.readability = Number(data.readability);
        const [acount, awords] = this.getA(data.results);
        count.a = acount;
        count.aWords = awords;
        const [vcount, vwords] = this.getV(data.results);
        count.v = vcount;
        count.vWords = vwords;
        const [dcount, dwords] = this.getD(data.results);
        count.d = dcount;
        count.dWords = dwords;
        const [ncount, nwords] = this.getN(data.results);
        count.n = ncount;
        count.nWords = nwords;
        count.l = data.text.length;
        count.isChild = !!data.pid;
        count.platform = data.platform;
        // 词云
        return count;
    }
}
exports.FenciTask = FenciTask;
