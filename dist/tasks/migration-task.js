"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrationTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
class MigrationTask extends rabbitmq_1.Task {
    constructor() {
        super('@nger/migration-data-task');
    }
    async handle(injector, next) {
        const data = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        try {
            const db = injector.get(typeorm_1.Db);
            const manager = db.manager.getRepository(data.name);
            await manager.insert(data.entities);
            await complete();
        }
        catch (e) {
            console.log(e.message);
            fail();
        }
        next && next();
    }
}
exports.MigrationTask = MigrationTask;
