"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.tasksProviders = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const fenci_task_1 = require("./fenci-task");
const migration_task_1 = require("./migration-task");
// import { RealTimeTask } from "./real-time.task";
const spilder_account_task_1 = require("./spilder-account.task");
const spilder_schedule_task_1 = require("./spilder-schedule.task");
const topic_article_child_comment_task_1 = require("./topic-article-child-comment.task");
const topic_article_comment_task_1 = require("./topic-article-comment.task");
const topic_article_detail_task_1 = require("./topic-article-detail.task");
const topic_article_links_task_1 = require("./topic-article-links.task");
const topic_article_more_child_comment_task_1 = require("./topic-article-more-child-comment.task");
const topic_article_more_comment_task_1 = require("./topic-article-more-comment.task");
const topic_article_page_links_task_1 = require("./topic-article-page-links.task");
const topic_detail_task_1 = require("./topic-detail.task");
const topic_links_task_1 = require("./topic-links.task");
const topic_page_links_task_1 = require("./topic-page-links.task");
exports.tasksProviders = [
    // {
    //     provide: Task,
    //     useFactory: () => new RealTimeTask(),
    //     multi: true
    // },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new fenci_task_1.FenciTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new migration_task_1.MigrationTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new topic_page_links_task_1.TopicPageLinksTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new topic_links_task_1.TopicLinksTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new topic_detail_task_1.TopicDetailTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new topic_article_page_links_task_1.TopicArticlePageLinksTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new topic_article_links_task_1.TopicArticleLinksTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new topic_article_detail_task_1.TopicArticleDetailTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new topic_article_comment_task_1.TopicArticleCommentTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new topic_article_more_comment_task_1.TopicArticleMoreCommentTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new topic_article_child_comment_task_1.TopicArticleChildCommentTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new spilder_account_task_1.SpilderAccountTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new topic_article_more_child_comment_task_1.TopicArticleMoreChildCommentTask(),
        multi: true
    },
    {
        provide: rabbitmq_1.Task,
        useFactory: () => new spilder_schedule_task_1.SpilderScheduleTask(),
        multi: true
    }
];
