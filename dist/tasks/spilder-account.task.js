"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderAccountTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const spilder_factory_1 = require("../adapter/spilder-factory");
const spilder_account_1 = require("../entities/spilder-account");
class SpilderAccountTask extends rabbitmq_1.Task {
    constructor() {
        super(`@nger/weibo/spilder-account-task`);
        this.count = 100;
    }
    async handle(injector, next) {
        const data = injector.get(rabbitmq_1.DATA);
        const fail = injector.get(rabbitmq_1.FAIL);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const { uid, type, update } = data;
        if (!(uid && type)) {
            await complete();
            return next && await next();
        }
        try {
            const db = injector.get(typeorm_1.Db);
            const a = new spilder_account_1.SpilderAccount();
            const old = await db.manager.findOne(spilder_account_1.SpilderAccount, { where: { uid: `${uid}` }, select: ['id'] });
            if (update) {
                if (old) {
                    a.id = old.id;
                }
            }
            else {
                if (old) {
                    await complete();
                    return next && await next();
                }
            }
            const spilderFactory = injector.get(spilder_factory_1.SpilderFactory);
            const spilder = await spilderFactory.create(type);
            const account = await spilder.createAccount(Number(uid));
            if (!account) {
                await complete();
                return next && await next();
            }
            const { user } = account;
            if (!user) {
                await complete();
                return next && await next();
            }
            a.created_at = account.created_at;
            a.company = account?.company;
            a.education = account.education?.school;
            a.career = account.career?.company;
            a.birthday = account.birthday;
            a.sunshine_credit = account.sunshine_credit?.level;
            a.tags = account.label_desc ? account.label_desc.map((label) => label.name) : [];
            a.uid = user.id;
            a.nickname = user.screen_name;
            a.verified = user.verified;
            a.verified_reason = user.verified_reason;
            a.description = user.description;
            a.location = user.location;
            a.url = user.url;
            const verified_type = user.verified_type;
            if (verified_type > 0 && verified_type < 8) {
                a.verified_type = 1; // 官方认证
            }
            else if (verified_type === -1) {
                a.verified_type = -1;
            }
            else {
                a.verified_type = 2; // 个人认证
            }
            function verifv(user) {
                const t = user.verified_type, e = user.verified_type_ext;
                return 0 === t ? 1 === e ? "vgold" : "vyellow" : t > 0 && t < 8 ? -1 == e ? "approve" : "vblue" : "";
            }
            const v = verifv(user);
            a.user_type = 0;
            if (v === 'vyellow') {
                // 黄色
                a.user_type = 1;
            }
            if (v === 'approve') {
                // 灰色
                a.user_type = 2;
            }
            if (v === 'vblue') {
                // 蓝色
                a.user_type = 3;
            }
            if (v === 'vgold') {
                // 红色
                a.user_type = 4;
            }
            a.verified_type_ext = user.verified_type_ext;
            a.profile_image_url = user.profile_image_url;
            a.friends_count = user.friends_count;
            a.statuses_count = user.statuses_count;
            a.followers_count = user.followers_count;
            a.profile_url = user.profile_url;
            a.mbrank = user.mbrank;
            a.mbtype = user.mbtype;
            a.pc_new = user.pc_new;
            a.top_user = user.top_user;
            a.weihao = user.weihao;
            a.wenda = user.wenda;
            a.planet_video = user.planet_video;
            a.is_muteuser = user.is_muteuser;
            a.gender = user.gender;
            try {
                await db.manager.save(spilder_account_1.SpilderAccount, a);
            }
            catch (e) { }
            await complete();
            next && await next();
        }
        catch (e) {
            await fail();
            next && await next();
        }
    }
}
exports.SpilderAccountTask = SpilderAccountTask;
