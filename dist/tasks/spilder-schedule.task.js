"use strict";
/**
 * 定时任务
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpilderScheduleTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const task_1 = require("../entities/task");
const node_schedule_1 = require("node-schedule");
const event_1 = require("../entities/event");
const typeorm_2 = require("typeorm");
class SpilderScheduleTask extends rabbitmq_1.Task {
    constructor() {
        super(`@nger/weibo/schedule-task`);
    }
    async handle(injector, next) {
        // 定时爬取数据快照
        const data = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const { id, time, ids } = data;
        const db = injector.get(typeorm_1.Db);
        if (!(id && time && ids)) {
            await complete();
            return next && await next();
        }
        const old = await db.manager.findOne(task_1.WbTask, { where: { id } });
        if (!old) {
            await complete();
            return next && await next();
        }
        const manager = injector.get(rabbitmq_1.TaskManager);
        const job = (0, node_schedule_1.scheduleJob)(time, async (fireDate) => {
            console.log(`scheduleJob`, fireDate);
            const old = await db.manager.findOne(task_1.WbTask, { where: { id } });
            if (!old) {
                job.cancel();
                await complete();
                return next && await next();
            }
            const events = await db.manager.find(event_1.WbEvent, { where: { id: (0, typeorm_2.In)(ids) } });
            events.map(it => {
                const keywords = it.keywords;
                if (keywords) {
                    keywords.map(async (key) => {
                        await manager.send({
                            topic: '@nger/weibo/topic-page-links-task',
                            data: {
                                type: 'weibo',
                                eid: it.id,
                                keyword: key
                            }
                        });
                        await manager.send({
                            topic: '@nger/weibo/topic-page-links-task',
                            data: {
                                type: 'toutiao',
                                eid: it.id,
                                keyword: key
                            }
                        });
                        await manager.send({
                            topic: '@nger/weibo/topic-page-links-task',
                            data: {
                                type: 'zhihu',
                                eid: it.id,
                                keyword: key
                            }
                        });
                    });
                }
            });
        });
        return next && await next();
    }
}
exports.SpilderScheduleTask = SpilderScheduleTask;
