"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TopicArticleCommentTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const typeorm_2 = require("typeorm");
const spilder_factory_1 = require("../adapter/spilder-factory");
const spilder_topic_article_1 = require("../entities/spilder-topic-article");
const spilder_topic_article_comment_1 = require("../entities/spilder-topic-article-comment");
const spilder_topic_article_comment_tend_1 = require("../entities/spilder-topic-article-comment-tend");
class TopicArticleCommentTask extends rabbitmq_1.Task {
    constructor() {
        super(`@nger/weibo/topic-article-comment`);
        this.count = 10;
    }
    async handle(injector, next) {
        const db = injector.get(typeorm_1.Db);
        const data = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        let { aid, tid, eid, type, id, uid, mblogid, count } = data;
        if (!(aid && eid && type)) {
            await complete();
            return next && next();
        }
        if (!id || !uid) {
            const article = await db.manager.findOne(spilder_topic_article_1.SpilderTopicArticle, { where: { id: aid }, select: ['mid', 'uid', 'mblogid'] });
            if (article) {
                id = Number(article.mid);
                uid = Number(article.uid);
                mblogid = article.mblogid;
                if (!(id && uid)) {
                    await complete();
                    return next && next();
                }
            }
            else {
                await complete();
                return next && next();
            }
        }
        try {
            const spilderFactory = injector.get(spilder_factory_1.SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                const page = count > 200 ? 200 : count;
                const data = await spilder.loadComments(id, uid, page);
                if (!data) {
                    await complete();
                    return next && next();
                }
                const manager = injector.get(rabbitmq_1.TaskManager);
                if (data.data.length === 0) {
                    await complete();
                    return next && await next();
                }
                else {
                    const { max_id, total_number, ok } = data;
                    if (ok !== 1) {
                        await complete();
                        return next && await next();
                    }
                    if (max_id > 0) {
                        await manager.send({
                            topic: '@nger/weibo/topic-article-more-comment',
                            data: {
                                type,
                                eid,
                                tid,
                                aid,
                                max_id,
                                id,
                                uid,
                                mblogid,
                                total_number
                            }
                        });
                    }
                    const list = data.data;
                    const comments = list.map((item) => {
                        const c = new spilder_topic_article_comment_1.SpilderTopicArticleComment();
                        c.aid = aid;
                        c.tid = tid;
                        c.eid = eid;
                        c.platform = type;
                        c.created_at = new Date(item.created_at);
                        c.floor_number = item.floor_number;
                        c.isLikedByMblogAuthor = item.isLikedByMblogAuthor;
                        c.like_counts = item.like_counts;
                        c.max_id = item.max_id;
                        c.cid = item.id;
                        c.text = item.text_raw;
                        c.source = item.source;
                        const { user } = item;
                        c.uid = user.id;
                        c.nickname = user.screen_name;
                        return c;
                    });
                    const inIds = comments.map(c => c.cid);
                    const exisitComments = await db.manager.find(spilder_topic_article_comment_1.SpilderTopicArticleComment, { where: { cid: (0, typeorm_2.In)(inIds), platform: type }, select: ['cid', 'id', 'platform'] });
                    let allCommnets = comments.map(c => {
                        const item = exisitComments.find(item => item.cid === c.cid && item.platform === c.platform);
                        if (item) {
                            c.id = item.id;
                        }
                        return c;
                    });
                    allCommnets = await db.manager.save(spilder_topic_article_comment_1.SpilderTopicArticleComment, allCommnets);
                    await manager.send({
                        topic: '@nger/weibo/wait_nlp',
                        data: { comments: allCommnets, type: 'comments' }
                    });
                    // 创建子连接
                    const tends = allCommnets.map(comment => {
                        const tend = new spilder_topic_article_comment_tend_1.SpilderTopicArticleCommentTend();
                        tend.cid = comment.id;
                        tend.like_counts = comment.like_counts;
                        return tend;
                    });
                    await Promise.all(allCommnets.map(async (c) => {
                        await manager.send({
                            topic: '@nger/weibo/topic-article-child-comment',
                            data: {
                                type,
                                eid,
                                tid,
                                aid,
                                id: c.cid,
                                uid,
                                mblogid,
                                pid: c.id
                            }
                        });
                        await manager.send({
                            topic: '@nger/weibo/fenci',
                            data: {
                                id: c.id,
                                type: 'comment',
                                text: c.text
                            }
                        });
                    }));
                    await db.manager.save(spilder_topic_article_comment_tend_1.SpilderTopicArticleCommentTend, tends);
                    await complete();
                }
            }
            catch (e) {
                await spilder.delay(20);
                await fail();
            }
        }
        catch (e) {
            await fail();
        }
        return next && await next();
    }
}
exports.TopicArticleCommentTask = TopicArticleCommentTask;
