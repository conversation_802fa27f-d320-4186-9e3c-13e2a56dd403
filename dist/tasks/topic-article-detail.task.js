"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TopicArticleDetailTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const spilder_factory_1 = require("../adapter/spilder-factory");
const spilder_topic_article_1 = require("../entities/spilder-topic-article");
const spilder_topic_article_tend_1 = require("../entities/spilder-topic-article-tend");
class TopicArticleDetailTask extends rabbitmq_1.Task {
    constructor() {
        super(`@nger/weibo/topic-article-detail`);
        this.count = 2;
    }
    async handle(injector, next) {
        const db = injector.get(typeorm_1.Db);
        const data = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        const { link, tid, eid, type } = data;
        if (!(link) && !type) {
            await complete();
            return next && next();
        }
        try {
            const spilderFactory = injector.get(spilder_factory_1.SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                // 获取数据
                const detail = await spilder.createTopicArticleDetail(link);
                if (!detail) {
                    await complete();
                    return next && next();
                }
                // 数据清洗
                const manager = injector.get(rabbitmq_1.TaskManager);
                let article = new spilder_topic_article_1.SpilderTopicArticle();
                article.eid = eid;
                article.tid = tid;
                article.platform = type;
                if (!detail.mid) {
                    await complete();
                    return next && next();
                }
                article.mid = detail.mid;
                article.mblogid = detail.mblogid;
                const { user, pic_ids, page_info } = detail;
                if (pic_ids && pic_ids.length > 0) {
                    article.hasImage = true;
                }
                if (page_info) {
                    const object_type = page_info.object_type;
                    if (object_type === 'video') {
                        article.hasVideo = true;
                    }
                    // 非原创
                    if (object_type === 'article') {
                        article.hasLink = true;
                    }
                }
                if (user) {
                    article.uid = user.idstr;
                    article.nickname = user.screen_name;
                    await manager.send({
                        topic: '@nger/weibo/spilder-account-task',
                        data: {
                            uid: article.uid,
                            type,
                        }
                    });
                }
                article.attitudes_count = detail.attitudes_count;
                article.comments_count = detail.comments_count;
                article.reposts_count = detail.reposts_count;
                article.source = detail.source;
                if (detail.isLongText) {
                    // 长文本 获取长文本
                    const longText = await spilder.createTopicArticleLongTextDetail(article.mblogid);
                    article.text_raw = longText.data?.longTextContent || detail.text_raw;
                }
                else {
                    article.text_raw = detail.text_raw || '';
                }
                article.pic_bg_new = detail.pic_bg_new;
                article.textLength = detail.textLength;
                if (detail.created_at) {
                    article.create_at = new Date(detail.created_at);
                }
                const { topic_struct } = detail;
                if (topic_struct) {
                    const structs = topic_struct.map((s) => s.topic_title);
                    article.structs = structs;
                }
                else {
                    article.structs = [];
                }
                const { title } = detail;
                if (title) {
                    article.title = title.text;
                }
                article.region_name = detail.region_name;
                const exisit = await db.manager.findOne(spilder_topic_article_1.SpilderTopicArticle, { where: { mid: article.mid } });
                if (exisit) {
                    article.id = exisit.id;
                }
                article = await db.manager.save(spilder_topic_article_1.SpilderTopicArticle, article);
                console.log('get a article', article.id);
                // 文章详情
                await manager.send({
                    topic: '@nger/weibo/wait_nlp',
                    data: { article, type: 'article' }
                });
                try {
                    const st = new spilder_topic_article_tend_1.SpilderTopicArticleTend();
                    st.aid = article.id;
                    st.attitudes_count = article.attitudes_count;
                    st.comments_count = article.comments_count;
                    st.reposts_count = article.reposts_count;
                    await db.manager.save(spilder_topic_article_tend_1.SpilderTopicArticleTend, st);
                }
                catch (e) {
                    console.error(e.message);
                }
                // 获取评论总数
                await manager.send({
                    topic: '@nger/weibo/topic-article-comment',
                    data: {
                        type,
                        eid,
                        tid,
                        aid: article.id,
                        id: article.mid,
                        uid: article.uid,
                        count: 200
                    }
                });
                await manager.send({
                    topic: '@nger/weibo/fenci',
                    data: {
                        id: article.id,
                        type: 'article',
                        text: article.text_raw
                    }
                });
                await complete();
            }
            catch (e) {
                console.error(e.message);
                await fail();
            }
        }
        catch (e) {
            console.error(e.message);
            await fail();
        }
        return next && await next();
    }
}
exports.TopicArticleDetailTask = TopicArticleDetailTask;
