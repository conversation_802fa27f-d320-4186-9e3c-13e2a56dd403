"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TopicArticleLinksTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const spilder_factory_1 = require("../adapter/spilder-factory");
class TopicArticleLinksTask extends rabbitmq_1.Task {
    constructor() {
        super(`@nger/weibo/topic-article-links`);
        this.count = 1;
    }
    async handle(injector, next) {
        const db = injector.get(typeorm_1.Db);
        const data = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        const { link, tid, eid, type } = data;
        if (!(link && tid && eid && type)) {
            await complete();
            return next && next();
        }
        try {
            const spilderFactory = injector.get(spilder_factory_1.SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                // console.info('get a link ---> ', link)
                const links = await spilder.createTopicArticleLinks(link).catch((e) => {
                    console.error(e.message);
                    return [];
                });
                const manager = injector.get(rabbitmq_1.TaskManager);
                await Promise.all(links.map(link => {
                    manager.send({
                        topic: '@nger/weibo/topic-article-detail',
                        data: {
                            link,
                            eid,
                            tid,
                            type
                        }
                    });
                }));
                await complete();
            }
            catch (e) {
                console.error(e.message);
                await fail();
            }
        }
        catch (e) {
            console.error(e.message);
            await fail();
        }
        return next && await next();
    }
}
exports.TopicArticleLinksTask = TopicArticleLinksTask;
