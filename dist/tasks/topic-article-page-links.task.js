"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TopicArticlePageLinksTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const spilder_factory_1 = require("../adapter/spilder-factory");
class TopicArticlePageLinksTask extends rabbitmq_1.Task {
    constructor() {
        super(`@nger/weibo/topic-article-page-links-task`);
        this.count = 100;
    }
    async handle(injector, next) {
        const db = injector.get(typeorm_1.Db);
        const data = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        const { link, tid, eid, type } = data;
        if (!(link && tid && eid && type)) {
            await complete();
            return next && next();
        }
        try {
            const spilderFactory = injector.get(spilder_factory_1.SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                const links = await spilder.createTopicArticlePageLinks(link);
                if (!(links && links.length > 0)) {
                    await complete();
                    return next && next();
                }
                const manager = injector.get(rabbitmq_1.TaskManager);
                await Promise.all(links.map(link => {
                    manager.send({
                        topic: '@nger/weibo/topic-article-links',
                        data: {
                            link,
                            eid,
                            tid,
                            type
                        }
                    });
                }));
                await complete();
            }
            catch (e) {
                await fail();
            }
        }
        catch (e) {
            await fail();
        }
        return next && await next();
    }
}
exports.TopicArticlePageLinksTask = TopicArticlePageLinksTask;
