"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TopicDetailTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const typeorm_1 = require("@nger/typeorm");
const spilder_factory_1 = require("../adapter/spilder-factory");
const event_1 = require("../entities/event");
const spilder_topic_1 = require("../entities/spilder-topic");
const spilder_topic_tend_1 = require("../entities/spilder-topic-tend");
class TopicDetailTask extends rabbitmq_1.Task {
    constructor() {
        super(`@nger/weibo/topic-detail`);
        this.count = 100;
    }
    async handle(injector, next) {
        const db = injector.get(typeorm_1.Db);
        const data = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        const { topic, eid, type } = data;
        if (!(topic && eid && type)) {
            console.log({ topic, eid, type });
            await complete();
            return next && next();
        }
        const event = db.manager.findOne(event_1.WbEvent, { where: { id: eid } });
        if (!event) {
            console.error('event not found');
            await complete();
            return next && next();
        }
        try {
            const spilderFactory = injector.get(spilder_factory_1.SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                const detail = await spilder.createTopicDetail(topic.detail);
                const manager = injector.get(rabbitmq_1.TaskManager);
                // 保存topic入库
                const { baseInfo } = detail;
                const { object, claim_info, count } = baseInfo;
                let t = new spilder_topic_1.SpilderTopic();
                t.eid = eid;
                if (object) {
                    t.cate_id = object.cate_id;
                    t.category_str = object.category_str;
                    t.title = object.display_name;
                    t.summary = object.summary;
                    t.create_at = object.create_at;
                    const { creator } = object;
                    if (creator) {
                        t.uid = creator.uid || '';
                        await manager.send({
                            topic: '@nger/weibo/spilder-account-task',
                            data: {
                                uid: t.uid,
                                type,
                            }
                        });
                    }
                    t.target_url = object.target_url;
                    t.topic_tag = object.topic_tag;
                    t.location = object.location;
                }
                else {
                    await complete();
                    return next && next();
                }
                if (claim_info) {
                    t.claim_id = claim_info.id;
                    t.claim_name = claim_info.name;
                }
                if (count) {
                    t.read_count = count.read;
                    t.ori_uv_Count = count.ori_uv;
                    t.mention_count = count.mention;
                }
                t.platform = type;
                const exisit = await db.manager.findOne(spilder_topic_1.SpilderTopic, { where: { title: t.title } });
                if (exisit) {
                    t.id = exisit.id;
                }
                /**
                 * todo
                 */
                t = await db.manager.save(spilder_topic_1.SpilderTopic, t);
                await manager.send({
                    topic: '@nger/weibo/topic-article-page-links-task',
                    data: {
                        eid,
                        type,
                        tid: t.id,
                        link: topic.list
                    }
                });
                await manager.send({
                    topic: '@nger/weibo/fenci',
                    data: {
                        id: t.id,
                        type: 'topic',
                        text: t.summary
                    }
                });
                try {
                    const st = new spilder_topic_tend_1.SpilderTopicTend();
                    st.tid = t.id;
                    st.mention_count = t.mention_count;
                    st.ori_uv_Count = t.ori_uv_Count;
                    st.read_count = t.read_count;
                    await db.manager.save(spilder_topic_tend_1.SpilderTopicTend, st);
                }
                catch (e) {
                    console.log(e.message);
                }
                await complete();
            }
            catch (e) {
                await fail();
            }
        }
        catch (e) {
            await fail();
        }
        return next && await next();
    }
}
exports.TopicDetailTask = TopicDetailTask;
