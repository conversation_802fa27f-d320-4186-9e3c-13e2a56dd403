"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TopicLinksTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const spilder_1 = require("../adapter/spilder");
const spilder_factory_1 = require("../adapter/spilder-factory");
class TopicLinksTask extends rabbitmq_1.Task {
    constructor() {
        super(`@nger/weibo/topic-links-task`);
        this.count = 100;
    }
    async handle(injector, next) {
        const data = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        const { link, eid, type } = data;
        if (!(link && eid && type)) {
            await complete();
            return next && next();
        }
        try {
            const spilderFactory = injector.get(spilder_factory_1.SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                const topics = await spilder.createTopicLinks(link);
                const manager = injector.get(rabbitmq_1.TaskManager);
                await Promise.all(topics.map((topic) => {
                    manager.send({
                        topic: '@nger/weibo/topic-detail',
                        data: {
                            topic,
                            eid,
                            type
                        }
                    });
                }));
                await complete();
            }
            catch (e) {
                if (e instanceof spilder_1.SpilderError) {
                    await complete();
                    return next && await next();
                }
                console.log(e.message);
                await spilder.delay(1000);
                await fail();
            }
        }
        catch (e) {
            await fail();
        }
        return next && await next();
    }
}
exports.TopicLinksTask = TopicLinksTask;
