"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TopicPageLinksTask = void 0;
const rabbitmq_1 = require("@nger/rabbitmq");
const spilder_1 = require("../adapter/spilder");
const spilder_factory_1 = require("../adapter/spilder-factory");
class TopicPageLinksTask extends rabbitmq_1.Task {
    constructor() {
        super(`@nger/weibo/topic-page-links-task`);
        this.count = 100;
    }
    async handle(injector, next) {
        const data = injector.get(rabbitmq_1.DATA);
        const complete = injector.get(rabbitmq_1.COMPLETE);
        const fail = injector.get(rabbitmq_1.FAIL);
        const { keyword, eid, type } = data;
        if (!(keyword && eid && type)) {
            await complete();
            return next && next();
        }
        try {
            const spilderFactory = injector.get(spilder_factory_1.SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                const { links, count } = await spilder.createTopicPageLinks(keyword);
                const manager = injector.get(rabbitmq_1.TaskManager);
                await Promise.all(links.map(link => {
                    manager.send({
                        topic: '@nger/weibo/topic-links-task',
                        data: {
                            link,
                            eid,
                            type
                        }
                    });
                }));
                await complete();
            }
            catch (e) {
                await spilder.delay(10);
                throw e;
            }
        }
        catch (e) {
            if (e instanceof spilder_1.SpilderError) {
                await complete();
                return next && await next();
            }
            console.log(e.message);
            await fail();
        }
        return next && await next();
    }
}
exports.TopicPageLinksTask = TopicPageLinksTask;
