"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.coreProviders = exports.ZHIHU_USER_POOL = exports.WEIBO_USER_POOL = exports.PUPPETEER_POOL = void 0;
const core_1 = require("@nger/core");
const puppeteer_1 = require("@nger/puppeteer");
const typeorm_1 = require("typeorm");
const spider_user_1 = require("./entities/spider_user");
const userPool_1 = require("./userPool");
const message_1 = require("./v2/message");
exports.PUPPETEER_POOL = new core_1.InjectionToken(`BROWSER_POOL`);
exports.WEIBO_USER_POOL = new core_1.InjectionToken(`WEIBO_USER_POOL`);
exports.ZHIHU_USER_POOL = new core_1.InjectionToken(`ZHIHU_USER_POOL`);
exports.coreProviders = [{
        provide: exports.PUPPETEER_POOL,
        useFactory: () => (0, puppeteer_1.createPuppeteerPool)()
    }, {
        provide: exports.WEIBO_USER_POOL,
        useFactory: (injector) => {
            const ds = injector.get(typeorm_1.DataSource);
            const r = ds.getRepository(spider_user_1.SpiderUser);
            const systemMessage = injector.get(message_1.SystemMessage);
            return (0, userPool_1.createUserPool)('weibo', r, systemMessage);
        },
        deps: [core_1.Injector]
    }, {
        provide: exports.ZHIHU_USER_POOL,
        useFactory: (injector) => {
            const ds = injector.get(typeorm_1.DataSource);
            const r = ds.getRepository(spider_user_1.SpiderUser);
            const systemMessage = injector.get(message_1.SystemMessage);
            return (0, userPool_1.createUserPool)('zhihu', r, systemMessage);
        },
        deps: [core_1.Injector]
    }];
