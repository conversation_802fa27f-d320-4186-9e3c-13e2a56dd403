"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUserPool = createUserPool;
exports.createUserPoolOptions = createUserPoolOptions;
const error_1 = require("./framework/tasks/error");
class UserPool {
    repository;
    platform;
    constructor(repository, platform) {
        this.repository = repository;
        this.platform = platform;
    }
    async acquire() {
        let users = await this.repository.findOne({
            where: { status: 2, platform: this.platform },
            order: { update_date: 'desc' },
        }).catch(e => {
            throw new error_1.UserLogoutError();
        });
        if (!users) {
            users = await this.repository.findOne({
                where: { status: 1, platform: this.platform },
                order: { update_date: 'desc' },
            }).catch(e => {
                throw new error_1.UserLogoutError();
            });
        }
        return users;
    }
    release(resource) { }
    async destroy(u) {
        await this.repository.update(u.id, { status: 1 });
    }
}
function createUserPool(platform, repository, systemMessage) {
    return new UserPool(repository, platform);
}
function createUserPoolOptions(platform, max = 100, repository, systemMessage) {
    return {
        name: 'spider-user-pool',
        create: async () => {
            const users = await repository.findOne({
                where: { status: 2, platform },
                order: { update_date: 'desc' },
            }).catch(e => {
                throw new error_1.UserLogoutError();
            });
            if (users) {
                return users;
            }
        },
        destroy: async (u) => {
            systemMessage.sendText('logout', {
                title: `${u.platform}-${u.username}登陆失效`,
                content: `用户登陆失效，请扫码重新登陆`
            });
            await repository.update(u.id, { status: 1 });
        },
        validate: (u) => u.status === 2,
        max: max,
        min: 1,
    };
}
