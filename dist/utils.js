"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMq = createMq;
const framework_1 = require("./framework");
function createMq(injector) {
    const mqFactory = injector.get(framework_1.MqFactory);
    return mqFactory.create({
        exchange: 'task.exchange',
        deadExchange: 'dead.task.exchange',
        routingKey: 'task.routingKey',
        deadRoutingKey: 'dead.task.routingKey',
        queue: 'task.queue',
        deadQueue: 'dead.task.queue',
        prefetch: 1
    });
}
