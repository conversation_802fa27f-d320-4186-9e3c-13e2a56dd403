"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Amqp = void 0;
const rxjs_1 = require("rxjs");
const amqplib_1 = require("amqplib");
class Amqp {
    url;
    type;
    constructor(url, type = 'direct') {
        this.url = url;
        this.type = type;
    }
    connect() {
        return (0, rxjs_1.from)((0, amqplib_1.connect)(this.url));
    }
    createConfirmChannel() {
        return this.connect().pipe((0, rxjs_1.switchMap)(c => {
            return new rxjs_1.Observable((sub) => {
                const channel = c.createConfirmChannel();
                channel.then(c => sub.next(c)).catch(e => sub.error(e));
                return async () => {
                    await channel.then(c => c.close());
                    await c.close();
                };
            });
        }));
    }
    consume(queue, count = 1) {
        return this.createConfirmChannel().pipe((0, rxjs_1.switchMap)(c => {
            return new rxjs_1.Observable((sub) => {
                c.prefetch(count).then(() => {
                    c.consume(queue, (msg) => {
                        if (msg) {
                            const content = msg.content.toString('utf-8');
                            const data = JSON.parse(content);
                            sub.next({
                                data,
                                ack: () => c.ack(msg),
                                nack: () => c.nack(msg, true, true),
                                reject: () => {
                                    c.reject(msg, false);
                                },
                                publish: (exchange, routingKey, ...contents) => (0, rxjs_1.from)(this.beforePublish(c, exchange, routingKey, ...contents))
                            });
                        }
                    }, { noAck: false });
                });
                return async () => { };
            });
        }));
    }
    publish(exchange, routingKey, ...contents) {
        return this.createConfirmChannel().pipe((0, rxjs_1.switchMap)(c => {
            return (0, rxjs_1.from)(this.beforePublish(c, exchange, routingKey, ...contents));
        }));
    }
    async beforePublish(channel, exchange, routingKey, ...contents) {
        await channel.assertExchange(exchange, this.type, {
            durable: true
        });
        contents.map(content => channel.publish(exchange, routingKey, Buffer.from(JSON.stringify(content))));
        return channel.waitForConfirms();
    }
}
exports.Amqp = Amqp;
