"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemMessage = void 0;
const crypto_1 = require("crypto");
const rxjs_1 = require("rxjs");
const entities_1 = require("@nger/entities");
const message_1 = require("../entities/message");
class SystemMessage {
    amqp;
    db;
    constructor(amqp, db) {
        this.amqp = amqp;
        this.db = db;
    }
    send(message) {
        message.from = 'system';
        message.create_date = new Date();
        return this.amqp.publish('message', 'system', message);
    }
    sendText(type, data) {
        const message = {
            from: 'system',
            to: '',
            type: type,
            data,
            create_date: new Date()
        };
        return this.amqp.publish('message', 'system', message);
    }
    receive() {
        return this.amqp.consume('message.system', 1).pipe((0, rxjs_1.switchMap)(msg => {
            return this.handler(msg);
        }));
    }
    handler(msg) {
        return new rxjs_1.Observable((sub) => {
            this.saveMsg(msg.data).then(() => {
                msg.ack();
            }).catch(e => {
                console.error(e);
                msg.reject();
            }).finally(() => {
                sub.next(msg);
            });
        });
    }
    async saveMsg(msg) {
        // 查找所有用户
        const users = await this.db.manager.find(entities_1.User, {});
        const msgList = users.map(user => {
            return { ...msg, to: `${user.uid}` };
        }).flat();
        const allMessage = msgList.map(m => {
            const msg = new message_1.SysMessageEntity();
            msg.to = m.to;
            msg.from = m.from;
            msg.sn = this.createSn(m);
            msg.data = JSON.stringify(m.data);
            msg.send_date = m.create_date;
            msg.type = m.type;
            return msg;
        });
        const toInserts = [];
        allMessage.map(it => {
            const isExisit = toInserts.find(t => {
                return t.sn === it.sn;
            });
            if (!isExisit) {
                toInserts.push(it);
            }
        });
        if (toInserts.length > 0)
            await this.db.manager.save(message_1.SysMessageEntity, toInserts);
    }
    createSn(msg) {
        return (0, crypto_1.createHash)('md5').update(JSON.stringify(msg)).digest('base64');
    }
}
exports.SystemMessage = SystemMessage;
