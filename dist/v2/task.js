"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpiderMiddleware = exports.DownloaderMiddleware = exports.Pipeline = exports.Spider = exports.Downloader = exports.Scheduler = exports.Engine = exports.CrawlingTask = void 0;
exports.isResponse = isResponse;
const rxjs_1 = require("rxjs");
const amqp_1 = require("./amqp");
const amqp = new amqp_1.Amqp({
    hostname: '***************',
    port: 5672,
    username: 'imeepos',
    password: '123qwe'
});
function isResponse(val) {
    return false;
}
class CrawlingTask {
    id;
    // 类型 search position
    type;
    // 关键字
    condiction;
    // 平台
    platform;
    // 开始时间
    start;
    // 结束时间
    end;
    // 步长
    step;
    // 定时
    schedule;
}
exports.CrawlingTask = CrawlingTask;
// 引擎负责控制数据流在系统中所有组件中流动, 并在相应动作发生时触发事件
class Engine {
    spider;
    start() {
        amqp.consume('request', 1).pipe((0, rxjs_1.switchMap)((request) => {
            return this.spider.request(request.data);
        })).subscribe();
    }
}
exports.Engine = Engine;
// 调度器从引擎接受request并将他们入队, 以便之后引擎请求他们时提供给引擎
class Scheduler {
}
exports.Scheduler = Scheduler;
// 下载器负责获取页面数据并提供给引擎，而后提供给spider
class Downloader {
    start() {
        // req to res
        amqp.consume('request', 1).pipe((0, rxjs_1.switchMap)((msg) => {
            return this.getHeaders().pipe((0, rxjs_1.switchMap)(headers => {
                const req = msg.data;
                req.headers = headers;
                return this.download(req).pipe((0, rxjs_1.switchMap)(res => {
                    if (Array.isArray(res)) {
                        const responses = res.filter(it => isResponse(it));
                        const datas = res.filter(it => !isResponse(it));
                        return this.multiSave(datas).pipe((0, rxjs_1.switchMap)(() => {
                            return msg.publish('nger', 'response', ...responses);
                        }));
                    }
                    else {
                        if (isResponse(res)) {
                            return msg.publish('nger', 'response', res);
                        }
                        else {
                            return this.save(res);
                        }
                    }
                }));
            }));
        })).subscribe();
    }
    // req to res
    download(request) {
        return (0, rxjs_1.of)([]);
    }
    // get headers
    getHeaders() {
        return (0, rxjs_1.of)({});
    }
    // save single data
    save(data) {
        return (0, rxjs_1.of)();
    }
    // save multi data
    multiSave(data) {
        return (0, rxjs_1.of)();
    }
}
exports.Downloader = Downloader;
class Spider {
    platform;
    type;
    request(req) {
        // 发现新的 req 
        throw new Error();
    }
    pushRequest(req) {
        return amqp.publish('nger', 'request', req);
    }
}
exports.Spider = Spider;
// 典型的处理有清理、 验证及持久化
class Pipeline {
}
exports.Pipeline = Pipeline;
// 下载器中间件是在引擎及下载器之间的特定钩子
class DownloaderMiddleware {
}
exports.DownloaderMiddleware = DownloaderMiddleware;
// Spider中间件是在引擎及Spider之间的特定钩子
class SpiderMiddleware {
}
exports.SpiderMiddleware = SpiderMiddleware;
// 
