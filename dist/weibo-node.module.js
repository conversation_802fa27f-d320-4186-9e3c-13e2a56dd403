"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeiboNodeModule = void 0;
const core_1 = require("@nger/core");
const typeorm_1 = require("@nger/typeorm");
const spilder_factory_1 = require("./adapter/spilder-factory");
const city_1 = require("./entities/city");
const event_1 = require("./entities/event");
const event_category_1 = require("./entities/event-category");
const event_type_1 = require("./entities/event-type");
const export_log_1 = require("./entities/export-log");
const platform_1 = require("./entities/platform");
const spilder_account_1 = require("./entities/spilder-account");
const spilder_topic_1 = require("./entities/spilder-topic");
const spilder_topic_article_1 = require("./entities/spilder-topic-article");
const spilder_topic_article_comment_1 = require("./entities/spilder-topic-article-comment");
const spilder_topic_article_comment_tend_1 = require("./entities/spilder-topic-article-comment-tend");
const spilder_topic_article_tend_1 = require("./entities/spilder-topic-article-tend");
const spilder_topic_tend_1 = require("./entities/spilder-topic-tend");
const task_1 = require("./entities/task");
const providers_1 = require("./tasks/providers");
const spilder_topic_article_comment_unique_1 = require("./entities/spilder-topic-article-comment-unique");
const comment_service_1 = require("./services/comment.service");
const count_1 = require("./entities/count");
let WeiboNodeModule = class WeiboNodeModule {
};
exports.WeiboNodeModule = WeiboNodeModule;
exports.WeiboNodeModule = WeiboNodeModule = __decorate([
    (0, core_1.Module)({
        providers: [
            spilder_factory_1.SpilderFactory,
            ...providers_1.tasksProviders,
            comment_service_1.CommentService
        ],
        imports: [
            typeorm_1.TypeormModule.forFeature([
                city_1.WbCity,
                event_1.WbEvent,
                event_type_1.WbEventType,
                event_category_1.WbEventCategory,
                platform_1.WbPlatform,
                spilder_topic_1.SpilderTopic,
                spilder_topic_article_1.SpilderTopicArticle,
                spilder_topic_article_comment_1.SpilderTopicArticleComment,
                spilder_account_1.SpilderAccount,
                export_log_1.SpilderExportLog,
                spilder_topic_tend_1.SpilderTopicTend,
                spilder_topic_article_tend_1.SpilderTopicArticleTend,
                spilder_topic_article_comment_tend_1.SpilderTopicArticleCommentTend,
                task_1.WbTask,
                spilder_topic_article_comment_unique_1.SpilderTopicArticleCommentUnique,
                count_1.CountArticle,
                count_1.CountArticleComment,
            ]),
        ],
        controllers: []
    })
], WeiboNodeModule);
