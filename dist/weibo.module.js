"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeiboModule = void 0;
const core_1 = require("@nger/core");
const typeorm_1 = require("@nger/typeorm");
const spilder_factory_1 = require("./adapter/spilder-factory");
const fenci_controller_1 = require("./controllers/fenci.controller");
const login_controller_1 = require("./controllers/login.controller");
const system_info_1 = require("./controllers/system-info");
const city_1 = require("./entities/city");
const event_1 = require("./entities/event");
const event_category_1 = require("./entities/event-category");
const event_type_1 = require("./entities/event-type");
const export_log_1 = require("./entities/export-log");
const platform_1 = require("./entities/platform");
const spilder_account_1 = require("./entities/spilder-account");
const spilder_topic_1 = require("./entities/spilder-topic");
const spilder_topic_article_1 = require("./entities/spilder-topic-article");
const spilder_topic_article_comment_1 = require("./entities/spilder-topic-article-comment");
const spilder_topic_article_comment_tend_1 = require("./entities/spilder-topic-article-comment-tend");
const spilder_topic_article_tend_1 = require("./entities/spilder-topic-article-tend");
const spilder_topic_tend_1 = require("./entities/spilder-topic-tend");
const task_1 = require("./entities/task");
const city_migration_1 = require("./migrations/city.migration");
const event_category_data_migration_1 = require("./migrations/event-category-data.migration");
const event_category_migration_1 = require("./migrations/event-category.migration");
const event_type_data_migration_1 = require("./migrations/event-type-data.migration");
const event_type_migration_1 = require("./migrations/event-type.migration");
const event_migration_1 = require("./migrations/event.migration");
const platform_data_migration_1 = require("./migrations/platform-data.migration");
const platform_migration_1 = require("./migrations/platform.migration");
const spilder_account_migration_1 = require("./migrations/spilder-account.migration");
const spilder_export_log_migration_1 = require("./migrations/spilder-export-log.migration");
const spilder_topic_article_comment_tend_migration_1 = require("./migrations/spilder-topic-article-comment-tend.migration");
const spilder_topic_article_comment_migration_1 = require("./migrations/spilder-topic-article-comment.migration");
const spilder_topic_article_tend_migration_1 = require("./migrations/spilder-topic-article-tend.migration");
const spilder_topic_article_migration_1 = require("./migrations/spilder-topic-article.migration");
const spilder_topic_tend_migration_1 = require("./migrations/spilder-topic-tend.migration");
const spilder_topic_migration_1 = require("./migrations/spilder-topic.migration");
const task_migration_1 = require("./migrations/task.migration");
const spilder_topic_article_comment_unique_1 = require("./entities/spilder-topic-article-comment-unique");
const spilder_topic_article_comment_unique_migration_1 = require("./migrations/spilder-topic-article-comment-unique.migration");
const count_migration_1 = require("./migrations/count.migration");
const count_1 = require("./entities/count");
const account_data_migration_1 = require("./migrations/account-data.migration");
const spider_online_migration_1 = require("./migrations/spider-online.migration");
const spider_online_1 = require("./entities/spider-online");
const spider_url_migration_1 = require("./migrations/spider-url.migration");
const spider_url_1 = require("./entities/spider_url");
const keyword_1 = require("./entities/keyword");
const keyword_migration_1 = require("./migrations/keyword.migration");
const spider_user_1 = require("./entities/spider_user");
const spider_user_migration_1 = require("./migrations/spider_user.migration");
const bind_user_controller_1 = require("./controllers/bind-user.controller");
const zhihu_login_1 = require("./services/zhihu.login");
const toutiao_login_1 = require("./services/toutiao.login");
const gonggao_1 = require("./entities/gonggao");
const gonggao_migration_1 = require("./migrations/gonggao.migration");
const qq_new_login_1 = require("./services/qq-new.login");
const spider_news_1 = require("./entities/spider_news");
const spider_news_migration_1 = require("./migrations/spider-news.migration");
const setting_migration_1 = require("./migrations/setting.migration");
const news_migration_1 = require("./migrations/news.migration");
const setting_1 = require("./entities/setting");
const news_1 = require("./entities/news");
const message_1 = require("./entities/message");
const message_2 = require("./controllers/message");
const message_3 = require("./v2/message");
const amqp_1 = require("./v2/amqp");
const crawling_url_migration_1 = require("./migrations/crawling_url.migration");
const crawling_url_1 = require("./entities/crawling_url");
const crawling_controller_1 = require("./controllers/crawling.controller");
const tokens_1 = require("./tokens");
const framework_1 = require("./framework");
const message_migration_1 = require("./migrations/message.migration");
let WeiboModule = class WeiboModule {
};
exports.WeiboModule = WeiboModule;
exports.WeiboModule = WeiboModule = __decorate([
    (0, core_1.Module)({
        providers: [
            ...(0, typeorm_1.createMigration)(city_migration_1.city1657731735130),
            ...(0, typeorm_1.createMigration)(event_migration_1.event1657763465436),
            ...(0, typeorm_1.createMigration)(platform_migration_1.platform1658554174060),
            ...(0, typeorm_1.createMigration)(platform_data_migration_1.platform1658554213740),
            ...(0, typeorm_1.createMigration)(spilder_topic_migration_1.spilderTopic1658645005229),
            ...(0, typeorm_1.createMigration)(spilder_topic_article_migration_1.spilderTopicArticle1658666059979),
            ...(0, typeorm_1.createMigration)(spilder_topic_article_comment_migration_1.spilderTopicArticleComment1658671163802),
            ...(0, typeorm_1.createMigration)(event_type_migration_1.WbEventType1658900303914),
            ...(0, typeorm_1.createMigration)(event_type_data_migration_1.WbEventTypeData1658900374793),
            ...(0, typeorm_1.createMigration)(event_category_migration_1.WbEventCategory1658900342571),
            ...(0, typeorm_1.createMigration)(event_category_data_migration_1.WbEventCategoryData1658900661284),
            ...(0, typeorm_1.createMigration)(spilder_account_migration_1.SpilderAccount1658935369563),
            ...(0, typeorm_1.createMigration)(spilder_export_log_migration_1.SpilderExportLog1659426673921),
            ...(0, typeorm_1.createMigration)(spilder_topic_tend_migration_1.SpilderTopicTend1659433128281),
            ...(0, typeorm_1.createMigration)(spilder_topic_article_tend_migration_1.SpilderTopicArticleTend1659433177432),
            ...(0, typeorm_1.createMigration)(spilder_topic_article_comment_tend_migration_1.SpilderTopicArticleCommentTend1659433214868),
            ...(0, typeorm_1.createMigration)(spilder_topic_article_comment_unique_migration_1.spilderTopicArticleCommentUnique1661934452670),
            ...(0, typeorm_1.createMigration)(task_migration_1.WbTask1659433453188),
            ...(0, typeorm_1.createMigration)(count_migration_1.count1662172062070),
            ...(0, typeorm_1.createMigration)(account_data_migration_1.Account1662542482429),
            ...(0, typeorm_1.createMigration)(spider_online_migration_1.SpiderOnline1663750157077),
            ...(0, typeorm_1.createMigration)(spider_url_migration_1.SpiderUrl1663773389836),
            ...(0, typeorm_1.createMigration)(keyword_migration_1.Keyword1664352047120),
            ...(0, typeorm_1.createMigration)(spider_user_migration_1.SpiderUser1664358244796),
            ...(0, typeorm_1.createMigration)(gonggao_migration_1.Gonggao1665026397032),
            ...(0, typeorm_1.createMigration)(spider_news_migration_1.SpiderNews1665754851939),
            ...(0, typeorm_1.createMigration)(setting_migration_1.Setting1665820822756),
            ...(0, typeorm_1.createMigration)(news_migration_1.News1665820797372),
            ...(0, typeorm_1.createMigration)(crawling_url_migration_1.CrawlingUrl1675300939131),
            ...(0, typeorm_1.createMigration)(message_migration_1.SysMessage1676478574476),
            zhihu_login_1.ZhihuLoginService,
            toutiao_login_1.ToutiaoLoginService,
            spilder_factory_1.SpilderFactory,
            qq_new_login_1.QqNewLoginService,
            ...tokens_1.coreProviders,
            {
                provide: amqp_1.Amqp,
                useFactory: () => {
                    const options = {
                        hostname: process.env['MQ_HOST'] || '***************',
                        port: parseInt(process.env['MQ_PORT'] || '5672'),
                        username: process.env['MQ_USER'] || 'imeepos',
                        password: process.env['MQ_PASS'] || '123qwe',
                        protocol: 'amqp'
                    };
                    return new amqp_1.Amqp(options);
                },
                deps: []
            },
            {
                provide: message_3.SystemMessage,
                useFactory: (injector) => {
                    const db = injector.get(typeorm_1.Db);
                    const amqp = injector.get(amqp_1.Amqp);
                    return new message_3.SystemMessage(amqp, db);
                },
                deps: [core_1.Injector]
            }
        ],
        imports: [
            framework_1.RabbitmqModule.forRoot(),
            typeorm_1.TypeormModule.forFeature([
                city_1.WbCity,
                event_1.WbEvent,
                event_type_1.WbEventType,
                event_category_1.WbEventCategory,
                platform_1.WbPlatform,
                spilder_topic_1.SpilderTopic,
                spilder_topic_article_1.SpilderTopicArticle,
                spilder_topic_article_comment_1.SpilderTopicArticleComment,
                spilder_account_1.SpilderAccount,
                export_log_1.SpilderExportLog,
                spilder_topic_tend_1.SpilderTopicTend,
                spilder_topic_article_tend_1.SpilderTopicArticleTend,
                spilder_topic_article_comment_tend_1.SpilderTopicArticleCommentTend,
                task_1.WbTask,
                spilder_topic_article_comment_unique_1.SpilderTopicArticleCommentUnique,
                count_1.CountArticle,
                count_1.CountArticleComment,
                spider_online_1.SpiderOnline,
                spider_url_1.SpiderUrl,
                keyword_1.Keyword,
                spider_user_1.SpiderUser,
                gonggao_1.Gonggao,
                spider_news_1.SpiderNews,
                setting_1.Setting,
                news_1.News,
                message_1.SysMessageEntity,
                crawling_url_1.CrawlingUrl
            ]),
            framework_1.RabbitmqModule.forRoot(),
            framework_1.TasksModule.forRoot(),
            framework_1.RedisModule,
        ],
        controllers: [
            login_controller_1.LoginController,
            system_info_1.SystemInfo,
            fenci_controller_1.FenciController,
            bind_user_controller_1.BindUserController,
            message_2.MessageController,
            crawling_controller_1.CrawlingController
        ]
    })
], WeiboModule);
