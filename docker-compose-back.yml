version: '3.1'

services:
  zookeeper:
    image: zookeeper
    restart: always
    ports:
      - 2181:2181
  postgres:
    image: postgres
    restart: always
    environment:
      - POSTGRES_PASSWORD=123qwe
    ports:
      - 5432:5432
  redis:
    image: redis
    restart: always
    ports:
      - 6379:6379
  rabbitmq:
    image: rabbitmq:3.7-management-alpine
    restart: always
    environment:
      - RABBITMQ_DEFAULT_USER=imeepos
      - RABBITMQ_DEFAULT_PASS=123qwe
    ports:
      - "4369:4369"
      - "5671:5671"
      - "5672:5672"
      - "15671:15671"
      - "15672:15672"
      - "25672:25672"
      - "8083:8080"
  