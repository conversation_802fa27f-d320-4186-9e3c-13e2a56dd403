FROM node:lts-slim
RUN sed -i 's#http://deb.debian.org#http://mirrors.cloud.tencent.com#g;s#http://security.debian.org#http://mirrors.cloud.tencent.com#g' /etc/apt/sources.list 
RUN apt-get -o Acquire::Check-Valid-Until=false update \
    && apt-get install -y \
    ca-certificates \
    fonts-freefont-ttf \
    fonts-ipafont-gothic \
    fonts-kacst \
    fonts-liberation \
    fonts-thai-tlwg \
    fonts-wqy-zenhei \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgbm1 \
    libgcc1 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    lsb-release \
    wget \
    xdg-utils \
    patch \
    build-essential \
    gcc \
    g++ \
    libtool \
    automake \
    curl \
    --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*
# RUN curl https://www.openssl.org/source/openssl-1.1.1-pre2.tar.gz | tar xz && cd openssl-1.0.2l && ./config && make