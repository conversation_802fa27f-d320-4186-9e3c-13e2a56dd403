{"name": "@nger/weibo", "version": "1.0.0", "description": "", "main": "index.js", "bin": {"weibo-master": "dist/master.js", "weibo-node": "dist/node.js"}, "scripts": {"start": "ts-node -r tsconfig-paths/register src/master.ts", "master": "ts-node -r tsconfig-paths/register src/master.ts", "test": "ts-node -r tsconfig-paths/register src/create-redis.ts", "tsc": "tsc", "pm2": "pm2 start -i 10 dist/main.js", "restart": "pm2 restart main", "kill": "pm2 kill"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@nger/core": "^1.10.38", "@nger/entities": "^1.1.14", "@nger/http": "^4.0.76", "@nger/install": "^1.0.45", "@nger/oauth2": "^1.0.3", "@nger/puppeteer": "^1.0.3", "@nger/rabbitmq": "^1.0.51", "@nger/redis": "^1.0.5", "@nger/rest": "^1.0.187", "@nger/sirv": "^1.0.41", "@nger/template": "^1.0.2", "@nger/typeorm": "^1.0.40", "@nger/utils": "^1.0.3", "@nger/weibo": "file:", "@nger/zookeeper": "^1.0.21", "@types/node-os-utils": "^1.3.0", "@types/node-schedule": "^2.1.0", "@types/os-utils": "^0.0.1", "@types/puppeteer": "^5.4.6", "@types/ws": "^8.5.3", "axios": "^0.27.2", "encodeurl": "^1.0.2", "koa": "^2.13.4", "node-cmd": "^5.0.0", "node-os-utils": "^1.3.7", "node-schedule": "^2.1.0", "node-xlsx": "^0.21.0", "os-utils": "^0.0.14", "pg": "^8.8.0", "pm2": "^5.2.0", "puppeteer": "^16.2.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.5.6", "typeorm": "^0.3.9", "ws": "^8.8.1"}, "devDependencies": {"@types/node": "^18.7.13", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.0", "typescript": "^4.8.2"}}