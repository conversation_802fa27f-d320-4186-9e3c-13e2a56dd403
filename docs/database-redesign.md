# 微博爬虫项目数据库重新设计

## 📋 设计原则

### 兼容性原则
- **完全保持现有表结构**: 不删除任何现有字段
- **只增不减**: 只添加新字段和表，不修改现有结构
- **数据类型一致**: 保持所有字段的数据类型和约束
- **关系保持**: 维持现有的外键关系和索引

### 优化原则
- **性能优化**: 添加必要的索引和分区
- **扩展性**: 支持新平台和新功能
- **规范化**: 改善数据结构设计
- **监控支持**: 添加系统监控相关表

## 🗄️ 核心数据表设计

### 1. 平台管理表 (wb_platform)
**现有结构保持不变，新增字段**:
```sql
-- 现有字段保持不变
ALTER TABLE wb_platform ADD COLUMN IF NOT EXISTS api_config JSONB;           -- API配置信息
ALTER TABLE wb_platform ADD COLUMN IF NOT EXISTS rate_limit INTEGER DEFAULT 1000;  -- 请求频率限制
ALTER TABLE wb_platform ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;   -- 是否启用
ALTER TABLE wb_platform ADD COLUMN IF NOT EXISTS last_check_time TIMESTAMPTZ;      -- 最后检查时间
ALTER TABLE wb_platform ADD COLUMN IF NOT EXISTS error_count INTEGER DEFAULT 0;    -- 错误计数

-- 新增索引
CREATE INDEX IF NOT EXISTS idx_wb_platform_name ON wb_platform(name);
CREATE INDEX IF NOT EXISTS idx_wb_platform_status ON wb_platform(status);
```

### 2. 事件管理表 (wb_event)
**现有结构保持不变，新增字段**:
```sql
-- 现有字段保持不变
ALTER TABLE wb_event ADD COLUMN IF NOT EXISTS status INTEGER DEFAULT 1;           -- 事件状态 (1:活跃, 0:暂停, -1:结束)
ALTER TABLE wb_event ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 5;        -- 优先级 (1-10)
ALTER TABLE wb_event ADD COLUMN IF NOT EXISTS start_time TIMESTAMPTZ;           -- 事件开始时间
ALTER TABLE wb_event ADD COLUMN IF NOT EXISTS end_time TIMESTAMPTZ;             -- 事件结束时间
ALTER TABLE wb_event ADD COLUMN IF NOT EXISTS auto_crawl BOOLEAN DEFAULT true;   -- 是否自动爬取
ALTER TABLE wb_event ADD COLUMN IF NOT EXISTS crawl_interval INTEGER DEFAULT 3600; -- 爬取间隔(秒)

-- 新增索引
CREATE INDEX IF NOT EXISTS idx_wb_event_status ON wb_event(status);
CREATE INDEX IF NOT EXISTS idx_wb_event_priority ON wb_event(priority);
CREATE INDEX IF NOT EXISTS idx_wb_event_time_range ON wb_event(start_time, end_time);
```

### 3. 话题表 (spilder_topic)
**现有结构保持不变，新增字段**:
```sql
-- 现有字段保持不变
ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS crawl_status INTEGER DEFAULT 0;     -- 爬取状态
ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS last_crawl_time TIMESTAMPTZ;        -- 最后爬取时间
ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS next_crawl_time TIMESTAMPTZ;        -- 下次爬取时间
ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS crawl_count INTEGER DEFAULT 0;      -- 爬取次数
ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS error_count INTEGER DEFAULT 0;      -- 错误次数
ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS is_trending BOOLEAN DEFAULT false;  -- 是否热门
ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS sentiment_score FLOAT;             -- 情感得分
ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS article_count INTEGER DEFAULT 0;    -- 文章数量
ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS comment_count INTEGER DEFAULT 0;    -- 评论数量
ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS hash_tags TEXT[];                   -- 相关标签

-- 新增索引
CREATE INDEX IF NOT EXISTS idx_spilder_topic_platform ON spilder_topic(platform);
CREATE INDEX IF NOT EXISTS idx_spilder_topic_eid ON spilder_topic(eid);
CREATE INDEX IF NOT EXISTS idx_spilder_topic_status ON spilder_topic(crawl_status);
CREATE INDEX IF NOT EXISTS idx_spilder_topic_trending ON spilder_topic(is_trending);
CREATE INDEX IF NOT EXISTS idx_spilder_topic_create_date ON spilder_topic(create_date);
CREATE INDEX IF NOT EXISTS idx_spilder_topic_read_count ON spilder_topic(read_count);
```

### 4. 文章表 (spilder_topic_article)
**现有结构保持不变，新增字段**:
```sql
-- 现有字段保持不变
ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS crawl_status INTEGER DEFAULT 0;    -- 爬取状态
ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS last_crawl_time TIMESTAMPTZ;       -- 最后爬取时间
ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS sentiment_score FLOAT;            -- 情感得分
ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS readability_score FLOAT;          -- 可读性得分
ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS word_count INTEGER DEFAULT 0;      -- 词数统计
ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS is_original BOOLEAN DEFAULT true;  -- 是否原创
ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS parent_id INTEGER;                 -- 转发来源ID
ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS media_urls TEXT[];                 -- 媒体文件URL
ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS mentions TEXT[];                   -- 提及的用户
ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS hashtags TEXT[];                   -- 话题标签

-- 新增索引
CREATE INDEX IF NOT EXISTS idx_spilder_topic_article_tid ON spilder_topic_article(tid);
CREATE INDEX IF NOT EXISTS idx_spilder_topic_article_eid ON spilder_topic_article(eid);
CREATE INDEX IF NOT EXISTS idx_spilder_topic_article_uid ON spilder_topic_article(uid);
CREATE INDEX IF NOT EXISTS idx_spilder_topic_article_create_at ON spilder_topic_article(create_at);
CREATE INDEX IF NOT EXISTS idx_spilder_topic_article_status ON spilder_topic_article(crawl_status);
CREATE INDEX IF NOT EXISTS idx_spilder_topic_article_sentiment ON spilder_topic_article(sentiment_score);
```

### 5. 评论表 (spilder_topic_article_comment)
**现有结构保持不变，新增字段**:
```sql
-- 现有字段保持不变
ALTER TABLE spilder_topic_article_comment ADD COLUMN IF NOT EXISTS crawl_status INTEGER DEFAULT 0;  -- 爬取状态
ALTER TABLE spilder_topic_article_comment ADD COLUMN IF NOT EXISTS last_crawl_time TIMESTAMPTZ;     -- 最后爬取时间
ALTER TABLE spilder_topic_article_comment ADD COLUMN IF NOT EXISTS sentiment_score FLOAT;          -- 情感得分
ALTER TABLE spilder_topic_article_comment ADD COLUMN IF NOT EXISTS word_count INTEGER DEFAULT 0;    -- 词数统计
ALTER TABLE spilder_topic_article_comment ADD COLUMN IF NOT EXISTS reply_count INTEGER DEFAULT 0;   -- 回复数量
ALTER TABLE spilder_topic_article_comment ADD COLUMN IF NOT EXISTS depth INTEGER DEFAULT 0;         -- 评论层级深度

-- 新增索引
CREATE INDEX IF NOT EXISTS idx_spilder_comment_aid ON spilder_topic_article_comment(aid);
CREATE INDEX IF NOT EXISTS idx_spilder_comment_tid ON spilder_topic_article_comment(tid);
CREATE INDEX IF NOT EXISTS idx_spilder_comment_eid ON spilder_topic_article_comment(eid);
CREATE INDEX IF NOT EXISTS idx_spilder_comment_uid ON spilder_topic_article_comment(uid);
CREATE INDEX IF NOT EXISTS idx_spilder_comment_pid ON spilder_topic_article_comment(pid);
CREATE INDEX IF NOT EXISTS idx_spilder_comment_created_at ON spilder_topic_article_comment(created_at);
CREATE INDEX IF NOT EXISTS idx_spilder_comment_status ON spilder_topic_article_comment(crawl_status);
```

### 6. 账号表 (spilder_account)
**现有结构保持不变，新增字段**:
```sql
-- 现有字段保持不变
ALTER TABLE spilder_account ADD COLUMN IF NOT EXISTS last_active_time TIMESTAMPTZ;        -- 最后活跃时间
ALTER TABLE spilder_account ADD COLUMN IF NOT EXISTS influence_score FLOAT DEFAULT 0;     -- 影响力得分
ALTER TABLE spilder_account ADD COLUMN IF NOT EXISTS activity_level INTEGER DEFAULT 1;    -- 活跃度等级
ALTER TABLE spilder_account ADD COLUMN IF NOT EXISTS is_bot BOOLEAN DEFAULT false;        -- 是否机器人账号
ALTER TABLE spilder_account ADD COLUMN IF NOT EXISTS account_age INTEGER;                 -- 账号年龄(天)
ALTER TABLE spilder_account ADD COLUMN IF NOT EXISTS avg_posts_per_day FLOAT DEFAULT 0;   -- 日均发帖数

-- 新增索引
CREATE INDEX IF NOT EXISTS idx_spilder_account_uid_platform ON spilder_account(uid, platform);
CREATE INDEX IF NOT EXISTS idx_spilder_account_platform ON spilder_account(platform);
CREATE INDEX IF NOT EXISTS idx_spilder_account_type ON spilder_account(type);
CREATE INDEX IF NOT EXISTS idx_spilder_account_influence ON spilder_account(influence_score);
```

## 🆕 新增系统表

### 7. 爬虫任务表 (spider_crawl_tasks)
```sql
CREATE TABLE IF NOT EXISTS spider_crawl_tasks (
    id SERIAL PRIMARY KEY,
    task_type VARCHAR(50) NOT NULL,                    -- 任务类型
    task_name VARCHAR(255) NOT NULL,                   -- 任务名称
    platform VARCHAR(50) NOT NULL,                     -- 平台
    target_id INTEGER,                                 -- 目标ID (topic_id, article_id等)
    target_url TEXT,                                   -- 目标URL
    priority INTEGER DEFAULT 5,                       -- 优先级 (1-10)
    status INTEGER DEFAULT 0,                         -- 状态 (0:待处理, 1:处理中, 2:完成, -1:失败)
    retry_count INTEGER DEFAULT 0,                    -- 重试次数
    max_retries INTEGER DEFAULT 3,                    -- 最大重试次数
    scheduled_time TIMESTAMPTZ DEFAULT NOW(),         -- 计划执行时间
    started_time TIMESTAMPTZ,                         -- 开始时间
    completed_time TIMESTAMPTZ,                       -- 完成时间
    error_message TEXT,                               -- 错误信息
    result_data JSONB,                                -- 结果数据
    config_data JSONB,                                -- 配置数据
    created_date TIMESTAMPTZ DEFAULT NOW(),
    updated_date TIMESTAMPTZ DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_spider_tasks_type ON spider_crawl_tasks(task_type);
CREATE INDEX idx_spider_tasks_platform ON spider_crawl_tasks(platform);
CREATE INDEX idx_spider_tasks_status ON spider_crawl_tasks(status);
CREATE INDEX idx_spider_tasks_priority ON spider_crawl_tasks(priority);
CREATE INDEX idx_spider_tasks_scheduled ON spider_crawl_tasks(scheduled_time);
```

### 8. 爬虫日志表 (spider_crawl_logs)
```sql
CREATE TABLE IF NOT EXISTS spider_crawl_logs (
    id SERIAL PRIMARY KEY,
    task_id INTEGER REFERENCES spider_crawl_tasks(id),
    log_level VARCHAR(20) NOT NULL,                   -- 日志级别 (INFO, WARN, ERROR)
    message TEXT NOT NULL,                            -- 日志消息
    details JSONB,                                    -- 详细信息
    created_date TIMESTAMPTZ DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_spider_logs_task_id ON spider_crawl_logs(task_id);
CREATE INDEX idx_spider_logs_level ON spider_crawl_logs(log_level);
CREATE INDEX idx_spider_logs_created ON spider_crawl_logs(created_date);
```

### 9. 系统配置表 (system_configs)
```sql
CREATE TABLE IF NOT EXISTS system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(255) UNIQUE NOT NULL,          -- 配置键
    config_value TEXT,                                -- 配置值
    config_type VARCHAR(50) DEFAULT 'string',         -- 配置类型
    description TEXT,                                 -- 描述
    is_encrypted BOOLEAN DEFAULT false,              -- 是否加密
    created_date TIMESTAMPTZ DEFAULT NOW(),
    updated_date TIMESTAMPTZ DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_system_configs_key ON system_configs(config_key);
```

### 10. 数据统计表 (data_statistics)
```sql
CREATE TABLE IF NOT EXISTS data_statistics (
    id SERIAL PRIMARY KEY,
    stat_date DATE NOT NULL,                          -- 统计日期
    platform VARCHAR(50) NOT NULL,                    -- 平台
    event_id INTEGER,                                 -- 事件ID
    topic_count INTEGER DEFAULT 0,                   -- 话题数量
    article_count INTEGER DEFAULT 0,                 -- 文章数量
    comment_count INTEGER DEFAULT 0,                 -- 评论数量
    user_count INTEGER DEFAULT 0,                    -- 用户数量
    avg_sentiment FLOAT DEFAULT 0,                   -- 平均情感得分
    total_interactions BIGINT DEFAULT 0,             -- 总互动数
    created_date TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(stat_date, platform, event_id)
);

-- 索引
CREATE INDEX idx_data_stats_date ON data_statistics(stat_date);
CREATE INDEX idx_data_stats_platform ON data_statistics(platform);
CREATE INDEX idx_data_stats_event ON data_statistics(event_id);
```

## 📊 数据分区策略

### 时间分区表
```sql
-- 为大表创建时间分区
-- 文章表按月分区
CREATE TABLE spilder_topic_article_y2024m01 PARTITION OF spilder_topic_article
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 评论表按月分区  
CREATE TABLE spilder_topic_article_comment_y2024m01 PARTITION OF spilder_topic_article_comment
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 日志表按周分区
CREATE TABLE spider_crawl_logs_y2024w01 PARTITION OF spider_crawl_logs
    FOR VALUES FROM ('2024-01-01') TO ('2024-01-08');
```

## 🔍 性能优化索引

### 复合索引
```sql
-- 话题表复合索引
CREATE INDEX idx_topic_platform_eid_status ON spilder_topic(platform, eid, crawl_status);
CREATE INDEX idx_topic_create_date_platform ON spilder_topic(create_date, platform);

-- 文章表复合索引
CREATE INDEX idx_article_tid_create_at ON spilder_topic_article(tid, create_at);
CREATE INDEX idx_article_platform_uid ON spilder_topic_article(platform, uid);

-- 评论表复合索引
CREATE INDEX idx_comment_aid_created_at ON spilder_topic_article_comment(aid, created_at);
CREATE INDEX idx_comment_platform_uid ON spilder_topic_article_comment(platform, uid);
```

### 全文搜索索引
```sql
-- 为文本字段创建全文搜索索引
CREATE INDEX idx_topic_title_fts ON spilder_topic USING gin(to_tsvector('chinese', title));
CREATE INDEX idx_article_text_fts ON spilder_topic_article USING gin(to_tsvector('chinese', text_raw));
CREATE INDEX idx_comment_text_fts ON spilder_topic_article_comment USING gin(to_tsvector('chinese', text));
```

## 🔧 数据库函数和触发器

### 自动更新触发器
```sql
-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_date = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为相关表添加触发器
CREATE TRIGGER trigger_update_spilder_topic
    BEFORE UPDATE ON spilder_topic
    FOR EACH ROW EXECUTE FUNCTION update_updated_date();
```

### 统计数据维护函数
```sql
-- 更新话题统计数据
CREATE OR REPLACE FUNCTION update_topic_stats(topic_id INTEGER)
RETURNS VOID AS $$
BEGIN
    UPDATE spilder_topic SET
        article_count = (
            SELECT COUNT(*) FROM spilder_topic_article WHERE tid = topic_id
        ),
        comment_count = (
            SELECT COUNT(*) FROM spilder_topic_article_comment WHERE tid = topic_id
        )
    WHERE id = topic_id;
END;
$$ LANGUAGE plpgsql;
```

## 📈 监控和维护

### 数据库监控视图
```sql
-- 创建系统状态监控视图
CREATE VIEW v_system_status AS
SELECT 
    'topics' as table_name,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE create_date >= CURRENT_DATE) as today_count
FROM spilder_topic
UNION ALL
SELECT 
    'articles' as table_name,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE create_date >= CURRENT_DATE) as today_count
FROM spilder_topic_article;
```

### 数据清理策略
```sql
-- 定期清理旧日志数据
DELETE FROM spider_crawl_logs 
WHERE created_date < NOW() - INTERVAL '30 days';

-- 归档旧数据
CREATE TABLE spilder_topic_article_archive AS 
SELECT * FROM spilder_topic_article 
WHERE create_date < NOW() - INTERVAL '1 year';
```

## 🔄 数据迁移策略

### 迁移脚本示例
```sql
-- 1. 备份现有数据
CREATE TABLE spilder_topic_backup AS SELECT * FROM spilder_topic;

-- 2. 添加新字段（使用事务确保原子性）
BEGIN;
    -- 添加新字段
    ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS crawl_status INTEGER DEFAULT 0;
    ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS last_crawl_time TIMESTAMPTZ;

    -- 初始化数据
    UPDATE spilder_topic SET crawl_status = 2 WHERE id IS NOT NULL; -- 标记为已完成
    UPDATE spilder_topic SET last_crawl_time = create_date WHERE create_date IS NOT NULL;

    -- 创建索引
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spilder_topic_status ON spilder_topic(crawl_status);
COMMIT;

-- 3. 验证数据完整性
SELECT
    COUNT(*) as original_count,
    COUNT(*) FILTER (WHERE crawl_status IS NOT NULL) as migrated_count
FROM spilder_topic;
```

### 回滚策略
```sql
-- 如果需要回滚，删除新添加的字段
ALTER TABLE spilder_topic DROP COLUMN IF EXISTS crawl_status;
ALTER TABLE spilder_topic DROP COLUMN IF EXISTS last_crawl_time;
-- 删除新创建的索引
DROP INDEX IF EXISTS idx_spilder_topic_status;
```

## 📋 Rust SeaORM 实体定义

### 话题实体 (Topic)
```rust
// src/entities/topic.rs
use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "spilder_topic")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,

    #[sea_orm(column_type = "String(Some(50))", default_value = "weibo")]
    pub platform: String,

    #[sea_orm(column_type = "String(Some(255))", default_value = "")]
    pub title: String,

    #[sea_orm(column_type = "String(Some(50))", nullable)]
    pub uid: Option<String>,

    #[sea_orm(nullable)]
    pub eid: Option<i32>,

    #[sea_orm(column_type = "String(Some(50))", default_value = "")]
    pub cate_id: String,

    #[sea_orm(column_type = "Text", default_value = "")]
    pub summary: String,

    #[sea_orm(column_type = "String(Some(500))", default_value = "")]
    pub target_url: String,

    #[sea_orm(column_type = "BigInteger", nullable)]
    pub read_count: Option<i64>,

    #[sea_orm(column_type = "BigInteger", nullable)]
    pub mention_count: Option<i64>,

    // 新增字段
    #[sea_orm(default_value = "0")]
    pub crawl_status: i32,

    #[sea_orm(nullable)]
    pub last_crawl_time: Option<DateTimeWithTimeZone>,

    #[sea_orm(nullable)]
    pub next_crawl_time: Option<DateTimeWithTimeZone>,

    #[sea_orm(default_value = "0")]
    pub crawl_count: i32,

    #[sea_orm(default_value = "0")]
    pub error_count: i32,

    #[sea_orm(default_value = "false")]
    pub is_trending: bool,

    #[sea_orm(nullable)]
    pub sentiment_score: Option<f32>,

    #[sea_orm(default_value = "0")]
    pub article_count: i32,

    #[sea_orm(default_value = "0")]
    pub comment_count: i32,

    #[sea_orm(column_type = "Array(Text)", nullable)]
    pub hash_tags: Option<Vec<String>>,

    // 原有时间字段
    pub create_date: DateTimeWithTimeZone,
    pub update_date: DateTimeWithTimeZone,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::article::Entity")]
    Articles,
    #[sea_orm(has_many = "super::comment::Entity")]
    Comments,
    #[sea_orm(
        belongs_to = "super::event::Entity",
        from = "Column::Eid",
        to = "super::event::Column::Id"
    )]
    Event,
}

impl Related<super::article::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Articles.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// 爬取状态枚举
#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum CrawlStatus {
    #[sea_orm(num_value = 0)]
    Pending,    // 待爬取
    #[sea_orm(num_value = 1)]
    Processing, // 爬取中
    #[sea_orm(num_value = 2)]
    Completed,  // 已完成
    #[sea_orm(num_value = -1)]
    Failed,     // 失败
}
```

### 爬虫任务实体 (CrawlTask)
```rust
// src/entities/crawl_task.rs
use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "spider_crawl_tasks")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,

    #[sea_orm(column_type = "String(Some(50))")]
    pub task_type: String,

    #[sea_orm(column_type = "String(Some(255))")]
    pub task_name: String,

    #[sea_orm(column_type = "String(Some(50))")]
    pub platform: String,

    #[sea_orm(nullable)]
    pub target_id: Option<i32>,

    #[sea_orm(column_type = "Text", nullable)]
    pub target_url: Option<String>,

    #[sea_orm(default_value = "5")]
    pub priority: i32,

    #[sea_orm(default_value = "0")]
    pub status: i32,

    #[sea_orm(default_value = "0")]
    pub retry_count: i32,

    #[sea_orm(default_value = "3")]
    pub max_retries: i32,

    pub scheduled_time: DateTimeWithTimeZone,

    #[sea_orm(nullable)]
    pub started_time: Option<DateTimeWithTimeZone>,

    #[sea_orm(nullable)]
    pub completed_time: Option<DateTimeWithTimeZone>,

    #[sea_orm(column_type = "Text", nullable)]
    pub error_message: Option<String>,

    #[sea_orm(column_type = "Json", nullable)]
    pub result_data: Option<Json>,

    #[sea_orm(column_type = "Json", nullable)]
    pub config_data: Option<Json>,

    pub created_date: DateTimeWithTimeZone,
    pub updated_date: DateTimeWithTimeZone,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::crawl_log::Entity")]
    Logs,
}

impl Related<super::crawl_log::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Logs.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// 任务类型枚举
#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum)]
#[sea_orm(rs_type = "String", db_type = "String(Some(50))")]
pub enum TaskType {
    #[sea_orm(string_value = "search_topics")]
    SearchTopics,
    #[sea_orm(string_value = "crawl_topic")]
    CrawlTopic,
    #[sea_orm(string_value = "crawl_articles")]
    CrawlArticles,
    #[sea_orm(string_value = "crawl_comments")]
    CrawlComments,
    #[sea_orm(string_value = "nlp_analysis")]
    NlpAnalysis,
}

// 任务状态枚举
#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum TaskStatus {
    #[sea_orm(num_value = 0)]
    Pending,    // 待处理
    #[sea_orm(num_value = 1)]
    Processing, // 处理中
    #[sea_orm(num_value = 2)]
    Completed,  // 已完成
    #[sea_orm(num_value = -1)]
    Failed,     // 失败
}
```

## 🔧 数据库连接配置

### Rust 数据库配置
```rust
// src/config/database.rs
use sea_orm::{Database, DatabaseConnection, ConnectOptions};
use std::time::Duration;

#[derive(Debug, Clone)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connect_timeout: Duration,
    pub idle_timeout: Duration,
    pub max_lifetime: Duration,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: "postgresql://user:password@localhost:5432/spider_db".to_string(),
            max_connections: 100,
            min_connections: 5,
            connect_timeout: Duration::from_secs(8),
            idle_timeout: Duration::from_secs(600),
            max_lifetime: Duration::from_secs(3600),
        }
    }
}

pub async fn establish_connection(config: &DatabaseConfig) -> Result<DatabaseConnection, sea_orm::DbErr> {
    let mut opt = ConnectOptions::new(&config.url);
    opt.max_connections(config.max_connections)
        .min_connections(config.min_connections)
        .connect_timeout(config.connect_timeout)
        .idle_timeout(config.idle_timeout)
        .max_lifetime(config.max_lifetime)
        .sqlx_logging(true)
        .sqlx_logging_level(log::LevelFilter::Info);

    Database::connect(opt).await
}
```

## 📊 性能监控查询

### 常用监控查询
```sql
-- 1. 表大小监控
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY size_bytes DESC;

-- 2. 索引使用情况
SELECT
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- 3. 慢查询监控
SELECT
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- 4. 连接数监控
SELECT
    state,
    COUNT(*) as connection_count
FROM pg_stat_activity
GROUP BY state;
```

## 🚀 部署建议

### 生产环境配置
```sql
-- PostgreSQL 生产环境配置建议
-- postgresql.conf
shared_buffers = 256MB                    # 25% of RAM
effective_cache_size = 1GB               # 75% of RAM
work_mem = 4MB                           # Per connection
maintenance_work_mem = 64MB              # For maintenance operations
checkpoint_completion_target = 0.9       # Checkpoint tuning
wal_buffers = 16MB                       # WAL buffer size
default_statistics_target = 100          # Statistics target

-- 连接池配置
max_connections = 200                    # Maximum connections
shared_preload_libraries = 'pg_stat_statements'  # Query statistics
```

### 备份策略
```bash
#!/bin/bash
# 数据库备份脚本
BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 全量备份
pg_dump -h localhost -U spider_user -d spider_db \
    --format=custom \
    --compress=9 \
    --file=$BACKUP_DIR/spider_db_full.dump

# 增量备份 (WAL 归档)
pg_basebackup -h localhost -U spider_user \
    --pgdata=$BACKUP_DIR/basebackup \
    --format=tar \
    --compress=9 \
    --checkpoint=fast
```

这个重新设计的数据库结构完全保持了与现有系统的兼容性，同时为新的Rust系统提供了更好的性能和扩展性支持。通过添加新字段而不修改现有结构，确保了平滑的迁移过程。
