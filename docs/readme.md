---
marp: true
theme: 'uncover'
_class: 'invert'
paginate: true
---

# 实习期工作总结

2022-10-24至2023-1-24

杨明明

---
## 技术栈介绍

1. `Angular`,`Vue`,`React`,`小程序`
2. `typescript`,`python`,`mysql`,`redis`,`mongodb`,`es`,`mq`,`pg`

---
## 1. 概述
* 工作及总结
* 意见及建议
* 成果展示
* 展望未来
---
## 2. 实习期间工作内容及回顾总结

---
### 2.1 入职及培训(2天)
<!--[10.24-10.26]-->
* 了解企业文化及发展历史
* 了解团队成员及项目架构
---
### 2.1 IOS推送
<!--[10.26-11.1]-->
> 使用第三方包 ***apn*** ，搭建websocket推送服务器，并将推送进度实时推送到`后台管理页面`，配合`接口调试`，`测试`及`部署`
* `apn`依赖要求node最低为v14.x，项目环境为V8.x，由于版本跨度大，最后脱离主项目独立部署
* 与`运维工程师`沟通，完成项目上线，总周期4天
---
### 2.2 广告统计
<!--【11.2-11.17】-->
* 理解业务逻辑，设计程序逻辑，设计数据库结构，实现前端页面及接口对接
* 带新人开发，讲解业务逻辑及编程规范，页面布局，学习线路等知识点
* 审查代码，发现错误或非标代码，更正后测试部署，周期13天

---
### 2.3 IW智能写作(2天)
<!--[11.17-11.18]-->
* 了解智能写作架构及编码规范文档
* 修复`盘前有料`信源失效问题

--- 
### 2.4 IP归属地/新闻-快讯爬虫/引力号改造
<!--[11.21-12.20]-->
* IP归属地
* 快讯爬虫
* 新闻爬虫
* 引力号改造
* 举报功能
* 专题随机图片

---
#### 2.4.1 事故
* 出现一次故障，使用`国外服务`造成页面接口长时间卡顿，排查后修复
* 爬虫放在`feapi`造成`api`服务器内存爆满宕机，后抽离后解决

---
#### 2.4.2 总结
* `API`接口不应放繁重的计算任务或内存IO任务，防止宕机，稳定性优先，提前沟通生产环境服务器配置，防止意外情况。
* 由于整体改动大，应做好充分调研及老代码分析，然后再做方案设计

---
### 2.5 PC端和移动端官网
<!--[12-21-1.20]-->
* 负责前端及接口对接，根据UI设计图进行作业，1:1还原设计稿，并做了不同尺寸屏幕自适应。
* 开发期间发现`测试环境`数据缺失，并提出问题，后配合`运维`解决测试环境爬虫数据问题。


---
## 3. 工作成果

---
### 3.1 后端编码规范制定
* 每类接口一个文件夹，如：用户`/api/user`
* 每个方法一个文件，驼峰式命名`/api/user/findOne.js` -->对应URL `get api/user/findOne/:uid`
* 规范HTTP方法，如：`GET`获取资源,`PUT`局部更新,`POST`添加,`DELETE`删除
* 规范HTTP状态吗，如：`2xx`成功, `3xx`重定向, `4xx`资源不存在或无权限,`5xx`服务器错误
* 避免使用全局变量，方法`小写开头驼峰`，类名`大写开头驼峰`，常量`全部大写`
---
### 3.1 统一配置文件读取
* @fehead/config
```ts
import config from '@fehead/config'

// 是否存在key
config.has(`key`)
// 获取key,默认为string
config.get(`key`,defaultValue)
config.string(`key`,defaultValue)
config.int(`key`, defaultValue)
config.float(`key`,defaultValue)
config.bool(`key`,defaultValue)
config.array(`key`,defaultValue)
config.object(`key`,defaultValue)
config.date(`key`,defaultValue)
```
---
### 3.2 DIY富文本编辑
* @fehead/diy 提供DIY设置功能
* @fehead/preview 实现DIY页面预览功能
* @fehead/watcher 提供设置到预览数据实时生效
---
### 3.3 FASS方案
* @fehead/faas 实现基于NODE的`函数服务`
---
### 3.4 爬虫方案
* 方案一
  * 采用代理方案，可实兼容任意App/网页爬虫
* 方案二
  * scrapy/scrapyd/crapykeeper/elk
  * 数据清洗PaddleNlp
  * 不支持APP爬虫
---
## 4. 意见建议

---
### 4.1 关于UI
* UI设计不应为简单的静态图片堆砌，应考虑不通尺寸屏幕的展示效果，某些位置应标明`定宽`，`定高`,`宽高都定`,`自适应`等，防止页面样式走形或失真
* UI设计稿，字体大小应制定统一规范，字体不应使用特殊字体，如有使用应出具`.font`等字体文件。
* UI设计稿分层及命名要合理，应制定统一颜色，尺寸，间隔，字体，LOGO，二维码等物料库，可加快从UI稿-实现的效率
---
### 3.2 关于沟通
* 团队内部沟通成本略大，应组织或使用有效团队协作工具，将所有环节流程化，降低沟通成本
* 岗位职责应明确，职业规划应清晰

---
### 3.3 关于项目
* 升级一定要及时，当NODE或者使用依赖有大版本变动时，应尽快尝试更新重构，避免版本跨度过大，导致项目无法升级，漏洞无法修复
* API网关缺失，造成无统一出入口，仓库管理混乱，分工不明确，无法沉淀出标准产品（可对外提供服务或软件）

---
### 3.4 标准化产品

* 采集系统-【标准化产品】
* 推荐系统-【标准化产品】
* 单点登录鉴权系统-【标准化产品】
* 网关系统-【标准化产品】
* CMS智能写作系统-【标准化产品】
* DIY智能排版系统-【标准化产品】

---
## 4. 展望未来

---
### 4.1 个人优势分析

* 具备一定的业务梳理能力，理解能力，逻辑能力
* 技术方面，基础知识扎实

---
### 4.2 个人劣势分析

* 沟通能力有待加强，项目及架构还需梳理，行业基础知识还需恶补。
* 对超大数据量，大并发量业务有待加强
* 算法方面属于弱势，应加强

--- 
### 4.3 未来方向
* 积累团队管理项目管理经验
* 积累标准化流程及质量管控经验
* 提升自己的技术实力

---
## 谢谢