import { match } from 'path-to-regexp';
import 'reflect-metadata';

function isType(val) {
    return typeof val === 'function';
}
class InjectionToken {
    desc;
    constructor(desc) {
        this.desc = desc;
    }
}
function isIMetadataMethodOrProperty(val) {
    return val && Reflect.has(val, 'propertyKey') && !isIMetadataParameter(val);
}
function isIMetadataParameter(val) {
    return val && Reflect.has(val, 'parameterIndex');
}

/*
 * @Author: imeepos
 * @Date: 2022-04-12 07:46:41
 * @LastEditors: imeepos
 * @LastEditTime: 2022-04-12 13:36:19
 * @Description:
 * email:<EMAIL> mobile:18639118753 wechat:meepo_brothet realname:yang ming ming
 * Copyright (c) 2022 by imeepos/guanjieinc.com, All Rights Reserved.
 */
class DecoratorStorage {
    _map = new Map();
    forEach(callbackfn, thisArg) {
        this._map.forEach(callbackfn, thisArg);
    }
    toKeys() {
        const result = [];
        this._map.forEach((v, k) => result.push(k));
        return result;
    }
    get(key) {
        if (this._map.has(key)) {
            return this._map.get(key);
        }
        const val = new Set();
        this._map.set(key, val);
        return val;
    }
    setV(key, val) {
        const vals = this.get(key);
        if (vals.has(val)) {
            return this;
        }
        else {
            vals.add(val);
            return this;
        }
    }
    getMetadata(key, metadataKey) {
        const set = this.get(key);
        return [...set].find(it => it.metadataKey === metadataKey);
    }
    getDecorators(key) {
        const decorators = this.get(key);
        const propertiesAndParameters = this.get(key.prototype);
        const children = [];
        const parameters = [...decorators].filter(it => isIMetadataParameter(it));
        const classes = [...decorators].filter(it => !isIMetadataParameter(it));
        propertiesAndParameters.forEach((val) => {
            if (isIMetadataMethodOrProperty(val)) {
                val.parameters = [...propertiesAndParameters].filter(it => {
                    if (isIMetadataParameter(it)) {
                        return it.propertyKey == val.propertyKey;
                    }
                    return false;
                });
                children.push(val);
            }
        });
        return {
            classes: classes,
            properties: children,
            parameters
        };
    }
}
const store = new DecoratorStorage();

/**
 * database decorator
 */
function createClassDecorator(metadataKey) {
    return (options) => {
        return (target) => {
            store.setV(target, { metadataKey, options });
        };
    };
}
function createIdClassDecorator(metadataKey) {
    return (id, options) => {
        return (target) => {
            store.setV(target, { metadataKey, id, options });
        };
    };
}
function createPropertyDecorator(metadataKey) {
    return (options) => {
        return (target, propertyKey) => {
            // insert class_decorator values(key,id)
            store.setV(target, { metadataKey, propertyKey, options });
        };
    };
}
function createMethodDecorator(metadataKey) {
    return (options) => {
        return (target, propertyKey, descriptor) => {
            // insert class_decorator values(key,id)
            store.setV(target, { metadataKey, id: propertyKey, propertyKey, options });
        };
    };
}
function createParameterDecorator(metadataKey) {
    return (options) => {
        return (target, propertyKey, parameterIndex) => {
            // insert class_decorator values(key,id)
            store.setV(target, { metadataKey, propertyKey, parameterIndex, options });
        };
    };
}

/**
 * injectable
 */
const InjectableMetadataKey = `@nger/core injectable metadata key`;
const Injectable = createClassDecorator(InjectableMetadataKey);
/**
 * controller
 */
const ControllerMetadataKey = `@nger/core controller metadata key`;
const Controller = createClassDecorator(ControllerMetadataKey);
/**
 * page
 */
const PageMetadataKey = `@nger/core page metadata key`;
const Page = createClassDecorator(PageMetadataKey);
function isModuleWidthProvider(val) {
    return val && Reflect.has(val, 'module') && Reflect.has(val, 'providers');
}
const ModuleMetadataKey = `@nger/core module metadata key`;
const Module = createClassDecorator(ModuleMetadataKey);
const InjectMetadataKey = `@nger/core inject metadata key`;
const Inject = createParameterDecorator(InjectMetadataKey);
const ParamsMetadataKey = `@nger/core params metadata key`;
const Params = createParameterDecorator(ParamsMetadataKey);
const StateMetadatakey = `@nger/core state metadata key`;
const State = createParameterDecorator(StateMetadatakey);
function createHttpMethodDecorator(key, method) {
    const decorator = createMethodDecorator(key);
    return (path) => decorator({ method, path });
}
const GetMetadataKey = `@nger/http get metadata key`;
const Get = createHttpMethodDecorator(GetMetadataKey, 'get');

function isOnInit(val) {
    return val && Reflect.has(val, 'onInit');
}
function isDoBootstrap(val) {
    return val && Reflect.has(val, 'doBootstrap');
}

function isUseValueProvider(val) {
    return val && Reflect.has(val, 'useValue');
}
function useValue(token, value, multi) {
    return {
        provide: token,
        useValue: value,
        multi
    };
}
function isUseClassProvider(val) {
    return val && Reflect.has(val, 'useClass');
}
function useClass(token, cls, deps = [], multi) {
    return {
        provide: token,
        useClass: cls,
        multi,
        deps
    };
}
function useMultiClass(token, cls, deps = []) {
    return useClass(token, cls, deps, true);
}
function isUseFactoryProvider(val) {
    return val && Reflect.has(val, 'useFactory');
}
function useFactory(token, factory, deps = [], multi) {
    return {
        provide: token,
        useFactory: factory,
        multi,
        deps: deps
    };
}
function useMultiFactory(token, factory, deps = []) {
    return useFactory(token, factory, deps, true);
}
function isTypeProvider(val) {
    return val && typeof val === 'function';
}
function isUseExistProvider(val) {
    return val && Reflect.has(val, 'useExisting');
}

function getDesignType(target) {
    return Reflect.getMetadata("design:type", target);
}
function getDesignParamTypes(target) {
    return Reflect.getMetadata("design:paramtypes", target);
}
function getDesignReturnType(target) {
    return Reflect.getMetadata('design:returntype', target);
}

function isRegExp(val) {
    return isObject(val) && objectToString(val) === '[object RegExp]';
}
function objectToString(o) {
    return Object.prototype.toString.call(o);
}
function isObject(val) {
    return typeof val === 'object' && val !== null;
}
function isDate(d) {
    return isObject(d) && objectToString(d) === '[object Date]';
}
function isPrimitive(arg) {
    return arg === null ||
        typeof arg === 'boolean' ||
        typeof arg === 'number' ||
        typeof arg === 'string' ||
        typeof arg === 'symbol' || // ES6 symbol
        typeof arg === 'undefined';
}
function pad(n) {
    return n < 10 ? '0' + n.toString(10) : n.toString(10);
}
function isString(val) {
    return typeof val === 'string';
}
class Injector {
    name = null;
    static create(providers, parent, name) {
        return new NgerInjector(providers, parent, name);
    }
}
let _current;
let _preInjector;
function setCurrentInjector(injector) {
    _preInjector = _current;
    _current = injector;
}
function getInjector() {
    return _current;
}
function reSetInjector() {
    _current = _preInjector;
}
const ngerInjectorStore = new Map();
const ngerInjectors = [];
class NgerInjector extends Injector {
    map = new Map();
    multiMap = new Map();
    parent;
    removes = [];
    constructor(providers, parent, name) {
        super();
        this.parent = parent;
        this.removes = this.use([...providers, { provide: Injector, useValue: this }]);
        this.name = name;
        ngerInjectorStore.set(name, this);
        ngerInjectors.push(this);
    }
    use(providers, name) {
        const removes = [];
        if (name) {
            if (this.name === name) {
                if (Array.isArray(providers)) {
                    removes.push(...providers.map(provider => this.processProviderAndType(provider)).flat());
                }
                else {
                    removes.push(...this.processProviderAndType(providers));
                }
            }
            if (this.parent) {
                removes.push(...this.parent.use(providers, name));
            }
        }
        else {
            if (Array.isArray(providers)) {
                removes.push(...providers.map(provider => this.processProviderAndType(provider)).flat());
            }
            else {
                removes.push(...this.processProviderAndType(providers));
            }
        }
        return removes;
    }
    create(providers, name) {
        return new NgerInjector(providers, this, name);
    }
    delete(filter) {
        const items = [];
        this.map.forEach((it, key) => {
            if (filter(key, it)) {
                items.push(key);
            }
        });
        items.map((key) => {
            this.map.delete(key);
        });
    }
    deleteMulti(filter) {
        this.multiMap.forEach((it, key) => {
            const removes = [];
            it.forEach(item => {
                if (filter(key, item)) {
                    removes.push(item);
                }
            });
            removes.map(remove => it.delete(remove));
        });
    }
    getMulti(token, to) {
        try {
            setCurrentInjector(this);
            let values = [];
            const set = this.multiMap.get(token);
            if (set) {
                [...set].map((record) => {
                    if (typeof record.value === 'undefined') {
                        record.value = record.factory();
                    }
                    values.push(record.value);
                });
                if (this.name && this.name === to) {
                    return values;
                }
                if (this.parent) {
                    const parentValue = this.parent.getMulti(token, to);
                    if (Array.isArray(parentValue)) {
                        values.push(...parentValue);
                    }
                }
                return values;
            }
            if (this.name && this.name === to) {
                return values;
            }
            if (this.parent) {
                return this.parent.getMulti(token, to);
            }
            return values;
        }
        finally {
            reSetInjector();
        }
    }
    get(token, notfound) {
        if (typeof token === 'undefined' || token === null) {
            debugger;
            return;
        }
        try {
            setCurrentInjector(this);
            const record = this.map.get(token);
            if (record) {
                if (typeof record.value === 'undefined') {
                    record.value = record.factory();
                }
                return record.value;
            }
            if (typeof token === 'string') {
                const keys = [...this.map.keys()];
                let result;
                const _token = keys.find(key => {
                    if (isRegExp(key) || isString(key)) {
                        const _match = match(key, { decode: decodeURIComponent });
                        const _result = _match(token);
                        if (_result) {
                            result = _result;
                            return true;
                        }
                        return false;
                    }
                });
                const record = this.map.get(_token);
                if (record) {
                    return record.factory(result);
                }
            }
            if (this.parent) {
                return this.parent.get(token, notfound);
            }
            return notfound;
        }
        finally {
            reSetInjector();
        }
    }
    processProviderAndType(provider) {
        if (isTypeProvider(provider)) {
            // TODO: type provider
            const toProvider = this.typeToProvider(provider);
            return this.processProvider(toProvider);
        }
        else {
            return this.processProvider(provider);
        }
    }
    typeToProvider(type) {
        return {
            provide: type,
            useClass: type,
            deps: getDesignParamTypes(type)
        };
    }
    processProvider(provider) {
        const removes = [];
        const record = this.providerToRecord(provider);
        if (!!provider.multi) {
            // multi
            if (this.multiMap.has(provider.provide)) {
                const set = this.multiMap.get(provider.provide);
                removes.push(() => set.has(record) && set.delete(record));
                set.add(record);
            }
            else {
                const set = new Set([record]);
                this.multiMap.set(provider.provide, set);
                removes.push(() => this.multiMap.has(record) && this.multiMap.delete(provider.provide));
            }
        }
        else {
            this.map.set(record.token, record);
            removes.push(() => this.map.has(record.token) && this.map.delete(record.token));
        }
        return removes;
    }
    providerToRecord(provider) {
        if (isUseValueProvider(provider)) {
            return this.useValueToRecord(provider);
        }
        else if (isUseClassProvider(provider)) {
            return this.useClassProviderToRecord(provider);
        }
        else if (isUseFactoryProvider(provider)) {
            return this.useFactoryProviderToRecord(provider);
        }
        else if (isUseExistProvider(provider)) {
            return {
                token: provider.provide,
                factory: () => {
                    return this.get(provider.useExisting);
                },
                value: undefined
            };
        }
        else {
            return {
                token: provider.provide,
                factory: () => {
                    return {};
                },
                value: undefined
            };
        }
    }
    useFactoryProviderToRecord(provider) {
        return {
            token: provider.provide,
            factory: (...args) => {
                if (args.length > 0) {
                    return provider.useFactory(...args);
                }
                else {
                    const deps = provider.deps || [];
                    const _args = deps.map(dep => this.get(dep));
                    return provider.useFactory(..._args);
                }
            },
            value: undefined
        };
    }
    useClassProviderToRecord(provider) {
        return {
            token: provider.provide,
            factory: (...args) => {
                if (args.length > 0) {
                    return new provider.useClass(...args);
                }
                else {
                    const deps = provider.deps || [];
                    const _args = deps.map(dep => this.get(dep));
                    return new provider.useClass(..._args);
                }
            },
            value: undefined
        };
    }
    useValueToRecord(provider) {
        return {
            token: provider.provide,
            factory: () => {
                return provider.useValue;
            },
            value: undefined
        };
    }
}

class Middleware {
    name;
    version = '0.0.0';
    status = 2;
    constructor(name) {
        this.name = name;
    }
}
class Plugin extends Middleware {
}
class Decorator extends Middleware {
}
class PlatformInit extends Middleware {
}
class AppInit extends Middleware {
}
class LoadAddon extends Middleware {
}
class AppStart extends Middleware {
}

const NEXT = new InjectionToken(`@nger/core next function`);
const CONTEXT = new InjectionToken(`@nger/core context function`);
const INSTANCE = new InjectionToken(`@nger/core instance`);
const METADATA = new InjectionToken(`@nger/core metadata`);
const METADATAS = new InjectionToken(`@nger/core metadatas`);
const CLASS_METADATAS = new InjectionToken(`@nger/core class metadatas`);
const METADATA_PARAMETERS = new InjectionToken(`@nger/core current type`);
const CURRENT_TYPE = new InjectionToken(`@nger/core current type`);
const MATCH_RESULT = new InjectionToken(`@nger/core match result`);
const DESIGN_TYPE = new InjectionToken(`@nger/core design type`);
const DESIGN_PARAM_TYPES = new InjectionToken(`@nger/core design param types`);
const DESIGN_RETURN_TYPE = new InjectionToken(`@nger/core design return type`);
// cli
const ARGS = new InjectionToken(`@nger/core args`);
const APP_ROOT = new InjectionToken(`@nger/core app root`);
const ADDON_NAME = new InjectionToken(`@nger/core addon name`);
const ADDON_ROOT = new InjectionToken(`@nger/core addon root`);
const PLATFORM_MODULE = new InjectionToken(`@nger/core platform module`);
// cloud url
const CLOUD_URL = new InjectionToken(`@nger/core cloud url`);
const UNINSTALL_HOOK = new InjectionToken(`@nger/core uninstall hook`);
const NG_MODULE = new InjectionToken(`@nger/core ng module`);
const STATE = new InjectionToken(`@nger/core state`);

function compose(middleware) {
    return (injector, next) => {
        let index = -1;
        return dispatch(0);
        function dispatch(i) {
            if (i <= index)
                return Promise.reject(new Error('next() called multiple times'));
            index = i;
            let fn = middleware[i];
            if (i === middleware.length) {
                fn = next;
            }
            if (!fn)
                return Promise.resolve();
            try {
                const next = dispatch.bind(null, i + 1);
                const nextInjector = injector.create([{ provide: NEXT, useValue: next }], `next`);
                return Promise.resolve(fn(nextInjector, dispatch.bind(null, i + 1)));
            }
            catch (err) {
                return Promise.reject(err);
            }
        }
    };
}

class MultiMiddleware extends Middleware {
    middlewares;
    constructor(middlewares) {
        super('@nger/core/MiltiMiddleware');
        this.middlewares = middlewares;
    }
    async handle(injector, next) {
        const middlewares = [...this.middlewares || []].map(mid => {
            if (typeof mid === 'function') {
                return mid;
            }
            else {
                return mid.handle.bind(mid);
            }
        });
        return compose(middlewares)(injector, next);
    }
}

class App {
    plugins = [];
    middlewares = [];
    injector;
    constructor(injector) {
        this.injector = injector || Injector.create([], undefined, 'root');
        this.injector.use({ provide: App, useValue: this }, 'root');
    }
    async bootstrap() {
        /**
         * plugin run
         */
        this.plugins = this.injector.getMulti(Plugin);
        await new MultiMiddleware(this.plugins).handle(this.injector);
        /**
         * platform init run
         */
        const platformInits = this.injector.getMulti(PlatformInit);
        await new MultiMiddleware(platformInits).handle(this.injector);
        /**
         * app init run
         */
        const appInits = this.injector.getMulti(AppInit);
        await new MultiMiddleware(appInits).handle(this.injector);
        /**
         * after platform and app init , load cloud app
         */
        const loadAddons = this.injector.getMulti(LoadAddon);
        await new MultiMiddleware(loadAddons).handle(this.injector);
        const appStarts = this.injector.getMulti(AppStart);
        await new MultiMiddleware(appStarts).handle(this.injector);
        return this.injector;
    }
}

class Watcher {
}

function toError(msg) {
    return { msg, data: null };
}
function toSuccess(data) {
    return { msg: 'ok', data };
}
function toJson(msg, data) {
    return {
        msg, data
    };
}
function toPayload(action, data) {
    return {
        action,
        data
    };
}
function getProvideInName(provideIn) {
    if (provideIn) {
        let obj = provideIn;
        if (Reflect.has(obj, `name`)) {
            return Reflect.get(obj, `name`);
        }
        return provideIn.toString();
    }
    return ``;
}
async function handlerMetadataMethodOrProperty(injector, metadata) {
    const handlers = injector.getMulti(metadata.metadataKey);
    const parameters = (paramsInjector) => {
        return Promise.all((metadata.parameters || [])
            .sort((a, b) => a.parameterIndex - b.parameterIndex)
            .map(parameter => {
            const handlers = injector.getMulti(parameter.metadataKey);
            paramsInjector.use([{ provide: METADATA, useValue: parameter }]);
            return new MultiMiddleware(handlers).handle(paramsInjector);
        }));
    };
    const parent = injector.name;
    const propertyKey = Symbol.for(getProvideInName(parent) + '.' + metadata.propertyKey);
    const currentInjector = injector.create([{ provide: METADATA, useValue: metadata }, { provide: METADATA_PARAMETERS, useValue: parameters }], propertyKey);
    await new MultiMiddleware(handlers).handle(currentInjector);
}
async function getPlatformModule(injector) {
    const platformModules = await Promise.all(injector.getMulti(PLATFORM_MODULE));
    return platformModules.map(module => {
        if (Array.isArray(module)) {
            return module;
        }
        else {
            return [module];
        }
    }).flat();
}
async function processRootModule(type, root) {
    const platformModules = await getPlatformModule(root);
    const injector = await processModule(type, root, 'root', platformModules);
    return injector;
}
async function processModule(type, root, name, modules) {
    const def = getTypeDef(type);
    def.imports = [...modules || [], ...def.imports || []];
    const _providers = await createModuleProviders(def);
    // typeinjector
    const decorators = store.getDecorators(type);
    const { classes, properties, parameters } = decorators;
    const _parameters = (injector) => Promise.all((parameters || []).map(parameter => {
        const handlers = injector.getMulti(parameter.metadataKey);
        const parameterInjector = injector.create([{ provide: METADATA, useValue: parameter }]);
        return new MultiMiddleware(handlers).handle(parameterInjector);
    }));
    const injector = root.create([], name || type);
    const removes = injector.use([
        ..._providers,
        { provide: NG_MODULE, useValue: type },
        {
            provide: INSTANCE,
            useFactory: (injector) => injector.get(type),
            deps: [Injector]
        }, {
            provide: DESIGN_TYPE,
            useFactory: () => getDesignType(type)
        }, {
            provide: DESIGN_PARAM_TYPES,
            useFactory: () => getDesignParamTypes(type) || []
        }, {
            provide: DESIGN_RETURN_TYPE,
            useFactory: () => getDesignReturnType(type)
        }, {
            provide: CLASS_METADATAS,
            useValue: classes
        },
        {
            provide: type,
            useFactory: (injector) => {
                const args = _parameters(injector);
                const paramTypes = injector.get(DESIGN_PARAM_TYPES);
                const _args = paramTypes.map((param, index) => {
                    if (Reflect.has(args, index)) {
                        return Reflect.get(args, index);
                    }
                    return injector.get(param);
                });
                return new type(..._args);
            },
            deps: [Injector]
        }
    ]);
    injector.use([{
            provide: UNINSTALL_HOOK,
            useValue: {
                type,
                removes
            },
            multi: true
        }], 'root');
    // const typeInjector = await handlerType(injector, type);
    const { controllers, imports, providers, id, dev } = def;
    if (!!dev) {
        const watcher = injector.get(Watcher);
        if (!id) {
            throw new Error(`in dev module, module id must be __filename`);
        }
        if (watcher) {
            watcher.watch(id, type);
        }
        else {
            console.log(`dev module is open, but not found watcher`);
        }
    }
    await Promise.all((controllers || []).map(c => handlerType(injector, c)));
    if (imports && imports.length > 0) {
        await Promise.all(imports.map(async (imp) => {
            if (isType(imp)) {
                await processModule(imp, injector);
            }
            else if (isModuleWidthProvider(imp)) {
                const { module, providers } = imp;
                await processModule(module, injector);
            }
            else ;
        }));
    }
    const instance = injector.get(type);
    if (isOnInit(instance)) {
        await instance.onInit(root);
    }
    if (isDoBootstrap(instance)) {
        await instance.doBootstrap(root);
    }
    return injector;
}
async function createModuleProviders(def, injector) {
    let _allProviders = [];
    const { controllers, imports, providers } = def || {};
    if (imports && imports.length > 0) {
        await Promise.all(imports.map(async (imp) => {
            if (isType(imp)) {
                const providers = await createModuleProviders(getTypeDef(imp));
                _allProviders = [
                    ..._allProviders,
                    ...providers
                ];
            }
            else if (isModuleWidthProvider(imp)) {
                const { module, providers } = imp;
                const moduleProviders = await createModuleProviders(getTypeDef(module));
                _allProviders = [
                    ..._allProviders,
                    ...moduleProviders,
                    ...providers
                ];
            }
            else ;
        }));
    }
    _allProviders = [
        ..._allProviders,
        ...providers || []
    ];
    return _allProviders;
}
function getTypeDef(type) {
    if (!type)
        return {};
    const decorators = store.getDecorators(type);
    const { classes, properties, parameters } = decorators;
    const item = classes.find(it => it.metadataKey === ModuleMetadataKey);
    return item && item.options || {};
}
function isNgModuleType(type) {
    const decorators = store.getDecorators(type);
    const { classes, properties, parameters } = decorators;
    const item = classes.find(it => it.metadataKey === ModuleMetadataKey);
    return !!item;
}
async function handlerType(injector, type) {
    const decorators = store.getDecorators(type);
    const { classes, properties, parameters } = decorators;
    const _parameters = (injector) => Promise.all((parameters || []).map(parameter => {
        const handlers = injector.getMulti(parameter.metadataKey);
        const parameterInjector = injector.create([{ provide: METADATA, useValue: parameter }], 'parameters');
        return new MultiMiddleware(handlers).handle(parameterInjector);
    }));
    const typeInjector = injector.create([], type);
    typeInjector.use([{
            provide: INSTANCE,
            useFactory: (injector) => injector.get(type),
            deps: [Injector]
        }, {
            provide: DESIGN_TYPE,
            useFactory: () => getDesignType(type)
        }, {
            provide: DESIGN_PARAM_TYPES,
            useFactory: () => getDesignParamTypes(type) || []
        }, {
            provide: DESIGN_RETURN_TYPE,
            useFactory: () => getDesignReturnType(type)
        }, {
            provide: CLASS_METADATAS,
            useValue: classes
        },
        {
            provide: type,
            useFactory: (injector) => {
                const args = _parameters(injector);
                const paramTypes = injector.get(DESIGN_PARAM_TYPES);
                const _args = paramTypes.map((param, index) => {
                    if (Reflect.has(args, index)) {
                        return Reflect.get(args, index);
                    }
                    return injector.get(param);
                });
                return new type(..._args);
            },
            deps: [Injector]
        }]);
    await Promise.all([...classes].map(async (cls) => {
        const handlers = typeInjector.getMulti(cls.metadataKey);
        if (handlers && handlers.length > 0) {
            const _injector = typeInjector.create([
                { provide: METADATA, useValue: cls },
                { provide: METADATAS, useValue: decorators },
                { provide: CURRENT_TYPE, useValue: type },
            ]);
            await new MultiMiddleware(handlers).handle(_injector);
        }
    }));
    await Promise.all(properties.map(p => handlerMetadataMethodOrProperty(typeInjector, p)));
    return typeInjector;
}

class ErrorHandler {
    handle(err) {
        console.log(err.message);
    }
}

class InjectDecorator extends Decorator {
    constructor() {
        super(ParamsMetadataKey);
    }
    handle(injector) {
        const metadata = injector.get(METADATA);
        if (metadata.options) {
            return injector.get(metadata.options);
        }
        return injector;
    }
}
class ParamsDecorator extends Decorator {
    constructor() {
        super(ParamsMetadataKey);
    }
    handle(injector, next) {
        const result = injector.get(MATCH_RESULT);
        const metadata = injector.get(METADATA);
        if (metadata.options) {
            return Reflect.get(result.params, metadata.options);
        }
        return { ...result.params };
    }
}
const paramDecoratorProviders = [{
        provide: ParamsMetadataKey,
        useFactory: () => new ParamsDecorator(),
        multi: true
    }, {
        provide: InjectMetadataKey,
        useFactory: () => new InjectDecorator(),
        multi: true
    }];

class PlatformRef {
    injector;
    type;
    constructor(injector) {
        this.injector = injector;
    }
    async bootstrap(type) {
        this.type = type;
        return this.reload();
    }
    async reload() {
        const appInjector = await processRootModule(this.type, this.injector);
        const app = new App(appInjector);
        await app.bootstrap();
        return appInjector;
    }
}
let _platform;
function createPlatform(injector) {
    _platform = injector.get(PlatformRef);
    return _platform;
}
function getPlatform() {
    return _platform ? _platform : null;
}
function createPlatformFactory(parentPlatformFactory, name, providers = []) {
    const desc = `Platform: ${name}`;
    const marker = new InjectionToken(desc);
    return (extraProviders = []) => {
        let platform = getPlatform();
        if (!platform) {
            if (parentPlatformFactory) {
                parentPlatformFactory(providers.concat(extraProviders).concat({ provide: marker, useValue: true }));
            }
            else {
                const injectedProviders = providers.concat(extraProviders).concat({ provide: marker, useValue: true });
                createPlatform(Injector.create(injectedProviders, undefined, 'platform'));
            }
        }
        return assertPlatform(marker);
    };
}
function assertPlatform(requiredToken) {
    const platform = getPlatform();
    if (!platform) {
        const errorMessage = 'No platform exists!';
        throw new Error(errorMessage);
    }
    if (!platform.injector.get(requiredToken, null)) {
        throw new Error('A platform with a different configuration has been created. Please destroy it first.');
    }
    return platform;
}
const platformCore = createPlatformFactory(null, 'core', [
    {
        provide: ErrorHandler,
        useFactory: () => new ErrorHandler()
    },
    {
        provide: PlatformRef,
        useFactory: (injector) => new PlatformRef(injector),
        deps: [Injector]
    },
    ...paramDecoratorProviders
]);

function defer() {
    let resolve;
    let reject;
    const promise = new Promise((_resolve, _reject) => {
        resolve = _resolve;
        reject = _reject;
    });
    promise.resolve = resolve;
    promise.reject = reject;
    return promise;
}
async function* observableToAsyncGenerator(observable) {
    let nextData = defer();
    const sub = observable.subscribe({
        next(data) {
            const n = nextData;
            nextData = defer();
            n.resolve(data);
        },
        error(err) {
            nextData.reject(err);
        },
        complete() {
            const n = nextData;
            nextData = null;
            n.resolve();
        }
    });
    try {
        for (;;) {
            const value = await nextData;
            if (!nextData)
                break;
            yield value;
        }
    }
    finally {
        sub.unsubscribe();
    }
}

class Log {
}

class Config {
}

class CoreMethodDecorator extends Decorator {
    method;
    constructor(method) {
        super(`method decorator`);
        this.method = method;
    }
    toUrl(path = '') {
        return path.split('/').filter(it => !!it).join('/');
    }
    async handle(injector, next) {
        const parent = injector.get(METADATA);
        const options = parent.options;
        const classMetadatas = injector.get(CLASS_METADATAS);
        const controllerMetadata = classMetadatas.find((it) => it.metadataKey === ControllerMetadataKey);
        let path = options.path;
        if (controllerMetadata) {
            const url = `${this.toUrl(controllerMetadata.options || '')}/${this.toUrl(path || parent.propertyKey)}`;
            path = `/${this.toUrl(url)}`;
        }
        const provide = `${this.method}${path}`;
        const parameterFactory = injector.get(METADATA_PARAMETERS);
        const log = injector.get(Log);
        log.info(`regist method decorator`, provide);
        const removes = injector.use([{
                provide: provide,
                useFactory: (result) => {
                    const instance = injector.get(INSTANCE);
                    const method = Reflect.get(instance, parent.propertyKey);
                    const handler = async (ctx, next) => {
                        const _injector = injector.create([
                            { provide: MATCH_RESULT, useValue: result },
                            { provide: INSTANCE, useValue: instance },
                            { provide: CONTEXT, useValue: ctx },
                            { provide: NEXT, useValue: next }
                        ], 'handler');
                        const parameters = await parameterFactory(_injector);
                        return method.bind(instance)(...parameters);
                    };
                    return handler;
                }
            }], 'root');
        const currentModule = injector.get(NG_MODULE);
        injector.use([{
                provide: UNINSTALL_HOOK,
                useValue: { removes, type: currentModule },
                multi: true
            }], 'root');
        if (next)
            await next();
    }
}

export { ADDON_NAME, ADDON_ROOT, APP_ROOT, ARGS, App, AppInit, AppStart, CLASS_METADATAS, CLOUD_URL, CONTEXT, CURRENT_TYPE, Config, Controller, ControllerMetadataKey, CoreMethodDecorator, DESIGN_PARAM_TYPES, DESIGN_RETURN_TYPE, DESIGN_TYPE, Decorator, ErrorHandler, Get, GetMetadataKey, INSTANCE, Inject, InjectMetadataKey, Injectable, InjectableMetadataKey, InjectionToken, Injector, LoadAddon, Log, MATCH_RESULT, METADATA, METADATAS, METADATA_PARAMETERS, Middleware, Module, ModuleMetadataKey, MultiMiddleware, NEXT, NG_MODULE, NgerInjector, PLATFORM_MODULE, Page, PageMetadataKey, Params, ParamsMetadataKey, PlatformInit, PlatformRef, Plugin, STATE, State, StateMetadatakey, UNINSTALL_HOOK, Watcher, assertPlatform, createClassDecorator, createHttpMethodDecorator, createIdClassDecorator, createMethodDecorator, createModuleProviders, createParameterDecorator, createPlatform, createPlatformFactory, createPropertyDecorator, defer, getInjector, getPlatform, getPlatformModule, getTypeDef, handlerMetadataMethodOrProperty, handlerType, isDate, isDoBootstrap, isIMetadataMethodOrProperty, isIMetadataParameter, isModuleWidthProvider, isNgModuleType, isObject, isOnInit, isPrimitive, isRegExp, isString, isType, isTypeProvider, isUseClassProvider, isUseExistProvider, isUseFactoryProvider, isUseValueProvider, ngerInjectorStore, ngerInjectors, objectToString, observableToAsyncGenerator, pad, platformCore, processModule, processRootModule, store, toError, toJson, toPayload, toSuccess, useClass, useFactory, useMultiClass, useMultiFactory, useValue };
//# sourceMappingURL=core.es.js.map
