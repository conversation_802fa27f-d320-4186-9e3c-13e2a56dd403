{"version": 3, "file": "core.js", "sources": ["../src/types.ts", "../src/decorator/store.ts", "../src/decorator/util.ts", "../src/decorator/decorators.ts", "../src/decorator/life-cycle.ts", "../src/provider.ts", "../src/metadata.ts", "../src/injector.ts", "../src/middleware.ts", "../src/token.ts", "../src/compose.ts", "../src/multi-middleware.ts", "../src/app.ts", "../src/watcher.ts", "../src/util.ts", "../src/error-handler.ts", "../src/param-decorator.ts", "../src/platform.ts", "../src/defer.ts", "../src/log.ts", "../src/config.ts", "../src/method-decorator.ts"], "sourcesContent": ["\n\nexport type StringToken<T> = string & { type?: T };\nexport type NumberToken<T> = number & { type?: T };\nexport type SymbolToken<T> = symbol & { type?: T };\nexport type BooleanToken<T> = boolean & { type?: T };\nexport type DateToken<T> = Date & { type?: T };\nexport type ObjectToken<T> = object & { type?: T };\nexport type RegExpToken<T> = RegExp & { type?: T };\n\nexport interface Type<T> extends Function {\n    new(...args: any[]): T;\n}\nexport function isType<T>(val: any): val is Type<T> {\n    return typeof val === 'function'\n}\nexport interface AbstractType<T> extends Function {\n    prototype: T;\n}\n\nexport class InjectionToken<T>{\n    constructor(private readonly desc: string) { }\n}\n\nexport type Token<T> =\n    StringToken<T>\n    | NumberToken<T>\n    | SymbolToken<T>\n    | BooleanToken<T>\n    | DateToken<T>\n    | ObjectToken<T>\n    | InjectionToken<T>\n    | AbstractType<T>\n    | Type<T>\n    | RegExpToken<T>;\n\n\nexport interface IMetadataClass<O> {\n    id: string;\n    metadataKey: string;\n    options: O;\n}\nexport interface IMetadataMethodOrProperty<O> {\n    id: string;\n    metadataKey: string;\n    propertyKey: string;\n    options: O;\n    parameters: IMetadataParameter<any>[];\n}\nexport function isIMetadataMethodOrProperty<O>(val: any): val is IMetadataMethodOrProperty<O> {\n    return val && Reflect.has(val, 'propertyKey') && !isIMetadataParameter(val)\n}\nexport interface IMetadataParameter<O> {\n    id: string;\n    metadataKey: string;\n    propertyKey: string;\n    parameterIndex: number;\n    options: O;\n}\nexport function isIMetadataParameter<O>(val: any): val is IMetadataParameter<O> {\n    return val && Reflect.has(val, 'parameterIndex')\n}\nexport type IMetadata<O> = IMetadataClass<O> | IMetadataMethodOrProperty<O> | IMetadataParameter<O>;", "/*\n * @Author: imeepos\n * @Date: 2022-04-12 07:46:41\n * @LastEditors: imeepos\n * @LastEditTime: 2022-04-12 13:36:19\n * @Description: \n * email:<EMAIL> mobile:18639118753 wechat:meepo_brothet realname:yang ming ming\n * Copyright (c) 2022 by imeepos/guanjieinc.com, All Rights Reserved. \n */\n\nimport { IMetadataClass, IMetadataMethodOrProperty, IMetadataParameter, isIMetadataMethodOrProperty, isIMetadataParameter, Type } from \"../types\";\n\nclass DecoratorStorage<K, V extends { metadataKey: string, id: string, options?: any }> {\n    private _map: Map<K, Set<V>> = new Map();\n    forEach(callbackfn: (value: Set<V>, key: K, map: Map<K, Set<V>>) => void, thisArg?: any) {\n        this._map.forEach(callbackfn, thisArg)\n    }\n    toKeys(): K[] {\n        const result: K[] = [];\n        this._map.forEach((v, k) => result.push(k));\n        return result;\n    }\n    get(key: K): Set<V> {\n        if (this._map.has(key)) {\n            return this._map.get(key)!\n        }\n        const val = new Set<V>()\n        this._map.set(key, val);\n        return val\n    }\n    setV(key: K, val: V) {\n        const vals = this.get(key);\n        if (vals.has(val)) {\n            return this;\n        } else {\n            vals.add(val);\n            return this;\n        }\n    }\n\n    getMetadata<T extends V>(key: K, metadataKey: string): T {\n        const set = this.get(key)\n        return [...set].find(it => it.metadataKey === metadataKey) as T;\n    }\n\n    getDecorators(key: Type<any>): {\n        classes: IMetadataClass<any>[],\n        properties: IMetadataMethodOrProperty<any>[],\n        parameters: IMetadataParameter<any>[]\n    } {\n        const decorators = this.get(key as any);\n        const propertiesAndParameters = this.get(key.prototype);\n        const children: IMetadataMethodOrProperty<any>[] = [];\n        const parameters: IMetadataParameter<any>[] = [...decorators].filter(it => isIMetadataParameter(it)) as any[];\n        const classes: IMetadataClass<any>[] = [...decorators].filter(it => !isIMetadataParameter(it)) as any[];\n        propertiesAndParameters.forEach((val) => {\n            if (isIMetadataMethodOrProperty(val)) {\n                val.parameters = [...propertiesAndParameters].filter(it => {\n                    if (isIMetadataParameter(it)) {\n                        return it.propertyKey == val.propertyKey\n                    }\n                    return false;\n                }) as any[];\n                children.push(val)\n            }\n        })\n        return {\n            classes: classes,\n            properties: children,\n            parameters\n        }\n    }\n}\n\nexport const store = new DecoratorStorage();", "import { store } from './store';\n/**\n * database decorator\n */\nexport function createClassDecorator<T>(metadataKey: string) {\n    return (options?: T) => {\n        return (target: any) => {\n            store.setV(target, { metadataKey, options } as any)\n        }\n    }\n}\n\nexport function createIdClassDecorator<T>(metadataKey: string) {\n    return (id: string, options?: T) => {\n        return (target: any) => {\n            store.setV(target, { metadataKey, id, options } as any)\n        }\n    }\n}\n\nexport function createPropertyDecorator<T>(metadataKey: string) {\n    return (options?: T): PropertyDecorator => {\n        return (target: any, propertyKey: any) => {\n            // insert class_decorator values(key,id)\n            store.setV(target, { metadataKey, propertyKey, options } as any)\n        }\n    }\n}\nexport function createMethodDecorator<T>(metadataKey: string) {\n    return (options?: T): MethodDecorator => {\n        return (target: any, propertyKey: any, descriptor: any) => {\n            // insert class_decorator values(key,id)\n            store.setV(target, { metadataKey, id: propertyKey, propertyKey, options } as any)\n        }\n    }\n}\nexport function createParameterDecorator<T>(metadataKey: string) {\n    return (options?: T): ParameterDecorator => {\n        return (target: any, propertyKey: any, parameterIndex: number) => {\n            // insert class_decorator values(key,id)\n            store.setV(target, { metadataKey, propertyKey, parameterIndex, options } as any)\n        }\n    }\n}\n\n", "import { Decorator } from \"../middleware\";\nimport { StaticProvider } from \"../provider\";\nimport { StringToken, Type, Token } from \"../types\";\nimport { createClassDecorator, createMethodDecorator, createParameterDecorator } from \"./util\";\n/**\n * injectable\n */\nexport const InjectableMetadataKey: StringToken<Decorator> = `@nger/core injectable metadata key`;\nexport interface InjectableDef {\n    providedIn?: Type<any> | 'root' | 'platform' | 'any' | null;\n}\nexport const Injectable = createClassDecorator<InjectableDef>(InjectableMetadataKey);\n/**\n * controller\n */\nexport const ControllerMetadataKey: StringToken<Decorator> = `@nger/core controller metadata key`;\nexport const Controller = createClassDecorator<string>(ControllerMetadataKey);\n/**\n * page\n */\nexport const PageMetadataKey: StringToken<Decorator> = `@nger/core page metadata key`;\nexport interface PageDef {\n    path: string;\n    templateUrl: string;\n    styleUrls?: string[];\n}\nexport const Page = createClassDecorator<PageDef>(PageMetadataKey);\n/**\n * module\n */\nexport interface ModuleWidthProvider<T> {\n    module: Type<T>;\n    providers: StaticProvider<any>[];\n}\nexport interface ModuleStatic {\n    main: string;\n    exportName: string;\n}\nexport type ModuleImport = Type<any> | ModuleWidthProvider<any> | ModuleStatic;\nexport function isModuleWidthProvider<T>(val: any): val is ModuleWidthProvider<T> {\n    return val && Reflect.has(val, 'module') && Reflect.has(val, 'providers')\n}\nexport interface ModuleDef {\n    id?: string;\n    imports?: ModuleImport[];\n    providers?: StaticProvider<any>[];\n    controllers?: Type<any>[];\n    dev?: boolean;\n}\n\nexport const ModuleMetadataKey: StringToken<Decorator> = `@nger/core module metadata key`;\nexport const Module = createClassDecorator<ModuleDef>(ModuleMetadataKey);\n\nexport const InjectMetadataKey: StringToken<Decorator> = `@nger/core inject metadata key`;\nexport const Inject = createParameterDecorator<Token<any>>(InjectMetadataKey);\n\nexport const ParamsMetadataKey: StringToken<Decorator> = `@nger/core params metadata key`;\nexport const Params = createParameterDecorator<string>(ParamsMetadataKey);\n\nexport const StateMetadatakey: StringToken<Decorator> = `@nger/core state metadata key`\nexport const State = createParameterDecorator<string>(StateMetadatakey)\n\n\nexport function createHttpMethodDecorator(key: string, method: string) {\n    const decorator = createMethodDecorator<{ method: string, path: string }>(key)\n    return (path: string) => decorator({ method, path })\n}\n\nexport const GetMetadataKey: StringToken<Decorator> = `@nger/http get metadata key`;\nexport const Get = createHttpMethodDecorator(GetMetadataKey, 'get');\n", "import { Injector } from \"../injector\";\n\nexport interface OnInstall { \n    onInstall(): Promise<void>;\n}\nexport interface OnUnInstall { }\nexport interface OnUpgrade { }\nexport interface OnWatch { }\n\nexport interface OnInit {\n    onInit(injector: Injector): Promise<void> | void;\n}\nexport function isOnInit(val: any): val is OnInit {\n    return val && Reflect.has(val, 'onInit')\n}\nexport interface OnDestory { }\nexport interface OnBootstrap {\n    doBootstrap(injector: Injector): Promise<void> | void;\n}\n\nexport function isDoBootstrap(val: any): val is OnBootstrap {\n    return val && Reflect.has(val, 'doBootstrap')\n}\nexport interface DoRegister { }\n\n\n", "import { Token, Type } from \"./types\";\n\nexport interface Provider<T> {\n    provide: Token<T>;\n    multi?: boolean;\n}\n\nexport interface UseValueProvider<T> extends Provider<T> {\n    useValue: T;\n}\n\nexport function isUseValueProvider<T>(val: Provider<T>): val is UseValueProvider<T> {\n    return val && Reflect.has(val, 'useValue')\n}\n\nexport function useValue<T>(token: Token<T>, value: T, multi?: boolean): UseValueProvider<T> {\n    return {\n        provide: token,\n        useValue: value,\n        multi\n    }\n}\n\nexport interface UseClassProvider<T> extends Provider<T> {\n    useClass: Type<T>;\n    deps?: unknown[];\n}\n\nexport function isUseClassProvider<T>(val: Provider<T>): val is UseClassProvider<T> {\n    return val && Reflect.has(val, 'useClass')\n}\n\nexport function useClass<T>(token: Token<T>, cls: Type<T>, deps: any[] = [], multi?: boolean): UseClassProvider<T> {\n    return {\n        provide: token,\n        useClass: cls,\n        multi,\n        deps\n    }\n}\n\nexport function useMultiClass<T>(token: Token<T>, cls: Type<T>, deps: any[] = []) {\n    return useClass(token, cls, deps, true)\n}\n\nexport interface UseFactoryProvider<T> extends Provider<T> {\n    useFactory: Factory<T>;\n    deps?: unknown[];\n}\n\nexport type Factory<T> = (...args: any[]) => T;\n\nexport function isUseFactoryProvider<T>(val: Provider<T>): val is UseFactoryProvider<T> {\n    return val && Reflect.has(val, 'useFactory')\n}\n\nexport function useFactory<T>(token: Token<T>, factory: Factory<T>, deps: any[] = [], multi?: boolean): UseFactoryProvider<T> {\n    return {\n        provide: token,\n        useFactory: factory,\n        multi,\n        deps: deps\n    }\n}\n\nexport function useMultiFactory<T>(token: Token<T>, factory: Factory<T>, deps: any[] = []) {\n    return useFactory(token, factory, deps, true)\n}\n\nexport interface TypeProvider<T> extends Type<T> { }\nexport function isTypeProvider<T>(val: any): val is TypeProvider<T> {\n    return val && typeof val === 'function'\n}\n\n\nexport interface UseExistProvider<T> extends Provider<T> {\n    useExisting: Token<T>;\n}\nexport function isUseExistProvider<T>(val: any): val is UseExistProvider<T> {\n    return val && Reflect.has(val, 'useExisting')\n}\nexport type StaticProvider<T> = UseExistProvider<T> | UseValueProvider<T> | UseClassProvider<T> | UseFactoryProvider<T> | TypeProvider<T>;\n", "import \"reflect-metadata\";\n\nexport function getDesignType(target: any) {\n    return Reflect.getMetadata(\"design:type\", target)\n}\nexport function getDesignParamTypes(target: any) {\n    return Reflect.getMetadata(\"design:paramtypes\", target)\n}\nexport function getDesignReturnType(target: any) {\n    return Reflect.getMetadata('design:returntype', target)\n}", "import {\n    isTypeProvider,\n    isUseClassProvider, isUseExistProvider, isUseFactoryProvider, isUseValueProvider,\n    Provider, StaticProvider, TypeProvider, UseClassProvider, UseFactoryProvider, UseValueProvider\n} from \"./provider\";\nimport { Token, Type } from \"./types\";\nexport function isRegExp(val: any): val is RegExp {\n    return isObject(val) && objectToString(val) === '[object RegExp]';\n}\nexport function objectToString(o: any): string {\n    return Object.prototype.toString.call(o);\n}\nexport function isObject(val: any): val is object {\n    return typeof val === 'object' && val !== null\n}\nexport function isDate(d: any): d is Date {\n    return isObject(d) && objectToString(d) === '[object Date]';\n}\nexport function isPrimitive(arg: any) {\n    return arg === null ||\n        typeof arg === 'boolean' ||\n        typeof arg === 'number' ||\n        typeof arg === 'string' ||\n        typeof arg === 'symbol' ||  // ES6 symbol\n        typeof arg === 'undefined';\n}\nexport function pad(n: number): string {\n    return n < 10 ? '0' + n.toString(10) : n.toString(10);\n}\nexport function isString(val: any): val is string {\n    return typeof val === 'string'\n}\nexport type ProvideIn = Type<any> | 'root' | 'platform' | 'any' | string | symbol | null | undefined;\nexport abstract class Injector {\n    name: ProvideIn = null;\n    abstract delete(filter: (token: Token<any>, record: Record<any>) => boolean): void;\n    abstract deleteMulti(filter: (token: Token<any>, record: Record<any>) => boolean): void;\n    abstract get<T>(token: Token<T>, notfound?: unknown): T;\n    abstract getMulti<T>(token: Token<T>, to?: ProvideIn): T[];\n    abstract use(providers: StaticProvider<any>[] | StaticProvider<any>, name?: ProvideIn): Function[];\n    abstract create(providers: StaticProvider<any>[], name?: ProvideIn): Injector;\n    static create(providers: StaticProvider<any>[], parent?: Injector, name?: ProvideIn) {\n        return new NgerInjector(providers, parent, name);\n    }\n}\n\nexport interface Record<T> {\n    token: Token<T>;\n    factory: (...args: any[]) => T;\n    value: T | undefined;\n}\nimport { match, MatchResult } from 'path-to-regexp'\nimport { getDesignParamTypes } from \"./metadata\";\nlet _current: Injector | undefined;\nlet _preInjector: Injector | undefined;\nfunction setCurrentInjector(injector: Injector) {\n    _preInjector = _current;\n    _current = injector;\n}\nexport function getInjector() {\n    return _current!;\n}\nfunction reSetInjector() {\n    _current = _preInjector;\n}\nexport const ngerInjectorStore: Map<ProvideIn, Injector> = new Map();\nexport const ngerInjectors: Injector[] = [];\nexport class NgerInjector extends Injector {\n    private map: Map<Token<any>, Record<any>> = new Map();\n    private multiMap: Map<Token<any>, Set<Record<any>>> = new Map();\n    parent: Injector | undefined;\n    removes: Function[] = [];\n    constructor(\n        providers: StaticProvider<any>[],\n        parent?: Injector,\n        name?: ProvideIn\n    ) {\n        super();\n        this.parent = parent;\n        this.removes = this.use([...providers, { provide: Injector, useValue: this }]);\n        this.name = name;\n        ngerInjectorStore.set(name, this);\n        ngerInjectors.push(this);\n    }\n    use(\n        providers: StaticProvider<any>[] | StaticProvider<any>,\n        name?: ProvideIn\n    ): Function[] {\n        const removes: Function[] = [];\n        if (name) {\n            if (this.name === name) {\n                if (Array.isArray(providers)) {\n                    removes.push(...providers.map(provider => this.processProviderAndType(provider)).flat())\n                } else {\n                    removes.push(...this.processProviderAndType(providers))\n                }\n            }\n            if (this.parent) {\n                removes.push(...this.parent.use(providers, name))\n            }\n        } else {\n            if (Array.isArray(providers)) {\n                removes.push(...providers.map(provider => this.processProviderAndType(provider)).flat())\n            } else {\n                removes.push(...this.processProviderAndType(providers))\n            }\n        }\n        return removes;\n    }\n    create(providers: StaticProvider<any>[], name?: ProvideIn): Injector {\n        return new NgerInjector(providers, this, name)\n    }\n    delete(filter: (token: Token<any>, record: Record<any>) => boolean) {\n        const items: Token<any>[] = [];\n        this.map.forEach((it, key) => {\n            if (filter(key, it)) {\n                items.push(key)\n            }\n        })\n        items.map((key) => {\n            this.map.delete(key)\n        })\n    }\n    deleteMulti(filter: (token: Token<any>, record: Record<any>) => boolean) {\n        this.multiMap.forEach((it, key) => {\n            const removes: Record<any>[] = [];\n            it.forEach(item => {\n                if (filter(key, item)) {\n                    removes.push(item)\n                }\n            });\n            removes.map(remove => it.delete(remove))\n        })\n    }\n    getMulti<T>(token: Token<T>, to?: ProvideIn): T[] {\n        try {\n            setCurrentInjector(this);\n            let values: T[] = [];\n            const set = this.multiMap.get(token)\n            if (set) {\n                [...set].map((record: Record<T>) => {\n                    if (typeof record.value === 'undefined') {\n                        record.value = record.factory();\n                    }\n                    values.push(record.value)\n                });\n                if (this.name && this.name === to) {\n                    return values;\n                }\n                if (this.parent) {\n                    const parentValue = this.parent.getMulti(token, to);\n                    if (Array.isArray(parentValue)) {\n                        values.push(...parentValue);\n                    }\n                }\n                return values;\n            }\n            if (this.name && this.name === to) {\n                return values;\n            }\n            if (this.parent) {\n                return this.parent.getMulti(token, to)\n            }\n            return values;\n        } finally {\n            reSetInjector()\n        }\n    }\n    get<T>(token: Token<T>, notfound?: unknown): T {\n        if (typeof token === 'undefined' || token === null) {\n            debugger;\n            return;\n        }\n        try {\n            setCurrentInjector(this);\n            const record = this.map.get(token);\n            if (record) {\n                if (typeof record.value === 'undefined') {\n                    record.value = record.factory();\n                }\n                return record.value;\n            }\n            if (typeof token === 'string') {\n                const keys = [...this.map.keys()];\n                let result: MatchResult<any> | undefined;\n                const _token = keys.find(key => {\n                    if (isRegExp(key) || isString(key)) {\n                        const _match = match(key, { decode: decodeURIComponent });\n                        const _result = _match(token);\n                        if (_result) {\n                            result = _result;\n                            return true;\n                        }\n                        return false;\n                    }\n                })!;\n                const record = this.map.get(_token)\n                if (record) {\n                    return record.factory(result)\n                }\n            }\n            if (this.parent) {\n                return this.parent.get(token, notfound)\n            }\n            return notfound as T;\n        } finally {\n            reSetInjector()\n        }\n\n    }\n    private processProviderAndType<T>(provider: Provider<T> | TypeProvider<T>): Function[] {\n        if (isTypeProvider(provider)) {\n            // TODO: type provider\n            const toProvider = this.typeToProvider(provider)\n            return this.processProvider(toProvider)\n        } else {\n            return this.processProvider(provider)\n        }\n    }\n    private typeToProvider<T>(type: TypeProvider<T>): UseClassProvider<T> {\n        return {\n            provide: type,\n            useClass: type,\n            deps: getDesignParamTypes(type)\n        }\n    }\n    private processProvider<T>(provider: Provider<T>): Function[] {\n        const removes: Function[] = [];\n        const record = this.providerToRecord(provider);\n        if (!!provider.multi) {\n            // multi\n            if (this.multiMap.has(provider.provide)) {\n                const set = this.multiMap.get(provider.provide)!;\n                removes.push(() => set.has(record) && set.delete(record))\n                set.add(record)\n            } else {\n                const set = new Set([record])\n                this.multiMap.set(provider.provide, set)\n                removes.push(() => this.multiMap.has(record) && this.multiMap.delete(provider.provide))\n            }\n        } else {\n            this.map.set(record.token, record);\n            removes.push(() => this.map.has(record.token) && this.map.delete(record.token))\n        }\n        return removes;\n    }\n    private providerToRecord<T>(provider: Provider<T>): Record<T> {\n        if (isUseValueProvider(provider)) {\n            return this.useValueToRecord(provider);\n        }\n        else if (isUseClassProvider(provider)) {\n            return this.useClassProviderToRecord(provider);\n        }\n        else if (isUseFactoryProvider(provider)) {\n            return this.useFactoryProviderToRecord(provider);\n        } else if (isUseExistProvider(provider)) {\n            return {\n                token: provider.provide,\n                factory: () => {\n                    return this.get<any>(provider.useExisting);\n                },\n                value: undefined\n            }\n        } else {\n            return {\n                token: provider.provide,\n                factory: () => {\n                    return {} as T;\n                },\n                value: undefined\n            }\n        }\n    }\n    private useFactoryProviderToRecord<T>(provider: UseFactoryProvider<T>) {\n        return {\n            token: provider.provide,\n            factory: (...args: any[]) => {\n                if (args.length > 0) {\n                    return provider.useFactory(...args);\n                } else {\n                    const deps: any[] = provider.deps || [];\n                    const _args = deps.map(dep => this.get(dep))\n                    return provider.useFactory(..._args);\n                }\n            },\n            value: undefined\n        }\n    }\n    private useClassProviderToRecord<T>(provider: UseClassProvider<T>) {\n        return {\n            token: provider.provide,\n            factory: (...args: any[]) => {\n                if (args.length > 0) {\n                    return new provider.useClass(...args);\n                } else {\n                    const deps: any[] = provider.deps || [];\n                    const _args = deps.map(dep => this.get(dep))\n                    return new provider.useClass(..._args);\n                }\n            },\n            value: undefined\n        }\n    }\n    private useValueToRecord<T>(provider: UseValueProvider<T>) {\n        return {\n            token: provider.provide,\n            factory: () => {\n                return provider.useValue;\n            },\n            value: undefined\n        }\n    }\n}\n", "import { Injector } from \"./injector\";\nimport { Next } from \"./token\";\nexport abstract class Middleware {\n    name: string;\n    version: string = '0.0.0';\n    status: number = 2;\n    constructor(name: string) {\n        this.name = name;\n    }\n    abstract handle(injector: Injector, next?: Next): Promise<any> | any;\n}\nexport abstract class Plugin extends Middleware { }\nexport abstract class Decorator extends Middleware { }\nexport abstract class PlatformInit extends Middleware { }\nexport abstract class AppInit extends Middleware { }\nexport abstract class LoadAddon extends Middleware { }\nexport abstract class AppStart extends Middleware { }\n", "import { IMetadata, IMetadataClass, IMetadataMethodOrProperty, InjectionToken, Type } from \"./types\";\nexport interface Next {\n    (): Promise<void> | void;\n}\nexport const NEXT = new InjectionToken<() => void>(`@nger/core next function`);\nexport const CONTEXT = new InjectionToken<any>(`@nger/core context function`);\n\nexport const INSTANCE = new InjectionToken<any>(`@nger/core instance`);\n\nexport const METADATA = new InjectionToken<IMetadata<any>>(`@nger/core metadata`)\nexport interface Decorators {\n    classes: Set<IMetadataClass<any>>;\n    properties: IMetadataMethodOrProperty<any>[];\n}\nexport const METADATAS = new InjectionToken<Decorators>(`@nger/core metadatas`)\nexport const CLASS_METADATAS = new InjectionToken<IMetadataClass<any>[]>(`@nger/core class metadatas`)\nexport const METADATA_PARAMETERS = new InjectionToken<Function>(`@nger/core current type`)\nexport const CURRENT_TYPE = new InjectionToken<Type<any>>(`@nger/core current type`)\nimport { MatchResult } from 'path-to-regexp';\nexport const MATCH_RESULT = new InjectionToken<MatchResult>(`@nger/core match result`)\nexport const DESIGN_TYPE = new InjectionToken<any>(`@nger/core design type`)\nexport const DESIGN_PARAM_TYPES = new InjectionToken<any[]>(`@nger/core design param types`)\nexport const DESIGN_RETURN_TYPE = new InjectionToken<any>(`@nger/core design return type`)\n// cli\nexport const ARGS = new InjectionToken<any>(`@nger/core args`);\nexport const APP_ROOT = new InjectionToken<string>(`@nger/core app root`);\nexport const ADDON_NAME = new InjectionToken<string>(`@nger/core addon name`);\nexport const ADDON_ROOT = new InjectionToken<string>(`@nger/core addon root`);\n// platform regist module\nexport type PlatformModule = Type<any> | Promise<Type<any> | Type<any>[] | Promise<Type<any>[]>>;\nexport const PLATFORM_MODULE = new InjectionToken<PlatformModule>(`@nger/core platform module`);\n// cloud url\nexport const CLOUD_URL = new InjectionToken<string>(`@nger/core cloud url`);\nexport const UNINSTALL_HOOK = new InjectionToken<{ type: Type<any>, removes: Function[] }>(`@nger/core uninstall hook`)\nexport const NG_MODULE = new InjectionToken<Type<any>>(`@nger/core ng module`)\n\nexport const STATE = new InjectionToken<any>(`@nger/core state`)", "import { Injector } from \"./injector\";\nimport { NEXT, Next } from \"./token\";\n\nexport interface CanCompose {\n    (injector: Injector, next?: Next): void | Promise<void>;\n}\n\n\nexport function composeAsync(middleware: CanCompose[]) {\n    return (injector: Injector, next?: Next) => {\n        let index = -1\n        return dispatch(0);\n        function dispatch(i: number): void {\n            if (i <= index) throw new Error('next() called multiple times')\n            index = i\n            let fn = middleware[i]\n            if (i === middleware.length) fn = next!\n            if (!fn) return;\n            try {\n                const next = dispatch.bind(null, i + 1);\n                const nextInjector = injector.create([{ provide: NEXT, useValue: next }], `next`)\n                return fn(nextInjector, dispatch.bind(null, i + 1)) as void;\n            } catch (err) {\n                throw err;\n            }\n        }\n    }\n}\nexport function compose(middleware: CanCompose[]) {\n    return (injector: Injector, next?: Next) => {\n        let index = -1\n        return dispatch(0);\n        function dispatch(i: number): Promise<void> {\n            if (i <= index) return Promise.reject(new Error('next() called multiple times'))\n            index = i\n            let fn = middleware[i]\n            if (i === middleware.length) {\n                fn = next!\n            }\n            if (!fn) return Promise.resolve()\n            try {\n                const next = dispatch.bind(null, i + 1);\n                const nextInjector = injector.create([{ provide: NEXT, useValue: next }], `next`)\n                return Promise.resolve(fn(nextInjector, dispatch.bind(null, i + 1)))\n            } catch (err) {\n                return Promise.reject(err)\n            }\n        }\n    }\n}", "import { Injector } from \"./injector\";\nimport { Middleware } from \"./middleware\";\nimport { Next } from \"./token\";\nimport { compose } from \"./compose\";\nexport class MultiMiddleware extends Middleware {\n    constructor(private middlewares: Middleware[]) {\n        super('@nger/core/MiltiMiddleware');\n    }\n    async handle(injector: Injector, next?: Next): Promise<void> {\n        const middlewares = [...this.middlewares || []].map(mid => {\n            if (typeof mid === 'function') {\n                return mid;\n            } else {\n                return mid.handle.bind(mid)\n            }\n        });\n        return compose(middlewares)(injector, next)\n    }\n}\n", "import { Injector } from \"./injector\";\nimport { Plugin, Middleware, PlatformInit, AppInit, LoadAddon, AppStart } from \"./middleware\";\nimport { MultiMiddleware } from \"./multi-middleware\";\n\nexport class App {\n    plugins: Plugin[] = [];\n    middlewares: Middleware[] = [];\n    injector: Injector;\n    constructor(injector?: Injector) {\n        this.injector = injector || Injector.create([], undefined, 'root')\n        this.injector.use({ provide: App, useValue: this }, 'root')\n    }\n    async bootstrap(): Promise<Injector> {\n        /**\n         * plugin run\n         */\n        this.plugins = this.injector.getMulti(Plugin);\n        await new MultiMiddleware(this.plugins).handle(this.injector);\n        /**\n         * platform init run\n         */\n        const platformInits = this.injector.getMulti(PlatformInit);\n        await new MultiMiddleware(platformInits).handle(this.injector);\n        /**\n         * app init run\n         */\n        const appInits = this.injector.getMulti(AppInit);\n        await new MultiMiddleware(appInits).handle(this.injector);\n        /**\n         * after platform and app init , load cloud app\n         */\n        const loadAddons = this.injector.getMulti(LoadAddon);\n        await new MultiMiddleware(loadAddons).handle(this.injector)\n\n        const appStarts = this.injector.getMulti(AppStart);\n        await new MultiMiddleware(appStarts).handle(this.injector)\n        return this.injector;\n    }\n}\n", "import { Type } from \"./types\";\n\nexport abstract class Watcher {\n    abstract watch(path: string, type: Type<any>): any;\n}", "import { isD<PERSON><PERSON><PERSON><PERSON>p, isModuleWidthProvider, isOnInit, ModuleDef, ModuleMetadataKey, store } from \"./decorator\";\nimport { Injector, ProvideIn } from \"./injector\";\nimport { getDesignParamTypes, getDesignReturnType, getDesignType } from \"./metadata\";\nimport { Decorator } from \"./middleware\";\nimport { MultiMiddleware } from \"./multi-middleware\";\nimport { StaticProvider } from \"./provider\";\nimport {\n    CURRENT_TYPE, METADATA, METADATAS, CLASS_METADATAS, METADATA_PARAMETERS,\n    INSTANCE, DESIGN_TYPE, DESIGN_PARAM_TYPES, DESIGN_RETURN_TYPE, PLATFORM_MODULE, NG_MODULE, Next, NEXT, UNINSTALL_HOOK\n} from \"./token\";\nimport { IMetadataMethodOrProperty, isType, Type } from \"./types\";\nimport { Watcher } from \"./watcher\";\n\nexport function toError(msg: string) {\n    return { msg, data: null }\n}\nexport function toSuccess<T>(data: T) {\n    return { msg: 'ok', data }\n}\nexport function toJson<T>(msg: string, data: T) {\n    return {\n        msg, data\n    }\n}\nexport interface Payload<T> {\n    action: string;\n    data: T;\n}\nexport function toPayload<T>(action: string, data: T): Payload<T> {\n    return {\n        action,\n        data\n    }\n}\nfunction getProvideInName(provideIn: ProvideIn) {\n    if (provideIn) {\n        let obj: any = provideIn;\n        if (Reflect.has(obj, `name`)) {\n            return Reflect.get(obj, `name`);\n        }\n        return provideIn.toString();\n    }\n    return ``\n}\nexport async function handlerMetadataMethodOrProperty(injector: Injector, metadata: IMetadataMethodOrProperty<any>) {\n    const handlers = injector.getMulti<Decorator>(metadata.metadataKey);\n    const parameters = (paramsInjector: Injector) => {\n        return Promise.all((metadata.parameters || [])\n            .sort((a, b) => a.parameterIndex - b.parameterIndex)\n            .map(parameter => {\n                const handlers = injector.getMulti<Decorator>(parameter.metadataKey)\n                paramsInjector.use([{ provide: METADATA, useValue: parameter }]);\n                return new MultiMiddleware(handlers).handle(paramsInjector);\n            }))\n    }\n    const parent = injector.name;\n    const propertyKey = Symbol.for(getProvideInName(parent) + '.' + metadata.propertyKey);\n    const currentInjector = injector.create([{ provide: METADATA, useValue: metadata }, { provide: METADATA_PARAMETERS, useValue: parameters }], propertyKey)\n    await new MultiMiddleware(handlers).handle(currentInjector)\n}\nexport async function getPlatformModule(injector: Injector): Promise<Type<any>[]> {\n    const platformModules = await Promise.all(injector.getMulti(PLATFORM_MODULE));\n    return platformModules.map(module => {\n        if (Array.isArray(module)) {\n            return module;\n        } else {\n            return [module]\n        }\n    }).flat() as Type<any>[];\n}\nexport async function processRootModule(type: Type<any>, root: Injector) {\n    const platformModules = await getPlatformModule(root);\n    const injector = await processModule(type, root, 'root', platformModules);\n    return injector;\n}\n\nexport async function processModule(type: Type<any>, root: Injector, name?: ProvideIn, modules?: Type<any>[]): Promise<Injector> {\n    const def = getTypeDef(type);\n    def.imports = [...modules || [], ...def.imports || []];\n    const _providers = await createModuleProviders(def, root)\n    // typeinjector\n    const decorators = store.getDecorators(type)\n    const { classes, properties, parameters } = decorators;\n    const _parameters = (injector: Injector) => Promise.all((parameters || []).map(parameter => {\n        const handlers = injector.getMulti<Decorator>(parameter.metadataKey)\n        const parameterInjector = injector.create([{ provide: METADATA, useValue: parameter }]);\n        return new MultiMiddleware(handlers).handle(parameterInjector);\n    }));\n    const injector = root.create([], name || type);\n    const removes = injector.use([\n        ..._providers,\n        { provide: NG_MODULE, useValue: type },\n        {\n            provide: INSTANCE,\n            useFactory: (injector: Injector) => injector.get(type),\n            deps: [Injector]\n        }, {\n            provide: DESIGN_TYPE,\n            useFactory: () => getDesignType(type)\n        }, {\n            provide: DESIGN_PARAM_TYPES,\n            useFactory: () => getDesignParamTypes(type) || []\n        }, {\n            provide: DESIGN_RETURN_TYPE,\n            useFactory: () => getDesignReturnType(type)\n        }, {\n            provide: CLASS_METADATAS,\n            useValue: classes\n        },\n        {\n            provide: type,\n            useFactory: (injector: Injector) => {\n                const args = _parameters(injector);\n                const paramTypes = injector.get(DESIGN_PARAM_TYPES)\n                const _args = paramTypes.map((param, index) => {\n                    if (Reflect.has(args, index)) {\n                        return Reflect.get(args, index)\n                    }\n                    return injector.get(param)\n                })\n                return new type(..._args);\n            },\n            deps: [Injector]\n        }]);\n    injector.use([{\n        provide: UNINSTALL_HOOK,\n        useValue: {\n            type,\n            removes\n        },\n        multi: true\n    }], 'root');\n    // const typeInjector = await handlerType(injector, type);\n    const { controllers, imports, providers, id, dev } = def as ModuleDef;\n    if (!!dev) {\n        const watcher = injector.get(Watcher)\n        if (!id) {\n            throw new Error(`in dev module, module id must be __filename`)\n        }\n        if (watcher) {\n            watcher.watch(id!, type)\n        } else {\n            console.log(`dev module is open, but not found watcher`)\n        }\n    }\n    await Promise.all((controllers || []).map(c => handlerType(injector, c)));\n    if (imports && imports.length > 0) {\n        await Promise.all(imports.map(async imp => {\n            if (isType(imp)) {\n                await processModule(imp, injector);\n            } else if (isModuleWidthProvider(imp)) {\n                const { module, providers } = imp;\n                await processModule(module, injector)\n            } else if (imp) {\n                // const { main, exportName } = imp;\n                // const type = await import(main).then(res => Reflect.get(res, exportName));\n                // await processModule(type, injector);\n            }\n        }))\n    }\n    const instance = injector.get(type);\n    if (isOnInit(instance)) {\n        await instance.onInit(root);\n    }\n    if (isDoBootstrap(instance)) {\n        await instance.doBootstrap(root);\n    }\n    return injector;\n}\n\nexport async function createModuleProviders(def: ModuleDef, injector: Injector): Promise<StaticProvider<any>[]> {\n    let _allProviders: StaticProvider<any>[] = [];\n    const { controllers, imports, providers } = def || {};\n    if (imports && imports.length > 0) {\n        await Promise.all(imports.map(async imp => {\n            if (isType(imp)) {\n                const providers = await createModuleProviders(getTypeDef(imp), injector);\n                _allProviders = [\n                    ..._allProviders,\n                    ...providers\n                ]\n            } else if (isModuleWidthProvider(imp)) {\n                const { module, providers } = imp;\n                const moduleProviders = await createModuleProviders(getTypeDef(module), injector)\n                _allProviders = [\n                    ..._allProviders,\n                    ...moduleProviders,\n                    ...providers\n                ]\n            } else {\n                // const { main, exportName } = imp;\n                // const type = await import(main).then(res => Reflect.get(res, exportName));\n                // const providers = await createModuleProviders(getTypeDef(type), injector);\n                // _allProviders = [\n                //     ..._allProviders,\n                //     ...providers\n                // ]\n            }\n        }))\n    }\n    _allProviders = [\n        ..._allProviders,\n        ...providers || []\n    ]\n    return _allProviders;\n}\n\nexport function getTypeDef(type: Type<any>) {\n    if (!type) return {};\n    const decorators = store.getDecorators(type)\n    const { classes, properties, parameters } = decorators;\n    const item = classes.find(it => it.metadataKey === ModuleMetadataKey);\n    return item && item!.options || {};\n}\n\nexport function isNgModuleType(type: Type<any>) {\n    const decorators = store.getDecorators(type)\n    const { classes, properties, parameters } = decorators;\n    const item = classes.find(it => it.metadataKey === ModuleMetadataKey);\n    return !!item;\n}\n\nexport async function handlerType(injector: Injector, type: Type<any>) {\n    const decorators = store.getDecorators(type)\n    const { classes, properties, parameters } = decorators;\n    const _parameters = (injector: Injector) => Promise.all((parameters || []).map(parameter => {\n        const handlers = injector.getMulti<Decorator>(parameter.metadataKey)\n        const parameterInjector = injector.create([{ provide: METADATA, useValue: parameter }], 'parameters');\n        return new MultiMiddleware(handlers).handle(parameterInjector);\n    }));\n    const typeInjector = injector.create([], type);\n    typeInjector.use([{\n        provide: INSTANCE,\n        useFactory: (injector: Injector) => injector.get(type),\n        deps: [Injector]\n    }, {\n        provide: DESIGN_TYPE,\n        useFactory: () => getDesignType(type)\n    }, {\n        provide: DESIGN_PARAM_TYPES,\n        useFactory: () => getDesignParamTypes(type) || []\n    }, {\n        provide: DESIGN_RETURN_TYPE,\n        useFactory: () => getDesignReturnType(type)\n    }, {\n        provide: CLASS_METADATAS,\n        useValue: classes\n    },\n    {\n        provide: type,\n        useFactory: (injector: Injector) => {\n            const args = _parameters(injector);\n            const paramTypes = injector.get(DESIGN_PARAM_TYPES)\n            const _args = paramTypes.map((param, index) => {\n                if (Reflect.has(args, index)) {\n                    return Reflect.get(args, index)\n                }\n                return injector.get(param)\n            })\n            return new type(..._args);\n        },\n        deps: [Injector]\n    }]);\n    await Promise.all([...classes].map(async (cls) => {\n        const handlers = typeInjector.getMulti<Decorator>(cls.metadataKey);\n        if (handlers && handlers.length > 0) {\n            const _injector = typeInjector.create([\n                { provide: METADATA, useValue: cls },\n                { provide: METADATAS, useValue: decorators },\n                { provide: CURRENT_TYPE, useValue: type },\n            ]);\n            await new MultiMiddleware(handlers).handle(_injector);\n        }\n    }));\n    await Promise.all(properties.map(p => handlerMetadataMethodOrProperty(typeInjector, p)));\n    return typeInjector;\n}\n", "export class ErrorHandler {\n    handle(err: Error) { \n        console.log(err.message)\n    }\n}", "import { MatchR<PERSON>ult } from \"path-to-regexp\";\nimport { InjectMetadata<PERSON>ey, ParamsMetadataKey } from \"./decorator\";\nimport { Injector } from \"./injector\";\nimport { Decorator } from \"./middleware\";\nimport { StaticProvider } from \"./provider\";\nimport {  MATCH_RESULT, METADATA, Next } from \"./token\";\n\nexport class InjectDecorator extends Decorator {\n    constructor() {\n        super(ParamsMetadataKey)\n    }\n    handle(injector: Injector) {\n        const metadata = injector.get(METADATA)\n        if (metadata.options) {\n            return injector.get(metadata.options)\n        }\n        return injector;\n    }\n}\n\nexport class ParamsDecorator extends Decorator {\n    constructor() {\n        super(ParamsMetadataKey)\n    }\n    handle(injector: Injector, next: Next) {\n        const result = injector.get<MatchResult>(MATCH_RESULT)\n        const metadata = injector.get(METADATA)\n        if (metadata.options) {\n            return Reflect.get(result.params, metadata.options)\n        }\n        return { ...result.params };\n    }\n}\n\nexport const paramDecoratorProviders: StaticProvider<any>[] = [{\n    provide: ParamsMetadataKey,\n    useFactory: () => new ParamsDecorator(),\n    multi: true\n}, {\n    provide: InjectMetadataKey,\n    useFactory: () => new InjectDecorator(),\n    multi: true\n}]", "import { App } from \"./app\";\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from \"./error-handler\";\nimport { Injector } from \"./injector\";\nimport { paramDecoratorProviders } from \"./param-decorator\";\nimport { StaticProvider } from \"./provider\";\nimport { InjectionToken, Type } from \"./types\";\nimport { processRootModule } from \"./util\";\nexport class PlatformRef {\n    injector: Injector;\n    type: Type<any>;\n    constructor(injector: Injector) {\n        this.injector = injector;\n    }\n    async bootstrap(type: Type<any>) {\n        this.type = type;\n        return this.reload();\n    }\n    private async reload() {\n        const appInjector = await processRootModule(this.type, this.injector);\n        const app = new App(appInjector);\n        await app.bootstrap();\n        return appInjector;\n    }\n}\n\nlet _platform: PlatformRef;\nexport function createPlatform(injector: Injector): PlatformRef {\n    _platform = injector.get(PlatformRef);\n    return _platform;\n}\nexport function getPlatform(): PlatformRef | null {\n    return _platform ? _platform : null;\n}\n\nexport function createPlatformFactory(\n    parentPlatformFactory: ((extraProviders?: StaticProvider<any>[]) => PlatformRef) | null,\n    name: string,\n    providers: StaticProvider<any>[] = []\n): (extraProviders?: StaticProvider<any>[]) => PlatformRef {\n    const desc = `Platform: ${name}`;\n    const marker = new InjectionToken(desc);\n    return (extraProviders: StaticProvider<any>[] = []) => {\n        let platform = getPlatform();\n        if (!platform) {\n            if (parentPlatformFactory) {\n                parentPlatformFactory(providers.concat(extraProviders).concat({ provide: marker, useValue: true }));\n            } else {\n                const injectedProviders: StaticProvider<any>[] = providers.concat(extraProviders).concat({ provide: marker, useValue: true });\n                createPlatform(Injector.create(injectedProviders, undefined, 'platform'));\n            }\n        }\n        return assertPlatform(marker);\n    };\n}\n\nexport function assertPlatform(requiredToken: any): PlatformRef {\n    const platform = getPlatform();\n    if (!platform) {\n        const errorMessage = 'No platform exists!';\n        throw new Error(errorMessage);\n    }\n    if (!platform.injector.get(requiredToken, null)) {\n        throw new Error('A platform with a different configuration has been created. Please destroy it first.');\n    }\n    return platform;\n}\n\nexport const platformCore = createPlatformFactory(null, 'core', [\n    {\n        provide: ErrorHandler,\n        useFactory: () => new ErrorHandler()\n    },\n    {\n        provide: PlatformRef,\n        useFactory: (injector: Injector) => new PlatformRef(injector),\n        deps: [Injector]\n    },\n    ...paramDecoratorProviders\n]);\n", "import { Observable } from 'rxjs';\nexport interface Defer<T> extends Promise<T> {\n    resolve: (value?: T) => void;\n    reject: (reason?: any) => void;\n}\nexport function defer<T>(): Defer<T> {\n    let resolve!: (value?: T) => void;\n    let reject!: (reason?: any) => void;\n    const promise = new Promise<T>((_resolve, _reject) => {\n        resolve = _resolve as any;\n        reject = _reject;\n    }) as Defer<T>;\n    promise.resolve = resolve;\n    promise.reject = reject;\n    return promise;\n}\n\nexport async function* observableToAsyncGenerator<T>(observable: Observable<T>) {\n    let nextData: Defer<T> = defer();\n    const sub = observable.subscribe({\n        next(data) {\n            const n = nextData;\n            nextData = defer();\n            n.resolve(data);\n        },\n        error(err) {\n            nextData.reject(err);\n        },\n        complete() {\n            const n = nextData;\n            nextData = null as any;\n            n.resolve();\n        }\n    });\n    try {\n        for (; ;) {\n            const value = await nextData;\n            if (!nextData) break;\n            yield value;\n        }\n    } finally {\n        sub.unsubscribe();\n    }\n}", "export abstract class Log {\n    abstract info(...args: any[]): Promise<void> | void;\n    abstract error(...args: any[]): Promise<void> | void;\n    abstract warn(...args: any[]): Promise<void> | void;\n}", "export abstract class Config {\n    abstract get<T>(key: string, def ?: T): T;\n}", "import { Controller<PERSON>eta<PERSON><PERSON><PERSON> } from \"./decorator\";\nimport { Injector } from \"./injector\";\nimport { Log } from \"./log\";\nimport { Decorator } from \"./middleware\";\nimport { CLASS_METADATAS, CONTEXT, INSTANCE, MATCH_RESULT, METADATA, METADATA_PARAMETERS, NEXT, Next, NG_MODULE, UNINSTALL_HOOK } from \"./token\";\nimport { IMetadataClass, IMetadataMethodOrProperty } from \"./types\";\n\nexport class CoreMethodDecorator extends Decorator {\n    constructor(private method: string) {\n        super(`method decorator`)\n    }\n    toUrl(path: string = '') {\n        return path.split('/').filter(it => !!it).join('/')\n    }\n    async handle(injector: Injector, next: Next): Promise<void> {\n        const parent = injector.get<IMetadataMethodOrProperty<any>>(METADATA)\n        const options = parent.options;\n        const classMetadatas = injector.get(CLASS_METADATAS);\n        const controllerMetadata = classMetadatas.find((it: IMetadataClass<any>) => it.metadataKey === ControllerMetadataKey)\n        let path = options.path;\n        if (controllerMetadata) {\n            const url = `${this.toUrl(controllerMetadata.options || '')}/${this.toUrl(path || parent.propertyKey)}`\n            path = `/${this.toUrl(url)}`\n        }\n        const provide = `${this.method}${path}`;\n        const parameterFactory = injector.get(METADATA_PARAMETERS);\n        const log = injector.get(Log)\n        log.info(`regist method decorator`, provide);\n        const removes = injector.use([{\n            provide: provide,\n            useFactory: (result) => {\n                const instance = injector.get(INSTANCE);\n                const method = Reflect.get(instance, parent.propertyKey);\n                const handler = async (ctx: any, next: Next) => {\n                    const _injector = injector.create([\n                        { provide: MATCH_RESULT, useValue: result },\n                        { provide: INSTANCE, useValue: instance },\n                        { provide: CONTEXT, useValue: ctx },\n                        { provide: NEXT, useValue: next }\n                    ], 'handler');\n                    const parameters = await parameterFactory(_injector);\n                    return method.bind(instance)(...parameters)\n                }\n                return handler;\n            }\n        }], 'root');\n        const currentModule = injector.get(NG_MODULE)\n        injector.use([{\n            provide: UNINSTALL_HOOK,\n            useValue: { removes, type: currentModule },\n            multi: true\n        }], 'root')\n        if (next) await next()\n    }\n}"], "names": ["match"], "mappings": ";;;;;;;AAaM,SAAU,MAAM,CAAI,GAAQ,EAAA;AAC9B,IAAA,OAAO,OAAO,GAAG,KAAK,UAAU,CAAA;AACpC,CAAC;MAKY,cAAc,CAAA;AACM,IAAA,IAAA,CAAA;AAA7B,IAAA,WAAA,CAA6B,IAAY,EAAA;QAAZ,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;KAAK;AACjD,CAAA;AA2BK,SAAU,2BAA2B,CAAI,GAAQ,EAAA;AACnD,IAAA,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAA;AAC/E,CAAC;AAQK,SAAU,oBAAoB,CAAI,GAAQ,EAAA;IAC5C,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAA;AACpD;;AC7DA;;;;;;;;AAQG;AAIH,MAAM,gBAAgB,CAAA;AACV,IAAA,IAAI,GAAmB,IAAI,GAAG,EAAE,CAAC;IACzC,OAAO,CAAC,UAAgE,EAAE,OAAa,EAAA;QACnF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;KACzC;IACD,MAAM,GAAA;QACF,MAAM,MAAM,GAAQ,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,QAAA,OAAO,MAAM,CAAC;KACjB;AACD,IAAA,GAAG,CAAC,GAAM,EAAA;QACN,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACpB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAE,CAAA;AAC7B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,EAAK,CAAA;QACxB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,QAAA,OAAO,GAAG,CAAA;KACb;IACD,IAAI,CAAC,GAAM,EAAE,GAAM,EAAA;QACf,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAA,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACf,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACd,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;IAED,WAAW,CAAc,GAAM,EAAE,WAAmB,EAAA;QAChD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AACzB,QAAA,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,WAAW,KAAK,WAAW,CAAM,CAAC;KACnE;AAED,IAAA,aAAa,CAAC,GAAc,EAAA;QAKxB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAU,CAAC,CAAC;QACxC,MAAM,uBAAuB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACxD,MAAM,QAAQ,GAAqC,EAAE,CAAC;AACtD,QAAA,MAAM,UAAU,GAA8B,CAAC,GAAG,UAAU,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,oBAAoB,CAAC,EAAE,CAAC,CAAU,CAAC;AAC9G,QAAA,MAAM,OAAO,GAA0B,CAAC,GAAG,UAAU,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAU,CAAC;AACxG,QAAA,uBAAuB,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AACpC,YAAA,IAAI,2BAA2B,CAAC,GAAG,CAAC,EAAE;AAClC,gBAAA,GAAG,CAAC,UAAU,GAAG,CAAC,GAAG,uBAAuB,CAAC,CAAC,MAAM,CAAC,EAAE,IAAG;AACtD,oBAAA,IAAI,oBAAoB,CAAC,EAAE,CAAC,EAAE;AAC1B,wBAAA,OAAO,EAAE,CAAC,WAAW,IAAI,GAAG,CAAC,WAAW,CAAA;AAC3C,qBAAA;AACD,oBAAA,OAAO,KAAK,CAAC;AACjB,iBAAC,CAAU,CAAC;AACZ,gBAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACrB,aAAA;AACL,SAAC,CAAC,CAAA;QACF,OAAO;AACH,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,QAAQ;YACpB,UAAU;SACb,CAAA;KACJ;AACJ,CAAA;AAEY,MAAA,KAAK,GAAG,IAAI,gBAAgB;;ACzEzC;;AAEG;AACG,SAAU,oBAAoB,CAAI,WAAmB,EAAA;IACvD,OAAO,CAAC,OAAW,KAAI;QACnB,OAAO,CAAC,MAAW,KAAI;YACnB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,OAAO,EAAS,CAAC,CAAA;AACvD,SAAC,CAAA;AACL,KAAC,CAAA;AACL,CAAC;AAEK,SAAU,sBAAsB,CAAI,WAAmB,EAAA;AACzD,IAAA,OAAO,CAAC,EAAU,EAAE,OAAW,KAAI;QAC/B,OAAO,CAAC,MAAW,KAAI;AACnB,YAAA,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,OAAO,EAAS,CAAC,CAAA;AAC3D,SAAC,CAAA;AACL,KAAC,CAAA;AACL,CAAC;AAEK,SAAU,uBAAuB,CAAI,WAAmB,EAAA;IAC1D,OAAO,CAAC,OAAW,KAAuB;AACtC,QAAA,OAAO,CAAC,MAAW,EAAE,WAAgB,KAAI;;AAErC,YAAA,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAS,CAAC,CAAA;AACpE,SAAC,CAAA;AACL,KAAC,CAAA;AACL,CAAC;AACK,SAAU,qBAAqB,CAAI,WAAmB,EAAA;IACxD,OAAO,CAAC,OAAW,KAAqB;AACpC,QAAA,OAAO,CAAC,MAAW,EAAE,WAAgB,EAAE,UAAe,KAAI;;AAEtD,YAAA,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAS,CAAC,CAAA;AACrF,SAAC,CAAA;AACL,KAAC,CAAA;AACL,CAAC;AACK,SAAU,wBAAwB,CAAI,WAAmB,EAAA;IAC3D,OAAO,CAAC,OAAW,KAAwB;AACvC,QAAA,OAAO,CAAC,MAAW,EAAE,WAAgB,EAAE,cAAsB,KAAI;;AAE7D,YAAA,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,EAAS,CAAC,CAAA;AACpF,SAAC,CAAA;AACL,KAAC,CAAA;AACL;;ACvCA;;AAEG;AACI,MAAM,qBAAqB,GAA2B,qCAAqC;MAIrF,UAAU,GAAG,oBAAoB,CAAgB,qBAAqB,EAAE;AACrF;;AAEG;AACI,MAAM,qBAAqB,GAA2B,qCAAqC;MACrF,UAAU,GAAG,oBAAoB,CAAS,qBAAqB,EAAE;AAC9E;;AAEG;AACI,MAAM,eAAe,GAA2B,+BAA+B;MAMzE,IAAI,GAAG,oBAAoB,CAAU,eAAe,EAAE;AAa7D,SAAU,qBAAqB,CAAI,GAAQ,EAAA;AAC7C,IAAA,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAA;AAC7E,CAAC;AASM,MAAM,iBAAiB,GAA2B,iCAAiC;MAC7E,MAAM,GAAG,oBAAoB,CAAY,iBAAiB,EAAE;AAElE,MAAM,iBAAiB,GAA2B,iCAAiC;MAC7E,MAAM,GAAG,wBAAwB,CAAa,iBAAiB,EAAE;AAEvE,MAAM,iBAAiB,GAA2B,iCAAiC;MAC7E,MAAM,GAAG,wBAAwB,CAAS,iBAAiB,EAAE;AAEnE,MAAM,gBAAgB,GAA2B,gCAA+B;MAC1E,KAAK,GAAG,wBAAwB,CAAS,gBAAgB,EAAC;AAGvD,SAAA,yBAAyB,CAAC,GAAW,EAAE,MAAc,EAAA;AACjE,IAAA,MAAM,SAAS,GAAG,qBAAqB,CAAmC,GAAG,CAAC,CAAA;AAC9E,IAAA,OAAO,CAAC,IAAY,KAAK,SAAS,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;AACxD,CAAC;AAEM,MAAM,cAAc,GAA2B,8BAA8B;AACvE,MAAA,GAAG,GAAG,yBAAyB,CAAC,cAAc,EAAE,KAAK;;ACzD5D,SAAU,QAAQ,CAAC,GAAQ,EAAA;IAC7B,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;AAC5C,CAAC;AAMK,SAAU,aAAa,CAAC,GAAQ,EAAA;IAClC,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;AACjD;;ACXM,SAAU,kBAAkB,CAAI,GAAgB,EAAA;IAClD,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAA;AAC9C,CAAC;SAEe,QAAQ,CAAI,KAAe,EAAE,KAAQ,EAAE,KAAe,EAAA;IAClE,OAAO;AACH,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,QAAQ,EAAE,KAAK;QACf,KAAK;KACR,CAAA;AACL,CAAC;AAOK,SAAU,kBAAkB,CAAI,GAAgB,EAAA;IAClD,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAA;AAC9C,CAAC;AAEK,SAAU,QAAQ,CAAI,KAAe,EAAE,GAAY,EAAE,IAAA,GAAc,EAAE,EAAE,KAAe,EAAA;IACxF,OAAO;AACH,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,QAAQ,EAAE,GAAG;QACb,KAAK;QACL,IAAI;KACP,CAAA;AACL,CAAC;AAEK,SAAU,aAAa,CAAI,KAAe,EAAE,GAAY,EAAE,OAAc,EAAE,EAAA;IAC5E,OAAO,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAC3C,CAAC;AASK,SAAU,oBAAoB,CAAI,GAAgB,EAAA;IACpD,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAA;AAChD,CAAC;AAEK,SAAU,UAAU,CAAI,KAAe,EAAE,OAAmB,EAAE,IAAA,GAAc,EAAE,EAAE,KAAe,EAAA;IACjG,OAAO;AACH,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,UAAU,EAAE,OAAO;QACnB,KAAK;AACL,QAAA,IAAI,EAAE,IAAI;KACb,CAAA;AACL,CAAC;AAEK,SAAU,eAAe,CAAI,KAAe,EAAE,OAAmB,EAAE,OAAc,EAAE,EAAA;IACrF,OAAO,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AACjD,CAAC;AAGK,SAAU,cAAc,CAAI,GAAQ,EAAA;AACtC,IAAA,OAAO,GAAG,IAAI,OAAO,GAAG,KAAK,UAAU,CAAA;AAC3C,CAAC;AAMK,SAAU,kBAAkB,CAAI,GAAQ,EAAA;IAC1C,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;AACjD;;AC9EM,SAAU,aAAa,CAAC,MAAW,EAAA;IACrC,OAAO,OAAO,CAAC,WAAW,CAAC,aAAa,EAAE,MAAM,CAAC,CAAA;AACrD,CAAC;AACK,SAAU,mBAAmB,CAAC,MAAW,EAAA;IAC3C,OAAO,OAAO,CAAC,WAAW,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAA;AAC3D,CAAC;AACK,SAAU,mBAAmB,CAAC,MAAW,EAAA;IAC3C,OAAO,OAAO,CAAC,WAAW,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAA;AAC3D;;ACJM,SAAU,QAAQ,CAAC,GAAQ,EAAA;IAC7B,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,cAAc,CAAC,GAAG,CAAC,KAAK,iBAAiB,CAAC;AACtE,CAAC;AACK,SAAU,cAAc,CAAC,CAAM,EAAA;IACjC,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC;AACK,SAAU,QAAQ,CAAC,GAAQ,EAAA;IAC7B,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,CAAA;AAClD,CAAC;AACK,SAAU,MAAM,CAAC,CAAM,EAAA;IACzB,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC;AAChE,CAAC;AACK,SAAU,WAAW,CAAC,GAAQ,EAAA;IAChC,OAAO,GAAG,KAAK,IAAI;QACf,OAAO,GAAG,KAAK,SAAS;QACxB,OAAO,GAAG,KAAK,QAAQ;QACvB,OAAO,GAAG,KAAK,QAAQ;AACvB,QAAA,OAAO,GAAG,KAAK,QAAQ;QACvB,OAAO,GAAG,KAAK,WAAW,CAAC;AACnC,CAAC;AACK,SAAU,GAAG,CAAC,CAAS,EAAA;IACzB,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC1D,CAAC;AACK,SAAU,QAAQ,CAAC,GAAQ,EAAA;AAC7B,IAAA,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAA;AAClC,CAAC;MAEqB,QAAQ,CAAA;IAC1B,IAAI,GAAc,IAAI,CAAC;AAOvB,IAAA,OAAO,MAAM,CAAC,SAAgC,EAAE,MAAiB,EAAE,IAAgB,EAAA;QAC/E,OAAO,IAAI,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;KACpD;AACJ,CAAA;AASD,IAAI,QAA8B,CAAC;AACnC,IAAI,YAAkC,CAAC;AACvC,SAAS,kBAAkB,CAAC,QAAkB,EAAA;IAC1C,YAAY,GAAG,QAAQ,CAAC;IACxB,QAAQ,GAAG,QAAQ,CAAC;AACxB,CAAC;SACe,WAAW,GAAA;AACvB,IAAA,OAAO,QAAS,CAAC;AACrB,CAAC;AACD,SAAS,aAAa,GAAA;IAClB,QAAQ,GAAG,YAAY,CAAC;AAC5B,CAAC;AACY,MAAA,iBAAiB,GAA6B,IAAI,GAAG,GAAG;AAC9D,MAAM,aAAa,GAAe,GAAG;AACtC,MAAO,YAAa,SAAQ,QAAQ,CAAA;AAC9B,IAAA,GAAG,GAAiC,IAAI,GAAG,EAAE,CAAC;AAC9C,IAAA,QAAQ,GAAsC,IAAI,GAAG,EAAE,CAAC;AAChE,IAAA,MAAM,CAAuB;IAC7B,OAAO,GAAe,EAAE,CAAC;AACzB,IAAA,WAAA,CACI,SAAgC,EAChC,MAAiB,EACjB,IAAgB,EAAA;AAEhB,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC/E,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAClC,QAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC5B;IACD,GAAG,CACC,SAAsD,EACtD,IAAgB,EAAA;QAEhB,MAAM,OAAO,GAAe,EAAE,CAAC;AAC/B,QAAA,IAAI,IAAI,EAAE;AACN,YAAA,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;AACpB,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;oBAC1B,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;AAC3F,iBAAA;AAAM,qBAAA;oBACH,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC,CAAA;AAC1D,iBAAA;AACJ,aAAA;YACD,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,gBAAA,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAA;AACpD,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAC1B,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;AAC3F,aAAA;AAAM,iBAAA;gBACH,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC,CAAA;AAC1D,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,OAAO,CAAC;KAClB;IACD,MAAM,CAAC,SAAgC,EAAE,IAAgB,EAAA;QACrD,OAAO,IAAI,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;KACjD;AACD,IAAA,MAAM,CAAC,MAA2D,EAAA;QAC9D,MAAM,KAAK,GAAiB,EAAE,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,GAAG,KAAI;AACzB,YAAA,IAAI,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;AACjB,gBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAClB,aAAA;AACL,SAAC,CAAC,CAAA;AACF,QAAA,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAI;AACd,YAAA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;AACxB,SAAC,CAAC,CAAA;KACL;AACD,IAAA,WAAW,CAAC,MAA2D,EAAA;QACnE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,GAAG,KAAI;YAC9B,MAAM,OAAO,GAAkB,EAAE,CAAC;AAClC,YAAA,EAAE,CAAC,OAAO,CAAC,IAAI,IAAG;AACd,gBAAA,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;AACnB,oBAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACrB,iBAAA;AACL,aAAC,CAAC,CAAC;AACH,YAAA,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;AAC5C,SAAC,CAAC,CAAA;KACL;IACD,QAAQ,CAAI,KAAe,EAAE,EAAc,EAAA;QACvC,IAAI;YACA,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACzB,IAAI,MAAM,GAAQ,EAAE,CAAC;YACrB,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AACpC,YAAA,IAAI,GAAG,EAAE;gBACL,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,MAAiB,KAAI;AAC/B,oBAAA,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,WAAW,EAAE;AACrC,wBAAA,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;AACnC,qBAAA;AACD,oBAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;AAC7B,iBAAC,CAAC,CAAC;gBACH,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE;AAC/B,oBAAA,OAAO,MAAM,CAAC;AACjB,iBAAA;gBACD,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,oBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACpD,oBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AAC5B,wBAAA,MAAM,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;AAC/B,qBAAA;AACJ,iBAAA;AACD,gBAAA,OAAO,MAAM,CAAC;AACjB,aAAA;YACD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE;AAC/B,gBAAA,OAAO,MAAM,CAAC;AACjB,aAAA;YACD,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;AACzC,aAAA;AACD,YAAA,OAAO,MAAM,CAAC;AACjB,SAAA;AAAS,gBAAA;AACN,YAAA,aAAa,EAAE,CAAA;AAClB,SAAA;KACJ;IACD,GAAG,CAAI,KAAe,EAAE,QAAkB,EAAA;QACtC,IAAI,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,IAAI,EAAE;AAChD,YAAA,SAAS;YACT,OAAO;AACV,SAAA;QACD,IAAI;YACA,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACzB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACnC,YAAA,IAAI,MAAM,EAAE;AACR,gBAAA,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,WAAW,EAAE;AACrC,oBAAA,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;AACnC,iBAAA;gBACD,OAAO,MAAM,CAAC,KAAK,CAAC;AACvB,aAAA;AACD,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC3B,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;AAClC,gBAAA,IAAI,MAAoC,CAAC;gBACzC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAG;oBAC3B,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;AAChC,wBAAA,MAAM,MAAM,GAAGA,kBAAK,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC,CAAC;AAC1D,wBAAA,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC9B,wBAAA,IAAI,OAAO,EAAE;4BACT,MAAM,GAAG,OAAO,CAAC;AACjB,4BAAA,OAAO,IAAI,CAAC;AACf,yBAAA;AACD,wBAAA,OAAO,KAAK,CAAC;AAChB,qBAAA;AACL,iBAAC,CAAE,CAAC;gBACJ,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;AACnC,gBAAA,IAAI,MAAM,EAAE;AACR,oBAAA,OAAO,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;AAChC,iBAAA;AACJ,aAAA;YACD,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;AAC1C,aAAA;AACD,YAAA,OAAO,QAAa,CAAC;AACxB,SAAA;AAAS,gBAAA;AACN,YAAA,aAAa,EAAE,CAAA;AAClB,SAAA;KAEJ;AACO,IAAA,sBAAsB,CAAI,QAAuC,EAAA;AACrE,QAAA,IAAI,cAAc,CAAC,QAAQ,CAAC,EAAE;;YAE1B,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;AAChD,YAAA,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAA;AAC1C,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;AACxC,SAAA;KACJ;AACO,IAAA,cAAc,CAAI,IAAqB,EAAA;QAC3C,OAAO;AACH,YAAA,OAAO,EAAE,IAAI;AACb,YAAA,QAAQ,EAAE,IAAI;AACd,YAAA,IAAI,EAAE,mBAAmB,CAAC,IAAI,CAAC;SAClC,CAAA;KACJ;AACO,IAAA,eAAe,CAAI,QAAqB,EAAA;QAC5C,MAAM,OAAO,GAAe,EAAE,CAAC;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAC/C,QAAA,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE;;YAElB,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AACrC,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAE,CAAC;gBACjD,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;AACzD,gBAAA,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;AAClB,aAAA;AAAM,iBAAA;gBACH,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAA;gBAC7B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;gBACxC,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAA;AAC1F,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACnC,YAAA,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;AAClF,SAAA;AACD,QAAA,OAAO,OAAO,CAAC;KAClB;AACO,IAAA,gBAAgB,CAAI,QAAqB,EAAA;AAC7C,QAAA,IAAI,kBAAkB,CAAC,QAAQ,CAAC,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAC1C,SAAA;AACI,aAAA,IAAI,kBAAkB,CAAC,QAAQ,CAAC,EAAE;AACnC,YAAA,OAAO,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;AAClD,SAAA;AACI,aAAA,IAAI,oBAAoB,CAAC,QAAQ,CAAC,EAAE;AACrC,YAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;AACpD,SAAA;AAAM,aAAA,IAAI,kBAAkB,CAAC,QAAQ,CAAC,EAAE;YACrC,OAAO;gBACH,KAAK,EAAE,QAAQ,CAAC,OAAO;gBACvB,OAAO,EAAE,MAAK;oBACV,OAAO,IAAI,CAAC,GAAG,CAAM,QAAQ,CAAC,WAAW,CAAC,CAAC;iBAC9C;AACD,gBAAA,KAAK,EAAE,SAAS;aACnB,CAAA;AACJ,SAAA;AAAM,aAAA;YACH,OAAO;gBACH,KAAK,EAAE,QAAQ,CAAC,OAAO;gBACvB,OAAO,EAAE,MAAK;AACV,oBAAA,OAAO,EAAO,CAAC;iBAClB;AACD,gBAAA,KAAK,EAAE,SAAS;aACnB,CAAA;AACJ,SAAA;KACJ;AACO,IAAA,0BAA0B,CAAI,QAA+B,EAAA;QACjE,OAAO;YACH,KAAK,EAAE,QAAQ,CAAC,OAAO;AACvB,YAAA,OAAO,EAAE,CAAC,GAAG,IAAW,KAAI;AACxB,gBAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACjB,oBAAA,OAAO,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;AACvC,iBAAA;AAAM,qBAAA;AACH,oBAAA,MAAM,IAAI,GAAU,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;AACxC,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;AAC5C,oBAAA,OAAO,QAAQ,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,CAAC;AACxC,iBAAA;aACJ;AACD,YAAA,KAAK,EAAE,SAAS;SACnB,CAAA;KACJ;AACO,IAAA,wBAAwB,CAAI,QAA6B,EAAA;QAC7D,OAAO;YACH,KAAK,EAAE,QAAQ,CAAC,OAAO;AACvB,YAAA,OAAO,EAAE,CAAC,GAAG,IAAW,KAAI;AACxB,gBAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;oBACjB,OAAO,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;AACzC,iBAAA;AAAM,qBAAA;AACH,oBAAA,MAAM,IAAI,GAAU,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;AACxC,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;oBAC5C,OAAO,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC;AAC1C,iBAAA;aACJ;AACD,YAAA,KAAK,EAAE,SAAS;SACnB,CAAA;KACJ;AACO,IAAA,gBAAgB,CAAI,QAA6B,EAAA;QACrD,OAAO;YACH,KAAK,EAAE,QAAQ,CAAC,OAAO;YACvB,OAAO,EAAE,MAAK;gBACV,OAAO,QAAQ,CAAC,QAAQ,CAAC;aAC5B;AACD,YAAA,KAAK,EAAE,SAAS;SACnB,CAAA;KACJ;AACJ;;MCtTqB,UAAU,CAAA;AAC5B,IAAA,IAAI,CAAS;IACb,OAAO,GAAW,OAAO,CAAC;IAC1B,MAAM,GAAW,CAAC,CAAC;AACnB,IAAA,WAAA,CAAY,IAAY,EAAA;AACpB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB;AAEJ,CAAA;AACK,MAAgB,MAAO,SAAQ,UAAU,CAAA;AAAI,CAAA;AAC7C,MAAgB,SAAU,SAAQ,UAAU,CAAA;AAAI,CAAA;AAChD,MAAgB,YAAa,SAAQ,UAAU,CAAA;AAAI,CAAA;AACnD,MAAgB,OAAQ,SAAQ,UAAU,CAAA;AAAI,CAAA;AAC9C,MAAgB,SAAU,SAAQ,UAAU,CAAA;AAAI,CAAA;AAChD,MAAgB,QAAS,SAAQ,UAAU,CAAA;AAAI;;MCZxC,IAAI,GAAG,IAAI,cAAc,CAAa,CAA0B,wBAAA,CAAA,EAAE;MAClE,OAAO,GAAG,IAAI,cAAc,CAAM,CAA6B,2BAAA,CAAA,EAAE;MAEjE,QAAQ,GAAG,IAAI,cAAc,CAAM,CAAqB,mBAAA,CAAA,EAAE;MAE1D,QAAQ,GAAG,IAAI,cAAc,CAAiB,CAAqB,mBAAA,CAAA,EAAC;MAKpE,SAAS,GAAG,IAAI,cAAc,CAAa,CAAsB,oBAAA,CAAA,EAAC;MAClE,eAAe,GAAG,IAAI,cAAc,CAAwB,CAA4B,0BAAA,CAAA,EAAC;MACzF,mBAAmB,GAAG,IAAI,cAAc,CAAW,CAAyB,uBAAA,CAAA,EAAC;MAC7E,YAAY,GAAG,IAAI,cAAc,CAAY,CAAyB,uBAAA,CAAA,EAAC;MAEvE,YAAY,GAAG,IAAI,cAAc,CAAc,CAAyB,uBAAA,CAAA,EAAC;MACzE,WAAW,GAAG,IAAI,cAAc,CAAM,CAAwB,sBAAA,CAAA,EAAC;MAC/D,kBAAkB,GAAG,IAAI,cAAc,CAAQ,CAA+B,6BAAA,CAAA,EAAC;MAC/E,kBAAkB,GAAG,IAAI,cAAc,CAAM,CAA+B,6BAAA,CAAA,EAAC;AAC1F;MACa,IAAI,GAAG,IAAI,cAAc,CAAM,CAAiB,eAAA,CAAA,EAAE;MAClD,QAAQ,GAAG,IAAI,cAAc,CAAS,CAAqB,mBAAA,CAAA,EAAE;MAC7D,UAAU,GAAG,IAAI,cAAc,CAAS,CAAuB,qBAAA,CAAA,EAAE;MACjE,UAAU,GAAG,IAAI,cAAc,CAAS,CAAuB,qBAAA,CAAA,EAAE;MAGjE,eAAe,GAAG,IAAI,cAAc,CAAiB,CAA4B,0BAAA,CAAA,EAAE;AAChG;MACa,SAAS,GAAG,IAAI,cAAc,CAAS,CAAsB,oBAAA,CAAA,EAAE;MAC/D,cAAc,GAAG,IAAI,cAAc,CAA2C,CAA2B,yBAAA,CAAA,EAAC;MAC1G,SAAS,GAAG,IAAI,cAAc,CAAY,CAAsB,oBAAA,CAAA,EAAC;MAEjE,KAAK,GAAG,IAAI,cAAc,CAAM,CAAkB,gBAAA,CAAA;;ACRzD,SAAU,OAAO,CAAC,UAAwB,EAAA;AAC5C,IAAA,OAAO,CAAC,QAAkB,EAAE,IAAW,KAAI;AACvC,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,CAAA;AACd,QAAA,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnB,SAAS,QAAQ,CAAC,CAAS,EAAA;YACvB,IAAI,CAAC,IAAI,KAAK;gBAAE,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC,CAAA;YAChF,KAAK,GAAG,CAAC,CAAA;AACT,YAAA,IAAI,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;AACtB,YAAA,IAAI,CAAC,KAAK,UAAU,CAAC,MAAM,EAAE;gBACzB,EAAE,GAAG,IAAK,CAAA;AACb,aAAA;AACD,YAAA,IAAI,CAAC,EAAE;AAAE,gBAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;YACjC,IAAI;AACA,gBAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;gBACxC,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,CAAA,IAAA,CAAM,CAAC,CAAA;gBACjF,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;AACvE,aAAA;AAAC,YAAA,OAAO,GAAG,EAAE;AACV,gBAAA,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;AAC7B,aAAA;SACJ;AACL,KAAC,CAAA;AACL;;AC7CM,MAAO,eAAgB,SAAQ,UAAU,CAAA;AACvB,IAAA,WAAA,CAAA;AAApB,IAAA,WAAA,CAAoB,WAAyB,EAAA;QACzC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QADpB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAc;KAE5C;AACD,IAAA,MAAM,MAAM,CAAC,QAAkB,EAAE,IAAW,EAAA;AACxC,QAAA,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,IAAG;AACtD,YAAA,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE;AAC3B,gBAAA,OAAO,GAAG,CAAC;AACd,aAAA;AAAM,iBAAA;gBACH,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAC9B,aAAA;AACL,SAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;KAC9C;AACJ;;MCdY,GAAG,CAAA;IACZ,OAAO,GAAa,EAAE,CAAC;IACvB,WAAW,GAAiB,EAAE,CAAC;AAC/B,IAAA,QAAQ,CAAW;AACnB,IAAA,WAAA,CAAY,QAAmB,EAAA;AAC3B,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAA;AAClE,QAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA;KAC9D;AACD,IAAA,MAAM,SAAS,GAAA;AACX;;AAEG;QACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC9C,QAAA,MAAM,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC9D;;AAEG;QACH,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;AAC3D,QAAA,MAAM,IAAI,eAAe,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/D;;AAEG;QACH,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACjD,QAAA,MAAM,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1D;;AAEG;QACH,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AACrD,QAAA,MAAM,IAAI,eAAe,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAE3D,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACnD,QAAA,MAAM,IAAI,eAAe,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC1D,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;AACJ;;MCpCqB,OAAO,CAAA;AAE5B;;ACSK,SAAU,OAAO,CAAC,GAAW,EAAA;AAC/B,IAAA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;AAC9B,CAAC;AACK,SAAU,SAAS,CAAI,IAAO,EAAA;AAChC,IAAA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;AAC9B,CAAC;AACe,SAAA,MAAM,CAAI,GAAW,EAAE,IAAO,EAAA;IAC1C,OAAO;AACH,QAAA,GAAG,EAAE,IAAI;KACZ,CAAA;AACL,CAAC;AAKe,SAAA,SAAS,CAAI,MAAc,EAAE,IAAO,EAAA;IAChD,OAAO;QACH,MAAM;QACN,IAAI;KACP,CAAA;AACL,CAAC;AACD,SAAS,gBAAgB,CAAC,SAAoB,EAAA;AAC1C,IAAA,IAAI,SAAS,EAAE;QACX,IAAI,GAAG,GAAQ,SAAS,CAAC;QACzB,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA,IAAA,CAAM,CAAC,EAAE;YAC1B,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA,IAAA,CAAM,CAAC,CAAC;AACnC,SAAA;AACD,QAAA,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC/B,KAAA;AACD,IAAA,OAAO,EAAE,CAAA;AACb,CAAC;AACM,eAAe,+BAA+B,CAAC,QAAkB,EAAE,QAAwC,EAAA;IAC9G,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAY,QAAQ,CAAC,WAAW,CAAC,CAAC;AACpE,IAAA,MAAM,UAAU,GAAG,CAAC,cAAwB,KAAI;QAC5C,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE;AACxC,aAAA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,cAAc,CAAC;aACnD,GAAG,CAAC,SAAS,IAAG;YACb,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAY,SAAS,CAAC,WAAW,CAAC,CAAA;AACpE,YAAA,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;YACjE,OAAO,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;SAC/D,CAAC,CAAC,CAAA;AACX,KAAC,CAAA;AACD,IAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC7B,IAAA,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;AACtF,IAAA,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,CAAC,CAAA;IACzJ,MAAM,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,CAAA;AAC/D,CAAC;AACM,eAAe,iBAAiB,CAAC,QAAkB,EAAA;AACtD,IAAA,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC;AAC9E,IAAA,OAAO,eAAe,CAAC,GAAG,CAAC,MAAM,IAAG;AAChC,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACvB,YAAA,OAAO,MAAM,CAAC;AACjB,SAAA;AAAM,aAAA;YACH,OAAO,CAAC,MAAM,CAAC,CAAA;AAClB,SAAA;AACL,KAAC,CAAC,CAAC,IAAI,EAAiB,CAAC;AAC7B,CAAC;AACM,eAAe,iBAAiB,CAAC,IAAe,EAAE,IAAc,EAAA;AACnE,IAAA,MAAM,eAAe,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACtD,IAAA,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;AAC1E,IAAA,OAAO,QAAQ,CAAC;AACpB,CAAC;AAEM,eAAe,aAAa,CAAC,IAAe,EAAE,IAAc,EAAE,IAAgB,EAAE,OAAqB,EAAA;AACxG,IAAA,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AAC7B,IAAA,GAAG,CAAC,OAAO,GAAG,CAAC,GAAG,OAAO,IAAI,EAAE,EAAE,GAAG,GAAG,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;IACvD,MAAM,UAAU,GAAG,MAAM,qBAAqB,CAAC,GAAS,CAAC,CAAA;;IAEzD,MAAM,UAAU,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;IAC5C,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,UAAU,CAAC;IACvD,MAAM,WAAW,GAAG,CAAC,QAAkB,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,SAAS,IAAG;QACvF,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAY,SAAS,CAAC,WAAW,CAAC,CAAA;AACpE,QAAA,MAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QACxF,OAAO,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;KAClE,CAAC,CAAC,CAAC;AACJ,IAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC;AAC/C,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC;AACzB,QAAA,GAAG,UAAU;AACb,QAAA,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE;AACtC,QAAA;AACI,YAAA,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,CAAC,QAAkB,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;YACtD,IAAI,EAAE,CAAC,QAAQ,CAAC;SACnB,EAAE;AACC,YAAA,OAAO,EAAE,WAAW;AACpB,YAAA,UAAU,EAAE,MAAM,aAAa,CAAC,IAAI,CAAC;SACxC,EAAE;AACC,YAAA,OAAO,EAAE,kBAAkB;YAC3B,UAAU,EAAE,MAAM,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE;SACpD,EAAE;AACC,YAAA,OAAO,EAAE,kBAAkB;AAC3B,YAAA,UAAU,EAAE,MAAM,mBAAmB,CAAC,IAAI,CAAC;SAC9C,EAAE;AACC,YAAA,OAAO,EAAE,eAAe;AACxB,YAAA,QAAQ,EAAE,OAAO;AACpB,SAAA;AACD,QAAA;AACI,YAAA,OAAO,EAAE,IAAI;AACb,YAAA,UAAU,EAAE,CAAC,QAAkB,KAAI;AAC/B,gBAAA,MAAM,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;gBACnC,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAA;gBACnD,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;oBAC1C,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBAC1B,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;AAClC,qBAAA;AACD,oBAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AAC9B,iBAAC,CAAC,CAAA;AACF,gBAAA,OAAO,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;aAC7B;YACD,IAAI,EAAE,CAAC,QAAQ,CAAC;AACnB,SAAA;AAAC,KAAA,CAAC,CAAC;IACR,QAAQ,CAAC,GAAG,CAAC,CAAC;AACV,YAAA,OAAO,EAAE,cAAc;AACvB,YAAA,QAAQ,EAAE;gBACN,IAAI;gBACJ,OAAO;AACV,aAAA;AACD,YAAA,KAAK,EAAE,IAAI;SACd,CAAC,EAAE,MAAM,CAAC,CAAC;;AAEZ,IAAA,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAgB,CAAC;IACtE,IAAI,CAAC,CAAC,GAAG,EAAE;QACP,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QACrC,IAAI,CAAC,EAAE,EAAE;AACL,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,2CAAA,CAA6C,CAAC,CAAA;AACjE,SAAA;AACD,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,OAAO,CAAC,KAAK,CAAC,EAAG,EAAE,IAAI,CAAC,CAAA;AAC3B,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,CAAC,GAAG,CAAC,CAAA,yCAAA,CAA2C,CAAC,CAAA;AAC3D,SAAA;AACJ,KAAA;IACD,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,WAAW,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAA,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/B,QAAA,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAM,GAAG,KAAG;AACtC,YAAA,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE;AACb,gBAAA,MAAM,aAAa,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACtC,aAAA;AAAM,iBAAA,IAAI,qBAAqB,CAAC,GAAG,CAAC,EAAE;AACnC,gBAAA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC;AAClC,gBAAA,MAAM,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;AACxC,aAAA;AAAM,iBAAA,CAIN;SACJ,CAAC,CAAC,CAAA;AACN,KAAA;IACD,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACpC,IAAA,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACpB,QAAA,MAAM,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/B,KAAA;AACD,IAAA,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;AACzB,QAAA,MAAM,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACpC,KAAA;AACD,IAAA,OAAO,QAAQ,CAAC;AACpB,CAAC;AAEM,eAAe,qBAAqB,CAAC,GAAc,EAAE,QAAkB,EAAA;IAC1E,IAAI,aAAa,GAA0B,EAAE,CAAC;IAC9C,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,GAAG,IAAI,EAAE,CAAC;AACtD,IAAA,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/B,QAAA,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAM,GAAG,KAAG;AACtC,YAAA,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE;AACb,gBAAA,MAAM,SAAS,GAAG,MAAM,qBAAqB,CAAC,UAAU,CAAC,GAAG,CAAW,CAAC,CAAC;AACzE,gBAAA,aAAa,GAAG;AACZ,oBAAA,GAAG,aAAa;AAChB,oBAAA,GAAG,SAAS;iBACf,CAAA;AACJ,aAAA;AAAM,iBAAA,IAAI,qBAAqB,CAAC,GAAG,CAAC,EAAE;AACnC,gBAAA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC;AAClC,gBAAA,MAAM,eAAe,GAAG,MAAM,qBAAqB,CAAC,UAAU,CAAC,MAAM,CAAW,CAAC,CAAA;AACjF,gBAAA,aAAa,GAAG;AACZ,oBAAA,GAAG,aAAa;AAChB,oBAAA,GAAG,eAAe;AAClB,oBAAA,GAAG,SAAS;iBACf,CAAA;AACJ,aAAA;AAAM,iBAAA,CAQN;SACJ,CAAC,CAAC,CAAA;AACN,KAAA;AACD,IAAA,aAAa,GAAG;AACZ,QAAA,GAAG,aAAa;QAChB,GAAG,SAAS,IAAI,EAAE;KACrB,CAAA;AACD,IAAA,OAAO,aAAa,CAAC;AACzB,CAAC;AAEK,SAAU,UAAU,CAAC,IAAe,EAAA;AACtC,IAAA,IAAI,CAAC,IAAI;AAAE,QAAA,OAAO,EAAE,CAAC;IACrB,MAAM,UAAU,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;IAC5C,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,UAAU,CAAC;AACvD,IAAA,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,WAAW,KAAK,iBAAiB,CAAC,CAAC;AACtE,IAAA,OAAO,IAAI,IAAI,IAAK,CAAC,OAAO,IAAI,EAAE,CAAC;AACvC,CAAC;AAEK,SAAU,cAAc,CAAC,IAAe,EAAA;IAC1C,MAAM,UAAU,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;IAC5C,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,UAAU,CAAC;AACvD,IAAA,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,WAAW,KAAK,iBAAiB,CAAC,CAAC;IACtE,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AAEM,eAAe,WAAW,CAAC,QAAkB,EAAE,IAAe,EAAA;IACjE,MAAM,UAAU,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;IAC5C,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,UAAU,CAAC;IACvD,MAAM,WAAW,GAAG,CAAC,QAAkB,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,SAAS,IAAG;QACvF,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAY,SAAS,CAAC,WAAW,CAAC,CAAA;QACpE,MAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;QACtG,OAAO,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;KAClE,CAAC,CAAC,CAAC;IACJ,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAC/C,YAAY,CAAC,GAAG,CAAC,CAAC;AACd,YAAA,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,CAAC,QAAkB,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;YACtD,IAAI,EAAE,CAAC,QAAQ,CAAC;SACnB,EAAE;AACC,YAAA,OAAO,EAAE,WAAW;AACpB,YAAA,UAAU,EAAE,MAAM,aAAa,CAAC,IAAI,CAAC;SACxC,EAAE;AACC,YAAA,OAAO,EAAE,kBAAkB;YAC3B,UAAU,EAAE,MAAM,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE;SACpD,EAAE;AACC,YAAA,OAAO,EAAE,kBAAkB;AAC3B,YAAA,UAAU,EAAE,MAAM,mBAAmB,CAAC,IAAI,CAAC;SAC9C,EAAE;AACC,YAAA,OAAO,EAAE,eAAe;AACxB,YAAA,QAAQ,EAAE,OAAO;AACpB,SAAA;AACD,QAAA;AACI,YAAA,OAAO,EAAE,IAAI;AACb,YAAA,UAAU,EAAE,CAAC,QAAkB,KAAI;AAC/B,gBAAA,MAAM,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;gBACnC,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAA;gBACnD,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;oBAC1C,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBAC1B,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;AAClC,qBAAA;AACD,oBAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AAC9B,iBAAC,CAAC,CAAA;AACF,gBAAA,OAAO,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;aAC7B;YACD,IAAI,EAAE,CAAC,QAAQ,CAAC;AACnB,SAAA,CAAC,CAAC,CAAC;AACJ,IAAA,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,KAAI;QAC7C,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAY,GAAG,CAAC,WAAW,CAAC,CAAC;AACnE,QAAA,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,YAAA,MAAM,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC;AAClC,gBAAA,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,EAAE;AACpC,gBAAA,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE;AAC5C,gBAAA,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE;AAC5C,aAAA,CAAC,CAAC;YACH,MAAM,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACzD,SAAA;KACJ,CAAC,CAAC,CAAC;IACJ,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,+BAA+B,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACzF,IAAA,OAAO,YAAY,CAAC;AACxB;;MCpRa,YAAY,CAAA;AACrB,IAAA,MAAM,CAAC,GAAU,EAAA;AACb,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;KAC3B;AACJ;;ACGK,MAAO,eAAgB,SAAQ,SAAS,CAAA;AAC1C,IAAA,WAAA,GAAA;QACI,KAAK,CAAC,iBAAiB,CAAC,CAAA;KAC3B;AACD,IAAA,MAAM,CAAC,QAAkB,EAAA;QACrB,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACvC,IAAI,QAAQ,CAAC,OAAO,EAAE;YAClB,OAAO,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;AACxC,SAAA;AACD,QAAA,OAAO,QAAQ,CAAC;KACnB;AACJ,CAAA;AAEK,MAAO,eAAgB,SAAQ,SAAS,CAAA;AAC1C,IAAA,WAAA,GAAA;QACI,KAAK,CAAC,iBAAiB,CAAC,CAAA;KAC3B;IACD,MAAM,CAAC,QAAkB,EAAE,IAAU,EAAA;QACjC,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAc,YAAY,CAAC,CAAA;QACtD,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACvC,IAAI,QAAQ,CAAC,OAAO,EAAE;AAClB,YAAA,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAA;AACtD,SAAA;AACD,QAAA,OAAO,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;KAC/B;AACJ,CAAA;AAEM,MAAM,uBAAuB,GAA0B,CAAC;AAC3D,QAAA,OAAO,EAAE,iBAAiB;AAC1B,QAAA,UAAU,EAAE,MAAM,IAAI,eAAe,EAAE;AACvC,QAAA,KAAK,EAAE,IAAI;KACd,EAAE;AACC,QAAA,OAAO,EAAE,iBAAiB;AAC1B,QAAA,UAAU,EAAE,MAAM,IAAI,eAAe,EAAE;AACvC,QAAA,KAAK,EAAE,IAAI;AACd,KAAA,CAAC;;MCnCW,WAAW,CAAA;AACpB,IAAA,QAAQ,CAAW;AACnB,IAAA,IAAI,CAAY;AAChB,IAAA,WAAA,CAAY,QAAkB,EAAA;AAC1B,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;KAC5B;IACD,MAAM,SAAS,CAAC,IAAe,EAAA;AAC3B,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;KACxB;AACO,IAAA,MAAM,MAAM,GAAA;AAChB,QAAA,MAAM,WAAW,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtE,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC;AACjC,QAAA,MAAM,GAAG,CAAC,SAAS,EAAE,CAAC;AACtB,QAAA,OAAO,WAAW,CAAC;KACtB;AACJ,CAAA;AAED,IAAI,SAAsB,CAAC;AACrB,SAAU,cAAc,CAAC,QAAkB,EAAA;AAC7C,IAAA,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACtC,IAAA,OAAO,SAAS,CAAC;AACrB,CAAC;SACe,WAAW,GAAA;IACvB,OAAO,SAAS,GAAG,SAAS,GAAG,IAAI,CAAC;AACxC,CAAC;AAEK,SAAU,qBAAqB,CACjC,qBAAuF,EACvF,IAAY,EACZ,YAAmC,EAAE,EAAA;AAErC,IAAA,MAAM,IAAI,GAAG,CAAa,UAAA,EAAA,IAAI,EAAE,CAAC;AACjC,IAAA,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;AACxC,IAAA,OAAO,CAAC,cAAA,GAAwC,EAAE,KAAI;AAClD,QAAA,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;QAC7B,IAAI,CAAC,QAAQ,EAAE;AACX,YAAA,IAAI,qBAAqB,EAAE;gBACvB,qBAAqB,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACvG,aAAA;AAAM,iBAAA;gBACH,MAAM,iBAAiB,GAA0B,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AAC9H,gBAAA,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,iBAAiB,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;AAC7E,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,cAAc,CAAC,MAAM,CAAC,CAAC;AAClC,KAAC,CAAC;AACN,CAAC;AAEK,SAAU,cAAc,CAAC,aAAkB,EAAA;AAC7C,IAAA,MAAM,QAAQ,GAAG,WAAW,EAAE,CAAC;IAC/B,IAAI,CAAC,QAAQ,EAAE;QACX,MAAM,YAAY,GAAG,qBAAqB,CAAC;AAC3C,QAAA,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AACjC,KAAA;IACD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE;AAC7C,QAAA,MAAM,IAAI,KAAK,CAAC,sFAAsF,CAAC,CAAC;AAC3G,KAAA;AACD,IAAA,OAAO,QAAQ,CAAC;AACpB,CAAC;MAEY,YAAY,GAAG,qBAAqB,CAAC,IAAI,EAAE,MAAM,EAAE;AAC5D,IAAA;AACI,QAAA,OAAO,EAAE,YAAY;AACrB,QAAA,UAAU,EAAE,MAAM,IAAI,YAAY,EAAE;AACvC,KAAA;AACD,IAAA;AACI,QAAA,OAAO,EAAE,WAAW;QACpB,UAAU,EAAE,CAAC,QAAkB,KAAK,IAAI,WAAW,CAAC,QAAQ,CAAC;QAC7D,IAAI,EAAE,CAAC,QAAQ,CAAC;AACnB,KAAA;AACD,IAAA,GAAG,uBAAuB;AAC7B,CAAA;;SCzEe,KAAK,GAAA;AACjB,IAAA,IAAI,OAA6B,CAAC;AAClC,IAAA,IAAI,MAA+B,CAAC;IACpC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAI,CAAC,QAAQ,EAAE,OAAO,KAAI;QACjD,OAAO,GAAG,QAAe,CAAC;QAC1B,MAAM,GAAG,OAAO,CAAC;AACrB,KAAC,CAAa,CAAC;AACf,IAAA,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1B,IAAA,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AACxB,IAAA,OAAO,OAAO,CAAC;AACnB,CAAC;AAEM,gBAAgB,0BAA0B,CAAI,UAAyB,EAAA;AAC1E,IAAA,IAAI,QAAQ,GAAa,KAAK,EAAE,CAAC;AACjC,IAAA,MAAM,GAAG,GAAG,UAAU,CAAC,SAAS,CAAC;AAC7B,QAAA,IAAI,CAAC,IAAI,EAAA;YACL,MAAM,CAAC,GAAG,QAAQ,CAAC;YACnB,QAAQ,GAAG,KAAK,EAAE,CAAC;AACnB,YAAA,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACnB;AACD,QAAA,KAAK,CAAC,GAAG,EAAA;AACL,YAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACxB;QACD,QAAQ,GAAA;YACJ,MAAM,CAAC,GAAG,QAAQ,CAAC;YACnB,QAAQ,GAAG,IAAW,CAAC;YACvB,CAAC,CAAC,OAAO,EAAE,CAAC;SACf;AACJ,KAAA,CAAC,CAAC;IACH,IAAI;QACA,SAAU;AACN,YAAA,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC;AAC7B,YAAA,IAAI,CAAC,QAAQ;gBAAE,MAAM;AACrB,YAAA,MAAM,KAAK,CAAC;AACf,SAAA;AACJ,KAAA;AAAS,YAAA;QACN,GAAG,CAAC,WAAW,EAAE,CAAC;AACrB,KAAA;AACL;;MC3CsB,GAAG,CAAA;AAIxB;;MCJqB,MAAM,CAAA;AAE3B;;ACKK,MAAO,mBAAoB,SAAQ,SAAS,CAAA;AAC1B,IAAA,MAAA,CAAA;AAApB,IAAA,WAAA,CAAoB,MAAc,EAAA;QAC9B,KAAK,CAAC,CAAkB,gBAAA,CAAA,CAAC,CAAA;QADT,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;KAEjC;IACD,KAAK,CAAC,OAAe,EAAE,EAAA;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;KACtD;AACD,IAAA,MAAM,MAAM,CAAC,QAAkB,EAAE,IAAU,EAAA;QACvC,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAiC,QAAQ,CAAC,CAAA;AACrE,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC/B,MAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;AACrD,QAAA,MAAM,kBAAkB,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,EAAuB,KAAK,EAAE,CAAC,WAAW,KAAK,qBAAqB,CAAC,CAAA;AACrH,QAAA,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AACxB,QAAA,IAAI,kBAAkB,EAAE;YACpB,MAAM,GAAG,GAAG,CAAA,EAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,CAAC,CAAI,CAAA,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,CAAC,WAAW,CAAC,CAAA,CAAE,CAAA;YACvG,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,CAAE,CAAA;AAC/B,SAAA;QACD,MAAM,OAAO,GAAG,CAAG,EAAA,IAAI,CAAC,MAAM,CAAA,EAAG,IAAI,CAAA,CAAE,CAAC;QACxC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAC3D,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AAC7B,QAAA,GAAG,CAAC,IAAI,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;AAC7C,QAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC1B,gBAAA,OAAO,EAAE,OAAO;AAChB,gBAAA,UAAU,EAAE,CAAC,MAAM,KAAI;oBACnB,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACxC,oBAAA,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;oBACzD,MAAM,OAAO,GAAG,OAAO,GAAQ,EAAE,IAAU,KAAI;AAC3C,wBAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC9B,4BAAA,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE;AAC3C,4BAAA,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;AACzC,4BAAA,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE;AACnC,4BAAA,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;yBACpC,EAAE,SAAS,CAAC,CAAC;AACd,wBAAA,MAAM,UAAU,GAAG,MAAM,gBAAgB,CAAC,SAAS,CAAC,CAAC;wBACrD,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,UAAU,CAAC,CAAA;AAC/C,qBAAC,CAAA;AACD,oBAAA,OAAO,OAAO,CAAC;iBAClB;aACJ,CAAC,EAAE,MAAM,CAAC,CAAC;QACZ,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC7C,QAAQ,CAAC,GAAG,CAAC,CAAC;AACV,gBAAA,OAAO,EAAE,cAAc;AACvB,gBAAA,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE;AAC1C,gBAAA,KAAK,EAAE,IAAI;aACd,CAAC,EAAE,MAAM,CAAC,CAAA;AACX,QAAA,IAAI,IAAI;YAAE,MAAM,IAAI,EAAE,CAAA;KACzB;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}