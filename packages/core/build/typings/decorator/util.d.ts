/**
 * database decorator
 */
export declare function createClassDecorator<T>(metadataKey: string): (options?: T) => (target: any) => void;
export declare function createIdClassDecorator<T>(metadataKey: string): (id: string, options?: T) => (target: any) => void;
export declare function createPropertyDecorator<T>(metadataKey: string): (options?: T) => PropertyDecorator;
export declare function createMethodDecorator<T>(metadataKey: string): (options?: T) => MethodDecorator;
export declare function createParameterDecorator<T>(metadataKey: string): (options?: T) => ParameterDecorator;
