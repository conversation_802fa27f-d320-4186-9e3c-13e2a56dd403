import { Injector } from "./injector";
import { Next } from "./token";
export declare abstract class Middleware {
    name: string;
    version: string;
    status: number;
    constructor(name: string);
    abstract handle(injector: Injector, next?: Next): Promise<any> | any;
}
export declare abstract class Plugin extends Middleware {
}
export declare abstract class Decorator extends Middleware {
}
export declare abstract class PlatformInit extends Middleware {
}
export declare abstract class AppInit extends Middleware {
}
export declare abstract class LoadAddon extends Middleware {
}
export declare abstract class AppStart extends Middleware {
}
