import { IMetadata, IMetadataClass, IMetadataMethodOrProperty, InjectionToken, Type } from "./types";
export interface Next {
    (): Promise<void> | void;
}
export declare const NEXT: InjectionToken<() => void>;
export declare const CONTEXT: InjectionToken<any>;
export declare const INSTANCE: InjectionToken<any>;
export declare const METADATA: InjectionToken<IMetadata<any>>;
export interface Decorators {
    classes: Set<IMetadataClass<any>>;
    properties: IMetadataMethodOrProperty<any>[];
}
export declare const METADATAS: InjectionToken<Decorators>;
export declare const CLASS_METADATAS: InjectionToken<IMetadataClass<any>[]>;
export declare const METADATA_PARAMETERS: InjectionToken<Function>;
export declare const CURRENT_TYPE: InjectionToken<Type<any>>;
import { MatchResult } from 'path-to-regexp';
export declare const MATCH_RESULT: InjectionToken<MatchResult<object>>;
export declare const DESIGN_TYPE: InjectionToken<any>;
export declare const DESIGN_PARAM_TYPES: InjectionToken<any[]>;
export declare const DESIGN_RETURN_TYPE: InjectionToken<any>;
export declare const ARGS: InjectionToken<any>;
export declare const APP_ROOT: InjectionToken<string>;
export declare const ADDON_NAME: InjectionToken<string>;
export declare const ADDON_ROOT: InjectionToken<string>;
export declare type PlatformModule = Type<any> | Promise<Type<any> | Type<any>[] | Promise<Type<any>[]>>;
export declare const PLATFORM_MODULE: InjectionToken<PlatformModule>;
export declare const CLOUD_URL: InjectionToken<string>;
export declare const UNINSTALL_HOOK: InjectionToken<{
    type: Type<any>;
    removes: Function[];
}>;
export declare const NG_MODULE: InjectionToken<Type<any>>;
export declare const STATE: InjectionToken<any>;
