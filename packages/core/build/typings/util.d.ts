import { ModuleDef } from "./decorator";
import { Injector, ProvideIn } from "./injector";
import { StaticProvider } from "./provider";
import { IMetadataMethodOrProperty, Type } from "./types";
export declare function toError(msg: string): {
    msg: string;
    data: any;
};
export declare function toSuccess<T>(data: T): {
    msg: string;
    data: T;
};
export declare function toJson<T>(msg: string, data: T): {
    msg: string;
    data: T;
};
export interface Payload<T> {
    action: string;
    data: T;
}
export declare function toPayload<T>(action: string, data: T): Payload<T>;
export declare function handlerMetadataMethodOrProperty(injector: Injector, metadata: IMetadataMethodOrProperty<any>): Promise<void>;
export declare function getPlatformModule(injector: Injector): Promise<Type<any>[]>;
export declare function processRootModule(type: Type<any>, root: Injector): Promise<Injector>;
export declare function processModule(type: Type<any>, root: Injector, name?: ProvideIn, modules?: Type<any>[]): Promise<Injector>;
export declare function createModuleProviders(def: ModuleDef, injector: Injector): Promise<StaticProvider<any>[]>;
export declare function getTypeDef(type: Type<any>): any;
export declare function isNgModuleType(type: Type<any>): boolean;
export declare function handlerType(injector: Injector, type: Type<any>): Promise<Injector>;
