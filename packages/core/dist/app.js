"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.App = void 0;
const injector_1 = require("./injector");
const middleware_1 = require("./middleware");
const multi_middleware_1 = require("./multi-middleware");
class App {
    plugins = [];
    middlewares = [];
    injector;
    constructor(injector) {
        this.injector = injector || injector_1.Injector.create([], undefined, 'root');
        this.injector.use({ provide: App, useValue: this }, 'root');
    }
    async bootstrap() {
        /**
         * plugin run
         */
        this.plugins = this.injector.getMulti(middleware_1.Plugin);
        await new multi_middleware_1.MultiMiddleware(this.plugins).handle(this.injector);
        /**
         * platform init run
         */
        const platformInits = this.injector.getMulti(middleware_1.PlatformInit);
        await new multi_middleware_1.MultiMiddleware(platformInits).handle(this.injector);
        /**
         * app init run
         */
        const appInits = this.injector.getMulti(middleware_1.AppInit);
        await new multi_middleware_1.MultiMiddleware(appInits).handle(this.injector);
        /**
         * after platform and app init , load cloud app
         */
        const loadAddons = this.injector.getMulti(middleware_1.LoadAddon);
        await new multi_middleware_1.MultiMiddleware(loadAddons).handle(this.injector);
        const appStarts = this.injector.getMulti(middleware_1.AppStart);
        await new multi_middleware_1.MultiMiddleware(appStarts).handle(this.injector);
        return this.injector;
    }
}
exports.App = App;
