"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.compose = exports.composeAsync = void 0;
const token_1 = require("./token");
function composeAsync(middleware) {
    return (injector, next) => {
        let index = -1;
        return dispatch(0);
        function dispatch(i) {
            if (i <= index)
                throw new Error('next() called multiple times');
            index = i;
            let fn = middleware[i];
            if (i === middleware.length)
                fn = next;
            if (!fn)
                return;
            try {
                const next = dispatch.bind(null, i + 1);
                const nextInjector = injector.create([{ provide: token_1.NEXT, useValue: next }], `next`);
                return fn(nextInjector, dispatch.bind(null, i + 1));
            }
            catch (err) {
                throw err;
            }
        }
    };
}
exports.composeAsync = composeAsync;
function compose(middleware) {
    return (injector, next) => {
        let index = -1;
        return dispatch(0);
        function dispatch(i) {
            if (i <= index)
                return Promise.reject(new Error('next() called multiple times'));
            index = i;
            let fn = middleware[i];
            if (i === middleware.length) {
                fn = next;
            }
            if (!fn)
                return Promise.resolve();
            try {
                const next = dispatch.bind(null, i + 1);
                const nextInjector = injector.create([{ provide: token_1.NEXT, useValue: next }], `next`);
                return Promise.resolve(fn(nextInjector, dispatch.bind(null, i + 1)));
            }
            catch (err) {
                return Promise.reject(err);
            }
        }
    };
}
exports.compose = compose;
