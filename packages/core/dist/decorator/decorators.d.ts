import { Decorator } from "../middleware";
import { StaticProvider } from "../provider";
import { StringToken, Type, Token } from "../types";
/**
 * injectable
 */
export declare const InjectableMetadataKey: StringToken<Decorator>;
export interface InjectableDef {
    providedIn?: Type<any> | 'root' | 'platform' | 'any' | null;
}
export declare const Injectable: (options?: InjectableDef) => (target: any) => void;
/**
 * controller
 */
export declare const ControllerMetadataKey: StringToken<Decorator>;
export declare const Controller: (options?: string) => (target: any) => void;
/**
 * page
 */
export declare const PageMetadataKey: StringToken<Decorator>;
export interface PageDef {
    path: string;
    templateUrl: string;
    styleUrls?: string[];
}
export declare const Page: (options?: PageDef) => (target: any) => void;
/**
 * module
 */
export interface ModuleWidthProvider<T> {
    module: Type<T>;
    providers: StaticProvider<any>[];
}
export interface ModuleStatic {
    main: string;
    exportName: string;
}
export declare type ModuleImport = Type<any> | ModuleWidthProvider<any> | ModuleStatic;
export declare function isModuleWidthProvider<T>(val: any): val is ModuleWidthProvider<T>;
export interface ModuleDef {
    id?: string;
    imports?: ModuleImport[];
    providers?: StaticProvider<any>[];
    controllers?: Type<any>[];
    dev?: boolean;
}
export declare const ModuleMetadataKey: StringToken<Decorator>;
export declare const Module: (options?: ModuleDef) => (target: any) => void;
export declare const InjectMetadataKey: StringToken<Decorator>;
export declare const Inject: (options?: Token<any>) => ParameterDecorator;
export declare const ParamsMetadataKey: StringToken<Decorator>;
export declare const Params: (options?: string) => ParameterDecorator;
export declare const StateMetadatakey: StringToken<Decorator>;
export declare const State: (options?: string) => ParameterDecorator;
export declare function createHttpMethodDecorator(key: string, method: string): (path: string) => MethodDecorator;
export declare const GetMetadataKey: StringToken<Decorator>;
export declare const Get: (path: string) => MethodDecorator;
