"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Get = exports.GetMetadataKey = exports.createHttpMethodDecorator = exports.State = exports.StateMetadatakey = exports.Params = exports.ParamsMetadataKey = exports.Inject = exports.InjectMetadataKey = exports.Module = exports.ModuleMetadataKey = exports.isModuleWidthProvider = exports.Page = exports.PageMetadataKey = exports.Controller = exports.ControllerMetadataKey = exports.Injectable = exports.InjectableMetadataKey = void 0;
const util_1 = require("./util");
/**
 * injectable
 */
exports.InjectableMetadataKey = `@nger/core injectable metadata key`;
exports.Injectable = (0, util_1.createClassDecorator)(exports.InjectableMetadataKey);
/**
 * controller
 */
exports.ControllerMetadataKey = `@nger/core controller metadata key`;
exports.Controller = (0, util_1.createClassDecorator)(exports.ControllerMetadataKey);
/**
 * page
 */
exports.PageMetadataKey = `@nger/core page metadata key`;
exports.Page = (0, util_1.createClassDecorator)(exports.PageMetadataKey);
function isModuleWidthProvider(val) {
    return val && Reflect.has(val, 'module') && Reflect.has(val, 'providers');
}
exports.isModuleWidthProvider = isModuleWidthProvider;
exports.ModuleMetadataKey = `@nger/core module metadata key`;
exports.Module = (0, util_1.createClassDecorator)(exports.ModuleMetadataKey);
exports.InjectMetadataKey = `@nger/core inject metadata key`;
exports.Inject = (0, util_1.createParameterDecorator)(exports.InjectMetadataKey);
exports.ParamsMetadataKey = `@nger/core params metadata key`;
exports.Params = (0, util_1.createParameterDecorator)(exports.ParamsMetadataKey);
exports.StateMetadatakey = `@nger/core state metadata key`;
exports.State = (0, util_1.createParameterDecorator)(exports.StateMetadatakey);
function createHttpMethodDecorator(key, method) {
    const decorator = (0, util_1.createMethodDecorator)(key);
    return (path) => decorator({ method, path });
}
exports.createHttpMethodDecorator = createHttpMethodDecorator;
exports.GetMetadataKey = `@nger/http get metadata key`;
exports.Get = createHttpMethodDecorator(exports.GetMetadataKey, 'get');
