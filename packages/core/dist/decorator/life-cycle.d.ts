import { Injector } from "../injector";
export interface OnInstall {
    onInstall(): Promise<void>;
}
export interface OnUnInstall {
}
export interface OnUpgrade {
}
export interface OnWatch {
}
export interface OnInit {
    onInit(injector: Injector): Promise<void> | void;
}
export declare function isOnInit(val: any): val is OnInit;
export interface OnDestory {
}
export interface OnBootstrap {
    doBootstrap(injector: Injector): Promise<void> | void;
}
export declare function isDoBootstrap(val: any): val is OnBootstrap;
export interface DoRegister {
}
