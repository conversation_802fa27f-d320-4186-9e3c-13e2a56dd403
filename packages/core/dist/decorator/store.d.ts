import { IMetadataClass, IMetadataMethodOrProperty, IMetadataParameter, Type } from "../types";
declare class DecoratorStorage<K, V extends {
    metadataKey: string;
    id: string;
    options?: any;
}> {
    private _map;
    forEach(callbackfn: (value: Set<V>, key: K, map: Map<K, Set<V>>) => void, thisArg?: any): void;
    toKeys(): K[];
    get(key: K): Set<V>;
    setV(key: K, val: V): this;
    getMetadata<T extends V>(key: K, metadataKey: string): T;
    getDecorators(key: Type<any>): {
        classes: IMetadataClass<any>[];
        properties: IMetadataMethodOrProperty<any>[];
        parameters: IMetadataParameter<any>[];
    };
}
export declare const store: DecoratorStorage<unknown, {
    metadataKey: string;
    id: string;
    options?: any;
}>;
export {};
