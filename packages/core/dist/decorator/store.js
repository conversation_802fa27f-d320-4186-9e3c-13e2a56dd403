"use strict";
/*
 * @Author: imeepos
 * @Date: 2022-04-12 07:46:41
 * @LastEditors: imeepos
 * @LastEditTime: 2022-04-12 13:36:19
 * @Description:
 * email:<EMAIL> mobile:18639118753 wechat:meepo_brothet realname:yang ming ming
 * Copyright (c) 2022 by imeepos/guanjieinc.com, All Rights Reserved.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.store = void 0;
const types_1 = require("../types");
class DecoratorStorage {
    _map = new Map();
    forEach(callbackfn, thisArg) {
        this._map.forEach(callbackfn, thisArg);
    }
    toKeys() {
        const result = [];
        this._map.forEach((v, k) => result.push(k));
        return result;
    }
    get(key) {
        if (this._map.has(key)) {
            return this._map.get(key);
        }
        const val = new Set();
        this._map.set(key, val);
        return val;
    }
    setV(key, val) {
        const vals = this.get(key);
        if (vals.has(val)) {
            return this;
        }
        else {
            vals.add(val);
            return this;
        }
    }
    getMetadata(key, metadataKey) {
        const set = this.get(key);
        return [...set].find(it => it.metadataKey === metadataKey);
    }
    getDecorators(key) {
        const decorators = this.get(key);
        const propertiesAndParameters = this.get(key.prototype);
        const children = [];
        const parameters = [...decorators].filter(it => (0, types_1.isIMetadataParameter)(it));
        const classes = [...decorators].filter(it => !(0, types_1.isIMetadataParameter)(it));
        propertiesAndParameters.forEach((val) => {
            if ((0, types_1.isIMetadataMethodOrProperty)(val)) {
                val.parameters = [...propertiesAndParameters].filter(it => {
                    if ((0, types_1.isIMetadataParameter)(it)) {
                        return it.propertyKey == val.propertyKey;
                    }
                    return false;
                });
                children.push(val);
            }
        });
        return {
            classes: classes,
            properties: children,
            parameters
        };
    }
}
exports.store = new DecoratorStorage();
