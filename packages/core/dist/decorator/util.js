"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createParameterDecorator = exports.createMethodDecorator = exports.createPropertyDecorator = exports.createIdClassDecorator = exports.createClassDecorator = void 0;
const store_1 = require("./store");
/**
 * database decorator
 */
function createClassDecorator(metadataKey) {
    return (options) => {
        return (target) => {
            store_1.store.setV(target, { metadataKey, options });
        };
    };
}
exports.createClassDecorator = createClassDecorator;
function createIdClassDecorator(metadataKey) {
    return (id, options) => {
        return (target) => {
            store_1.store.setV(target, { metadataKey, id, options });
        };
    };
}
exports.createIdClassDecorator = createIdClassDecorator;
function createPropertyDecorator(metadataKey, config) {
    return (options) => {
        return (target, propertyKey, descriptor) => {
            // insert class_decorator values(key,id)
            if (config && descriptor) {
                const { enumerable, configurable, writable } = config;
                if (typeof enumerable !== 'undefined') {
                    descriptor.enumerable = !!enumerable;
                }
                if (typeof configurable !== 'undefined') {
                    descriptor.configurable = !!configurable;
                }
                if (typeof writable !== 'undefined') {
                    descriptor.writable = !!writable;
                }
            }
            store_1.store.setV(target, { metadataKey, propertyKey, descriptor, options });
            return descriptor;
        };
    };
}
exports.createPropertyDecorator = createPropertyDecorator;
function createMethodDecorator(metadataKey) {
    return (options) => {
        return (target, propertyKey, descriptor) => {
            store_1.store.setV(target, { metadataKey, id: propertyKey, propertyKey, options });
            return descriptor;
        };
    };
}
exports.createMethodDecorator = createMethodDecorator;
function createParameterDecorator(metadataKey) {
    return (options) => {
        return (target, propertyKey, parameterIndex) => {
            // insert class_decorator values(key,id)
            store_1.store.setV(target, { metadataKey, propertyKey, parameterIndex, options });
        };
    };
}
exports.createParameterDecorator = createParameterDecorator;
