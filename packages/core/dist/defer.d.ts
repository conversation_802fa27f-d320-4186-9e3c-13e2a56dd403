import { Observable } from 'rxjs';
export interface Defer<T> extends Promise<T> {
    resolve: (value?: T) => void;
    reject: (reason?: any) => void;
}
export declare function defer<T>(): Defer<T>;
export interface NgerObservable<T> extends Observable<T> {
    next: (value?: T) => void;
    error: (err?: any) => void;
    complete: () => void;
}
export declare function observable<T>(): NgerObservable<T>;
export declare function observableToAsyncGenerator<T>(observable: Observable<T>): AsyncGenerator<Awaited<T>, void, unknown>;
