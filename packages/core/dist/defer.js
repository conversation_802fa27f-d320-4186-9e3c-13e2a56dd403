"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.observableToAsyncGenerator = exports.observable = exports.defer = void 0;
const rxjs_1 = require("rxjs");
function defer() {
    let resolve;
    let reject;
    const promise = new Promise((_resolve, _reject) => {
        resolve = _resolve;
        reject = _reject;
    });
    promise.resolve = resolve;
    promise.reject = reject;
    return promise;
}
exports.defer = defer;
function observable() {
    let next;
    let error;
    let complete;
    const obser = new rxjs_1.Observable((sub) => {
        next = sub.next.bind(sub);
        error = sub.error.bind(sub);
        complete = sub.complete.bind(sub);
        obser.next = next;
        obser.error = error;
        obser.complete = complete;
    });
    return obser;
}
exports.observable = observable;
async function* observableToAsyncGenerator(observable) {
    let nextData = defer();
    const sub = observable.subscribe({
        next(data) {
            const n = nextData;
            nextData = defer();
            n.resolve(data);
        },
        error(err) {
            nextData.reject(err);
        },
        complete() {
            const n = nextData;
            nextData = null;
            n.resolve();
        }
    });
    try {
        for (;;) {
            const value = await nextData;
            if (!nextData)
                break;
            yield value;
        }
    }
    finally {
        sub.unsubscribe();
    }
}
exports.observableToAsyncGenerator = observableToAsyncGenerator;
