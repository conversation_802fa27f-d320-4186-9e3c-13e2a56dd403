import { StaticProvider } from "./provider";
import { InjectionToken, Token, Type } from "./types";
export declare function isRegExp(val: any): val is RegExp;
export declare function objectToString(o: any): string;
export declare function isObject(val: any): val is object;
export declare function isDate(d: any): d is Date;
export declare function isPrimitive(arg: any): boolean;
export declare function pad(n: number): string;
export declare function isString(val: any): val is string;
export declare type ProvideIn = Type<any> | 'root' | 'platform' | 'any' | string | symbol | null | undefined;
export declare const INJECTOR_GET: InjectionToken<(<T>(token: Token<T>, notfound?: unknown, from?: ProvideIn, to?: ProvideIn) => T)>;
export declare abstract class Injector {
    name: ProvideIn;
    abstract delete(filter: (token: Token<any>, record: Record<any>) => boolean): void;
    abstract deleteMulti(filter: (token: Token<any>, record: Record<any>) => boolean, from: ProvideIn): void;
    abstract get<T>(token: Token<T>, notfound?: unknown, from?: ProvideIn, to?: ProvideIn): T;
    abstract getMulti<T>(token: Token<T>, from?: ProvideIn, to?: ProvideIn): T[];
    abstract use(providers: StaticProvider<any>[] | StaticProvider<any>, name?: ProvideIn): Function[];
    abstract create(providers: StaticProvider<any>[], name?: ProvideIn): Injector;
    abstract getString(to: string): Record<any>[];
    static create(providers: StaticProvider<any>[], parent?: Injector, name?: ProvideIn): NgerInjector;
}
export interface Record<T> {
    token: Token<T>;
    factory: (...args: any[]) => T;
    value: T | undefined;
    title?: string;
}
export declare function getInjector(): Injector;
export declare const ngerInjectorStore: Map<ProvideIn, Injector>;
export declare const ngerInjectors: Injector[];
export declare class NgerInjector extends Injector {
    private map;
    private multiMap;
    parent: Injector | undefined;
    removes: Function[];
    constructor(providers: StaticProvider<any>[], parent?: Injector, name?: ProvideIn);
    getString(to: string): Record<any>[];
    use(providers: StaticProvider<any>[] | StaticProvider<any>, name?: ProvideIn): Function[];
    create(providers: StaticProvider<any>[], name?: ProvideIn): Injector;
    delete(filter: (token: Token<any>, record: Record<any>) => boolean): void;
    deleteMulti(filter: (token: Token<any>, record: Record<any>) => boolean, from: ProvideIn): void;
    tryGetMulti<T>(token: Token<T>, to?: ProvideIn): T[];
    getMulti<T>(token: Token<T>, from?: ProvideIn, to?: ProvideIn): T[];
    getThis(token: Token<any>): any;
    tryGet<T>(token: Token<T>, notfound?: unknown, to?: ProvideIn): any;
    _get: <T>(token: Token<T>, notfound?: unknown, from?: ProvideIn, to?: ProvideIn) => T;
    get<T>(token: Token<T>, notfound?: unknown, from?: ProvideIn, to?: ProvideIn): T;
    private processProviderAndType;
    private typeToProvider;
    private processProvider;
    private providerToRecord;
    private useFactoryProviderToRecord;
    private useClassProviderToRecord;
    private useValueToRecord;
}
