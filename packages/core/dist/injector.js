"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NgerInjector = exports.ngerInjectors = exports.ngerInjectorStore = exports.getInjector = exports.Injector = exports.INJECTOR_GET = exports.isString = exports.pad = exports.isPrimitive = exports.isDate = exports.isObject = exports.objectToString = exports.isRegExp = void 0;
const provider_1 = require("./provider");
const types_1 = require("./types");
function isRegExp(val) {
    return isObject(val) && objectToString(val) === '[object RegExp]';
}
exports.isRegExp = isRegExp;
function objectToString(o) {
    return Object.prototype.toString.call(o);
}
exports.objectToString = objectToString;
function isObject(val) {
    return typeof val === 'object' && val !== null;
}
exports.isObject = isObject;
function isDate(d) {
    return isObject(d) && objectToString(d) === '[object Date]';
}
exports.isDate = isDate;
function isPrimitive(arg) {
    return arg === null ||
        typeof arg === 'boolean' ||
        typeof arg === 'number' ||
        typeof arg === 'string' ||
        typeof arg === 'symbol' || // ES6 symbol
        typeof arg === 'undefined';
}
exports.isPrimitive = isPrimitive;
function pad(n) {
    return n < 10 ? '0' + n.toString(10) : n.toString(10);
}
exports.pad = pad;
function isString(val) {
    return typeof val === 'string';
}
exports.isString = isString;
exports.INJECTOR_GET = new types_1.InjectionToken(`@nger/core not found`);
class Injector {
    name = null;
    static create(providers, parent, name) {
        return new NgerInjector(providers, parent, name);
    }
}
exports.Injector = Injector;
const path_to_regexp_1 = require("path-to-regexp");
const metadata_1 = require("./metadata");
let _current;
let _preInjector;
function setCurrentInjector(injector) {
    _preInjector = _current;
    _current = injector;
}
function getInjector() {
    return _current;
}
exports.getInjector = getInjector;
function reSetInjector() {
    _current = _preInjector;
}
exports.ngerInjectorStore = new Map();
exports.ngerInjectors = [];
class NgerInjector extends Injector {
    map = new Map();
    multiMap = new Map();
    parent;
    removes = [];
    constructor(providers, parent, name) {
        super();
        this.parent = parent;
        this.removes = this.use([...providers, { provide: Injector, useValue: this }]);
        this.name = name;
        exports.ngerInjectorStore.set(name, this);
        exports.ngerInjectors.push(this);
    }
    getString(to) {
        const records = [];
        if (this.name === to) {
            this.map.forEach((val, key) => {
                if (typeof key === 'string') {
                    records.push(val);
                }
            });
            return records;
        }
        if (this.parent) {
            return this.parent.getString(to);
        }
        return records;
    }
    use(providers, name) {
        const removes = [];
        if (name) {
            if (this.name === name) {
                if (Array.isArray(providers)) {
                    removes.push(...providers.map(provider => this.processProviderAndType(provider)).flat());
                }
                else {
                    removes.push(...this.processProviderAndType(providers));
                }
            }
            if (this.parent) {
                removes.push(...this.parent.use(providers, name));
            }
        }
        else {
            if (Array.isArray(providers)) {
                removes.push(...providers.map(provider => this.processProviderAndType(provider)).flat());
            }
            else {
                removes.push(...this.processProviderAndType(providers));
            }
        }
        if (name && name === this.name) {
            const get = this.get(exports.INJECTOR_GET);
            if (get && typeof get === 'function') {
                const old = this.get.bind(this);
                this._get = old;
                this.get = get.bind(this);
            }
        }
        return removes;
    }
    create(providers, name) {
        return new NgerInjector(providers, this, name);
    }
    delete(filter) {
        const items = [];
        this.map.forEach((it, key) => {
            if (filter(key, it)) {
                items.push(key);
            }
        });
        items.map((key) => {
            this.map.delete(key);
        });
    }
    deleteMulti(filter, from) {
        if (from) {
            if (from !== this.name) {
                return this.parent.deleteMulti(filter, from);
            }
        }
        this.multiMap.forEach((it, key) => {
            const removes = [];
            it.forEach(item => {
                if (filter(key, item)) {
                    removes.push(item);
                }
            });
            removes.map(remove => it.delete(remove));
        });
    }
    tryGetMulti(token, to) {
        try {
            setCurrentInjector(this);
            let values = [];
            const set = this.multiMap.get(token);
            if (set) {
                [...set].map((record) => {
                    if (typeof record.value === 'undefined') {
                        record.value = record.factory();
                    }
                    values.push(record.value);
                });
                if (this.name && this.name === to) {
                    return values;
                }
                if (this.parent) {
                    const parentValue = this.parent.getMulti(token, to);
                    if (Array.isArray(parentValue)) {
                        values.push(...parentValue);
                    }
                }
                return values;
            }
            if (this.name && this.name === to) {
                return values;
            }
            if (this.parent) {
                return this.parent.getMulti(token, to);
            }
            return values;
        }
        finally {
            reSetInjector();
        }
    }
    getMulti(token, from, to) {
        if (from) {
            if (this.name === from) {
                return this.tryGetMulti(token, to);
            }
            else if (this.parent) {
                return this.parent.getMulti(token, from, to);
            }
            else {
                throw new Error(`can not found injector: ${from.toString()}`);
            }
        }
        return this.tryGetMulti(token, to);
    }
    getThis(token) {
        const record = this.map.get(token);
        if (record) {
            if (typeof record.value === 'undefined') {
                record.value = record.factory();
            }
            return record.value;
        }
    }
    tryGet(token, notfound, to) {
        if (typeof token === 'undefined' || token === null) {
            debugger;
            return;
        }
        try {
            setCurrentInjector(this);
            const record = this.map.get(token);
            if (record) {
                if (typeof record.value === 'undefined') {
                    record.value = record.factory();
                }
                return record.value;
            }
            if (typeof token === 'string') {
                const keys = [...this.map.keys()];
                let result;
                const _token = keys.find(key => {
                    if (isRegExp(key) || isString(key)) {
                        const _match = (0, path_to_regexp_1.match)(key, { decode: decodeURIComponent });
                        const _result = _match(token);
                        if (_result) {
                            result = _result;
                            return true;
                        }
                        return false;
                    }
                });
                const record = this.map.get(_token);
                if (record) {
                    return record.factory(result);
                }
            }
            if (to) {
                if (this.name === to) {
                    return notfound;
                }
            }
            if (this.parent) {
                return this.parent.get(token, notfound);
            }
            return notfound;
        }
        finally {
            reSetInjector();
        }
    }
    _get;
    get(token, notfound, from, to) {
        if (from) {
            if (this.name === from) {
                return this.tryGet(token, notfound);
            }
            else if (this.parent) {
                return this.parent.get(token, notfound, from);
            }
            else {
                throw new Error(`can not found injector: ${from.toString()}`);
            }
        }
        return this.tryGet(token, notfound, to);
    }
    processProviderAndType(provider) {
        if ((0, provider_1.isTypeProvider)(provider)) {
            // TODO: type provider
            const toProvider = this.typeToProvider(provider);
            return this.processProvider(toProvider);
        }
        else {
            return this.processProvider(provider);
        }
    }
    typeToProvider(type) {
        return {
            provide: type,
            useClass: type,
            deps: (0, metadata_1.getDesignParamTypes)(type)
        };
    }
    processProvider(provider) {
        const removes = [];
        const record = this.providerToRecord(provider);
        if (!!provider.multi) {
            // multi
            if (this.multiMap.has(provider.provide)) {
                const set = this.multiMap.get(provider.provide);
                removes.push(() => set.has(record) && set.delete(record));
                set.add(record);
            }
            else {
                const set = new Set([record]);
                this.multiMap.set(provider.provide, set);
                removes.push(() => this.multiMap.has(record) && this.multiMap.delete(provider.provide));
            }
        }
        else {
            this.map.set(record.token, record);
            removes.push(() => this.map.has(record.token) && this.map.delete(record.token));
        }
        return removes;
    }
    providerToRecord(provider) {
        if ((0, provider_1.isUseValueProvider)(provider)) {
            return this.useValueToRecord(provider);
        }
        else if ((0, provider_1.isUseClassProvider)(provider)) {
            return this.useClassProviderToRecord(provider);
        }
        else if ((0, provider_1.isUseFactoryProvider)(provider)) {
            return this.useFactoryProviderToRecord(provider);
        }
        else if ((0, provider_1.isUseExistProvider)(provider)) {
            if (provider.isClass) {
                return {
                    token: provider.provide,
                    factory: () => {
                        const that = this;
                        return function Existing() {
                            return that.get(provider.useExisting);
                        };
                    },
                    value: undefined
                };
            }
            return {
                token: provider.provide,
                factory: () => {
                    return this.get(provider.useExisting);
                },
                value: undefined
            };
        }
        else {
            return {
                token: provider.provide,
                factory: () => {
                    return {};
                },
                value: undefined
            };
        }
    }
    useFactoryProviderToRecord(provider) {
        return {
            token: provider.provide,
            factory: (...args) => {
                if (args.length > 0) {
                    return provider.useFactory(...args);
                }
                else {
                    const deps = provider.deps || [];
                    const _args = deps.map(dep => this.get(dep));
                    return provider.useFactory(..._args);
                }
            },
            value: undefined
        };
    }
    useClassProviderToRecord(provider) {
        return {
            token: provider.provide,
            factory: (...args) => {
                if (args.length > 0) {
                    return new provider.useClass(...args);
                }
                else {
                    const deps = provider.deps || [];
                    const _args = deps.map(dep => this.get(dep));
                    return new provider.useClass(..._args);
                }
            },
            value: undefined
        };
    }
    useValueToRecord(provider) {
        return {
            token: provider.provide,
            factory: () => {
                return provider.useValue;
            },
            value: undefined
        };
    }
}
exports.NgerInjector = NgerInjector;
