"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDesignReturnType = exports.getDesignParamTypes = exports.getDesignType = void 0;
require("reflect-metadata");
function getDesignType(target) {
    return Reflect.getMetadata("design:type", target);
}
exports.getDesignType = getDesignType;
function getDesignParamTypes(target) {
    return Reflect.getMetadata("design:paramtypes", target);
}
exports.getDesignParamTypes = getDesignParamTypes;
function getDesignReturnType(target) {
    return Reflect.getMetadata('design:returntype', target);
}
exports.getDesignReturnType = getDesignReturnType;
