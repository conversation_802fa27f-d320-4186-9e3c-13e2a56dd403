"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoreMethodDecorator = void 0;
const decorator_1 = require("./decorator");
const middleware_1 = require("./middleware");
const token_1 = require("./token");
class CoreMethodDecorator extends middleware_1.Decorator {
    method;
    constructor(method) {
        super(`method decorator`);
        this.method = method;
    }
    toUrl(path = '') {
        return path.split('/').filter(it => !!it).join('/');
    }
    async handle(injector, next) {
        const parent = injector.get(token_1.METADATA);
        const options = parent.options;
        const classMetadatas = injector.get(token_1.CLASS_METADATAS);
        const controllerMetadata = classMetadatas.find((it) => it.metadataKey === decorator_1.ControllerMetadataKey);
        let path = options.path;
        if (controllerMetadata) {
            const url = `${this.toUrl(controllerMetadata.options || '')}/${this.toUrl(path || parent.propertyKey)}`;
            path = `/${this.toUrl(url)}`;
        }
        const provide = `${this.method}${path}`;
        const parameterFactory = injector.get(token_1.METADATA_PARAMETERS);
        const removes = injector.use([{
                provide: provide,
                useFactory: (result) => {
                    const instance = injector.get(token_1.INSTANCE);
                    const method = Reflect.get(instance, parent.propertyKey);
                    const handler = async (ctx, next, hInjector) => {
                        const rootInjector = hInjector || injector;
                        const _injector = rootInjector.create([
                            { provide: token_1.MATCH_RESULT, useValue: result },
                            { provide: token_1.INSTANCE, useValue: instance },
                            { provide: token_1.CONTEXT, useValue: ctx },
                            { provide: token_1.NEXT, useValue: next }
                        ], 'handler');
                        const parameters = await parameterFactory(_injector);
                        return method.bind(instance)(...parameters);
                    };
                    return handler;
                }
            }], 'root');
        const currentModule = injector.get(token_1.NG_MODULE);
        injector.use([{
                provide: token_1.UNINSTALL_HOOK,
                useValue: { removes, type: currentModule },
                multi: true
            }], 'root');
        if (next)
            await next();
    }
}
exports.CoreMethodDecorator = CoreMethodDecorator;
