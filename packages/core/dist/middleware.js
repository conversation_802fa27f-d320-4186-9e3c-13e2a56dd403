"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppStart = exports.LoadAddon = exports.AppInit = exports.PlatformInit = exports.Decorator = exports.Plugin = exports.Middleware = void 0;
class Middleware {
    name;
    version = '0.0.0';
    status = 2;
    constructor(name) {
        this.name = name;
    }
}
exports.Middleware = Middleware;
class Plugin extends Middleware {
}
exports.Plugin = Plugin;
class Decorator extends Middleware {
}
exports.Decorator = Decorator;
class PlatformInit extends Middleware {
}
exports.PlatformInit = PlatformInit;
class AppInit extends Middleware {
}
exports.AppInit = AppInit;
class LoadAddon extends Middleware {
}
exports.LoadAddon = LoadAddon;
class AppStart extends Middleware {
}
exports.AppStart = AppStart;
