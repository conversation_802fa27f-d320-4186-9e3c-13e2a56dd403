"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MultiMiddleware = void 0;
const middleware_1 = require("./middleware");
const compose_1 = require("./compose");
class MultiMiddleware extends middleware_1.Middleware {
    middlewares;
    constructor(middlewares) {
        super('@nger/core/MiltiMiddleware');
        this.middlewares = middlewares;
    }
    async handle(injector, next) {
        const middlewares = [...this.middlewares || []].map(mid => {
            if (typeof mid === 'function') {
                return mid;
            }
            else {
                return mid.handle.bind(mid);
            }
        });
        return (0, compose_1.compose)(middlewares)(injector, next);
    }
}
exports.MultiMiddleware = MultiMiddleware;
