import { Injector } from "./injector";
import { Decorator } from "./middleware";
import { StaticProvider } from "./provider";
import { Next } from "./token";
export declare class InjectDecorator extends Decorator {
    constructor();
    handle(injector: Injector): unknown;
}
export declare class ParamsDecorator extends Decorator {
    constructor();
    handle(injector: Injector, next: Next): any;
}
export declare const paramDecoratorProviders: StaticProvider<any>[];
