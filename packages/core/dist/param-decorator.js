"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paramDecoratorProviders = exports.ParamsDecorator = exports.InjectDecorator = void 0;
const decorator_1 = require("./decorator");
const middleware_1 = require("./middleware");
const token_1 = require("./token");
class InjectDecorator extends middleware_1.Decorator {
    constructor() {
        super(decorator_1.ParamsMetadataKey);
    }
    handle(injector) {
        const metadata = injector.get(token_1.METADATA);
        if (metadata.options) {
            return injector.get(metadata.options);
        }
        return injector;
    }
}
exports.InjectDecorator = InjectDecorator;
class ParamsDecorator extends middleware_1.Decorator {
    constructor() {
        super(decorator_1.ParamsMetadataKey);
    }
    handle(injector, next) {
        const result = injector.get(token_1.MATCH_RESULT);
        const metadata = injector.get(token_1.METADATA);
        if (metadata.options) {
            return Reflect.get(result.params, metadata.options);
        }
        return { ...result.params };
    }
}
exports.ParamsDecorator = ParamsDecorator;
exports.paramDecoratorProviders = [{
        provide: decorator_1.ParamsMetadataKey,
        useFactory: () => new ParamsDecorator(),
        multi: true
    }, {
        provide: decorator_1.InjectMetadataKey,
        useFactory: () => new InjectDecorator(),
        multi: true
    }];
