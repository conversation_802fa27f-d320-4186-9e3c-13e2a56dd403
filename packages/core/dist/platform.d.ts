import { Injector } from "./injector";
import { StaticProvider } from "./provider";
import { Type } from "./types";
export declare class PlatformRef {
    injector: Injector;
    type: Type<any>;
    constructor(injector: Injector);
    bootstrap(type: Type<any>): Promise<Injector>;
    private reload;
}
export declare function createPlatform(injector: Injector): PlatformRef;
export declare function getPlatform(): PlatformRef | null;
export declare function createPlatformFactory(parentPlatformFactory: ((extraProviders?: StaticProvider<any>[]) => PlatformRef) | null, name: string, providers?: StaticProvider<any>[]): (extraProviders?: StaticProvider<any>[]) => PlatformRef;
export declare function assertPlatform(requiredToken: any): PlatformRef;
export declare const coreProviders: StaticProvider<any>[];
export declare const platformCore: (extraProviders?: StaticProvider<any>[]) => PlatformRef;
