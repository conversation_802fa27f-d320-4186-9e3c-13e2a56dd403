"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.platformCore = exports.coreProviders = exports.assertPlatform = exports.createPlatformFactory = exports.getPlatform = exports.createPlatform = exports.PlatformRef = void 0;
const app_1 = require("./app");
const error_handler_1 = require("./error-handler");
const injector_1 = require("./injector");
const param_decorator_1 = require("./param-decorator");
const token_1 = require("./token");
const types_1 = require("./types");
const util_1 = require("./util");
class PlatformRef {
    injector;
    type;
    constructor(injector) {
        this.injector = injector;
    }
    async bootstrap(type) {
        this.type = type;
        return this.reload();
    }
    async reload() {
        const appInjector = await (0, util_1.processRootModule)(this.type, this.injector);
        const app = new app_1.App(appInjector);
        await app.bootstrap();
        return appInjector;
    }
}
exports.PlatformRef = PlatformRef;
let _platform;
function createPlatform(injector) {
    _platform = injector.get(PlatformRef);
    return _platform;
}
exports.createPlatform = createPlatform;
function getPlatform() {
    return _platform ? _platform : null;
}
exports.getPlatform = getPlatform;
function createPlatformFactory(parentPlatformFactory, name, providers = []) {
    const desc = `Platform: ${name}`;
    const marker = new types_1.InjectionToken(desc);
    return (extraProviders = []) => {
        let platform = getPlatform();
        if (!platform) {
            if (parentPlatformFactory) {
                parentPlatformFactory(providers.concat(extraProviders).concat({ provide: marker, useValue: true }));
            }
            else {
                const injectedProviders = providers.concat(extraProviders).concat({ provide: marker, useValue: true });
                const topInjector = injector_1.Injector.create([], undefined, 'top');
                const databaseInjector = injector_1.Injector.create([], topInjector, 'db');
                const redisInjector = injector_1.Injector.create([], databaseInjector, 'redis');
                createPlatform(injector_1.Injector.create(injectedProviders, redisInjector, 'platform'));
            }
        }
        return assertPlatform(marker);
    };
}
exports.createPlatformFactory = createPlatformFactory;
function assertPlatform(requiredToken) {
    const platform = getPlatform();
    if (!platform) {
        const errorMessage = 'No platform exists!';
        throw new Error(errorMessage);
    }
    if (!platform.injector.get(requiredToken, null)) {
        throw new Error('A platform with a different configuration has been created. Please destroy it first.');
    }
    return platform;
}
exports.assertPlatform = assertPlatform;
exports.coreProviders = [
    {
        provide: error_handler_1.ErrorHandler,
        useFactory: () => new error_handler_1.ErrorHandler()
    },
    {
        provide: PlatformRef,
        useFactory: (injector) => new PlatformRef(injector),
        deps: [injector_1.Injector]
    },
    {
        provide: token_1.ADDON_ROOT,
        useFactory: (injector) => {
            const root = injector.get(token_1.APP_ROOT);
            return `${root}/addons`;
        },
        deps: [injector_1.Injector]
    },
    {
        provide: token_1.ATTACHMENT_ROOT,
        useFactory: (injector) => {
            const root = injector.get(token_1.APP_ROOT);
            return `${root}/attachments`;
        },
        deps: [injector_1.Injector]
    },
    {
        provide: token_1.CONFIG_ROOT,
        useFactory: (injector) => {
            const root = injector.get(token_1.APP_ROOT);
            return `${root}/config`;
        },
        deps: [injector_1.Injector]
    },
    ...param_decorator_1.paramDecoratorProviders
];
exports.platformCore = createPlatformFactory(null, 'core', exports.coreProviders);
