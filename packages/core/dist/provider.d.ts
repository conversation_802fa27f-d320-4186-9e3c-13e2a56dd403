import { Token, Type } from "./types";
export interface Provider<T> {
    provide: Token<T>;
    multi?: boolean;
}
export interface UseValueProvider<T> extends Provider<T> {
    useValue: T;
}
export declare function isUseValueProvider<T>(val: Provider<T>): val is UseValueProvider<T>;
export declare function useValue<T>(token: Token<T>, value: T, multi?: boolean): UseValueProvider<T>;
export interface UseClassProvider<T> extends Provider<T> {
    useClass: Type<T>;
    deps?: unknown[];
}
export declare function isUseClassProvider<T>(val: Provider<T>): val is UseClassProvider<T>;
export declare function useClass<T>(token: Token<T>, cls: Type<T>, deps?: any[], multi?: boolean): UseClassProvider<T>;
export declare function useMultiClass<T>(token: Token<T>, cls: Type<T>, deps?: any[]): UseClassProvider<T>;
export interface UseFactoryProvider<T> extends Provider<T> {
    useFactory: Factory<T>;
    deps?: unknown[];
}
export declare type Factory<T> = (...args: any[]) => T;
export declare function isUseFactoryProvider<T>(val: Provider<T>): val is UseFactoryProvider<T>;
export declare function useFactory<T>(token: Token<T>, factory: Factory<T>, deps?: any[], multi?: boolean): UseFactoryProvider<T>;
export declare function useMultiFactory<T>(token: Token<T>, factory: Factory<T>, deps?: any[]): UseFactoryProvider<T>;
export interface TypeProvider<T> extends Type<T> {
}
export declare function isTypeProvider<T>(val: any): val is TypeProvider<T>;
export interface UseExistProvider<T> extends Provider<T> {
    useExisting: Token<T>;
    isClass?: boolean;
}
export declare function isUseExistProvider<T>(val: any): val is UseExistProvider<T>;
export declare type StaticProvider<T> = UseExistProvider<T> | UseValueProvider<T> | UseClassProvider<T> | UseFactoryProvider<T> | TypeProvider<T>;
