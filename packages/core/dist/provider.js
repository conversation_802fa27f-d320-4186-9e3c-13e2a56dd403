"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isUseExistProvider = exports.isTypeProvider = exports.useMultiFactory = exports.useFactory = exports.isUseFactoryProvider = exports.useMultiClass = exports.useClass = exports.isUseClassProvider = exports.useValue = exports.isUseValueProvider = void 0;
function isUseValueProvider(val) {
    return val && Reflect.has(val, 'useValue');
}
exports.isUseValueProvider = isUseValueProvider;
function useValue(token, value, multi) {
    return {
        provide: token,
        useValue: value,
        multi
    };
}
exports.useValue = useValue;
function isUseClassProvider(val) {
    return val && Reflect.has(val, 'useClass');
}
exports.isUseClassProvider = isUseClassProvider;
function useClass(token, cls, deps = [], multi) {
    return {
        provide: token,
        useClass: cls,
        multi,
        deps
    };
}
exports.useClass = useClass;
function useMultiClass(token, cls, deps = []) {
    return useClass(token, cls, deps, true);
}
exports.useMultiClass = useMultiClass;
function isUseFactoryProvider(val) {
    return val && Reflect.has(val, 'useFactory');
}
exports.isUseFactoryProvider = isUseFactoryProvider;
function useFactory(token, factory, deps = [], multi) {
    return {
        provide: token,
        useFactory: factory,
        multi,
        deps: deps
    };
}
exports.useFactory = useFactory;
function useMultiFactory(token, factory, deps = []) {
    return useFactory(token, factory, deps, true);
}
exports.useMultiFactory = useMultiFactory;
function isTypeProvider(val) {
    return val && typeof val === 'function';
}
exports.isTypeProvider = isTypeProvider;
function isUseExistProvider(val) {
    return val && Reflect.has(val, 'useExisting');
}
exports.isUseExistProvider = isUseExistProvider;
