import { IMetadata, IMetadataClass, IMetadataMethodOrProperty, InjectionToken, Type } from "./types";
export interface Next {
    (): Promise<void> | void;
}
export declare const NEXT: InjectionToken<() => void>;
export declare const CONTEXT: InjectionToken<any>;
export declare const INSTANCE: InjectionToken<any>;
export declare const METADATA: InjectionToken<IMetadata<any>>;
export interface Decorators {
    classes: Set<IMetadataClass<any>>;
    properties: IMetadataMethodOrProperty<any>[];
}
export declare const METADATAS: InjectionToken<Decorators>;
export declare const CLASS_METADATAS: InjectionToken<IMetadataClass<any>[]>;
export declare const METADATA_PARAMETERS: InjectionToken<Function>;
export declare const CURRENT_TYPE: InjectionToken<Type<any>>;
import { MatchResult } from 'path-to-regexp';
export declare const MATCH_RESULT: InjectionToken<MatchResult<object>>;
export declare const DESIGN_TYPE: InjectionToken<any>;
export declare const DESIGN_PARAM_TYPES: InjectionToken<any[]>;
export declare const DESIGN_RETURN_TYPE: InjectionToken<any>;
export declare const ARGS: InjectionToken<any>;
export declare const APP_ROOT: InjectionToken<string>;
export declare const APP_THEME: InjectionToken<string>;
/**
 * 应用系统根目录
 */
export declare const ADDON_ROOT: InjectionToken<string>;
/**
 * 附件系统根目录
 */
export declare const ATTACHMENT_ROOT: InjectionToken<string>;
/**
 * 配置文件根目录
 */
export declare const CONFIG_ROOT: InjectionToken<string>;
/**
 * 系统配置
 */
export declare const CONFIG: InjectionToken<any>;
/**
 * 是否安装
 */
export declare const INSTALL_LOCK: InjectionToken<boolean>;
/**
 * 应用ID
 */
export declare const APP_ID: InjectionToken<string>;
export declare type PlatformModule = Type<any> | Promise<Type<any> | Type<any>[] | Promise<Type<any>[]>>;
export declare const PLATFORM_MODULE: InjectionToken<PlatformModule>;
export declare const CLOUD_URL: InjectionToken<string>;
/**
 * web site url
 */
export declare const WEB_SITE: InjectionToken<string>;
/**
 * uninstall hook
 */
export declare const UNINSTALL_HOOK: InjectionToken<{
    type: Type<any>;
    removes: Function[];
}>;
/**
 * 系统内置module
 */
export declare const NG_MODULE: InjectionToken<Type<any>>;
export declare const STATE: InjectionToken<any>;
export declare const NOT_FOUND: InjectionToken<any>;
