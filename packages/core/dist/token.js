"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NOT_FOUND = exports.STATE = exports.NG_MODULE = exports.UNINSTALL_HOOK = exports.WEB_SITE = exports.CLOUD_URL = exports.PLATFORM_MODULE = exports.APP_ID = exports.INSTALL_LOCK = exports.CONFIG = exports.CONFIG_ROOT = exports.ATTACHMENT_ROOT = exports.ADDON_ROOT = exports.APP_THEME = exports.APP_ROOT = exports.ARGS = exports.DESIGN_RETURN_TYPE = exports.DESIGN_PARAM_TYPES = exports.DESIGN_TYPE = exports.MATCH_RESULT = exports.CURRENT_TYPE = exports.METADATA_PARAMETERS = exports.CLASS_METADATAS = exports.METADATAS = exports.METADATA = exports.INSTANCE = exports.CONTEXT = exports.NEXT = void 0;
const types_1 = require("./types");
exports.NEXT = new types_1.InjectionToken(`@nger/core next function`);
exports.CONTEXT = new types_1.InjectionToken(`@nger/core context function`);
exports.INSTANCE = new types_1.InjectionToken(`@nger/core instance`);
exports.METADATA = new types_1.InjectionToken(`@nger/core metadata`);
exports.METADATAS = new types_1.InjectionToken(`@nger/core metadatas`);
exports.CLASS_METADATAS = new types_1.InjectionToken(`@nger/core class metadatas`);
exports.METADATA_PARAMETERS = new types_1.InjectionToken(`@nger/core current type`);
exports.CURRENT_TYPE = new types_1.InjectionToken(`@nger/core current type`);
exports.MATCH_RESULT = new types_1.InjectionToken(`@nger/core match result`);
exports.DESIGN_TYPE = new types_1.InjectionToken(`@nger/core design type`);
exports.DESIGN_PARAM_TYPES = new types_1.InjectionToken(`@nger/core design param types`);
exports.DESIGN_RETURN_TYPE = new types_1.InjectionToken(`@nger/core design return type`);
// cli
exports.ARGS = new types_1.InjectionToken(`@nger/core args`);
// paths
exports.APP_ROOT = new types_1.InjectionToken(`@nger/core app root`);
exports.APP_THEME = new types_1.InjectionToken(`@nger/core app theme`);
/**
 * 应用系统根目录
 */
exports.ADDON_ROOT = new types_1.InjectionToken(`@nger/core addon root`);
/**
 * 附件系统根目录
 */
exports.ATTACHMENT_ROOT = new types_1.InjectionToken(`@nger/core attachment root`);
/**
 * 配置文件根目录
 */
exports.CONFIG_ROOT = new types_1.InjectionToken(`@nger/core config root`);
/**
 * 系统配置
 */
exports.CONFIG = new types_1.InjectionToken(`@nger/core config`);
/**
 * 是否安装
 */
exports.INSTALL_LOCK = new types_1.InjectionToken(`@nger/core install lock`);
/**
 * 应用ID
 */
exports.APP_ID = new types_1.InjectionToken(`@nger/core app id`);
exports.PLATFORM_MODULE = new types_1.InjectionToken(`@nger/core platform module`);
// cloud url
exports.CLOUD_URL = new types_1.InjectionToken(`@nger/core cloud url`);
/**
 * web site url
 */
exports.WEB_SITE = new types_1.InjectionToken(`@nger/core web site url`);
/**
 * uninstall hook
 */
exports.UNINSTALL_HOOK = new types_1.InjectionToken(`@nger/core uninstall hook`);
/**
 * 系统内置module
 */
exports.NG_MODULE = new types_1.InjectionToken(`@nger/core ng module`);
exports.STATE = new types_1.InjectionToken(`@nger/core state`);
exports.NOT_FOUND = new types_1.InjectionToken(`@nger/core not found`);
