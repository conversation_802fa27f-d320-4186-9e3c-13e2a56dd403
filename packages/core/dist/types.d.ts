export declare type StringToken<T> = string & {
    type?: T;
};
export declare type NumberToken<T> = number & {
    type?: T;
};
export declare type SymbolToken<T> = symbol & {
    type?: T;
};
export declare type BooleanToken<T> = boolean & {
    type?: T;
};
export declare type DateToken<T> = Date & {
    type?: T;
};
export declare type ObjectToken<T> = object & {
    type?: T;
};
export declare type RegExpToken<T> = RegExp & {
    type?: T;
};
export interface Type<T> extends Function {
    new (...args: any[]): T;
}
export declare function isType<T>(val: any): val is Type<T>;
export interface AbstractType<T> extends Function {
    prototype: T;
}
export declare class InjectionToken<T> {
    private readonly desc;
    constructor(desc: string);
}
export declare type Token<T> = StringToken<T> | NumberToken<T> | SymbolToken<T> | BooleanToken<T> | DateToken<T> | ObjectToken<T> | InjectionToken<T> | AbstractType<T> | Type<T> | RegExpToken<T>;
export interface IMetadataClass<O> {
    id: string;
    metadataKey: string;
    options: O;
}
export interface IMetadataMethodOrProperty<O> {
    id: string;
    metadataKey: string;
    propertyKey: string;
    options: O;
    parameters: IMetadataParameter<any>[];
}
export declare function isIMetadataMethodOrProperty<O>(val: any): val is IMetadataMethodOrProperty<O>;
export interface IMetadataParameter<O> {
    id: string;
    metadataKey: string;
    propertyKey: string;
    parameterIndex: number;
    options: O;
}
export declare function isIMetadataParameter<O>(val: any): val is IMetadataParameter<O>;
export declare type IMetadata<O> = IMetadataClass<O> | IMetadataMethodOrProperty<O> | IMetadataParameter<O>;
