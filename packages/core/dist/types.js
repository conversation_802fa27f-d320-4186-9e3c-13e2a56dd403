"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isIMetadataParameter = exports.isIMetadataMethodOrProperty = exports.InjectionToken = exports.isType = void 0;
function isType(val) {
    return typeof val === 'function';
}
exports.isType = isType;
class InjectionToken {
    desc;
    constructor(desc) {
        this.desc = desc;
    }
}
exports.InjectionToken = InjectionToken;
function isIMetadataMethodOrProperty(val) {
    return val && Reflect.has(val, 'propertyKey') && !isIMetadataParameter(val);
}
exports.isIMetadataMethodOrProperty = isIMetadataMethodOrProperty;
function isIMetadataParameter(val) {
    return val && Reflect.has(val, 'parameterIndex');
}
exports.isIMetadataParameter = isIMetadataParameter;
