"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handlerType = exports.isNgModuleType = exports.getTypeDef = exports.createModuleProviders = exports.processModule = exports.processRootModule = exports.getPlatformModule = exports.handlerMetadataMethodOrProperty = exports.toPayload = exports.toJson = exports.toSuccess = exports.toError = void 0;
const decorator_1 = require("./decorator");
const injector_1 = require("./injector");
const metadata_1 = require("./metadata");
const multi_middleware_1 = require("./multi-middleware");
const token_1 = require("./token");
const types_1 = require("./types");
const watcher_1 = require("./watcher");
function toError(msg) {
    return { msg, data: null };
}
exports.toError = toError;
function toSuccess(data) {
    return { msg: 'ok', data };
}
exports.toSuccess = toSuccess;
function toJson(msg, data) {
    return {
        msg, data
    };
}
exports.toJson = toJson;
function toPayload(action, data) {
    return {
        action,
        data
    };
}
exports.toPayload = toPayload;
function getProvideInName(provideIn) {
    if (provideIn) {
        let obj = provideIn;
        if (Reflect.has(obj, `name`)) {
            return Reflect.get(obj, `name`);
        }
        return provideIn.toString();
    }
    return ``;
}
async function handlerMetadataMethodOrProperty(injector, metadata) {
    const handlers = injector.getMulti(metadata.metadataKey);
    const parameters = (paramsInjector) => {
        return Promise.all((metadata.parameters || [])
            .sort((a, b) => a.parameterIndex - b.parameterIndex)
            .map(parameter => {
            const handlers = injector.getMulti(parameter.metadataKey);
            paramsInjector.use([{ provide: token_1.METADATA, useValue: parameter }]);
            return new multi_middleware_1.MultiMiddleware(handlers).handle(paramsInjector);
        }));
    };
    const parent = injector.name;
    const propertyKey = Symbol.for(getProvideInName(parent) + '.' + metadata.propertyKey);
    const currentInjector = injector.create([{ provide: token_1.METADATA, useValue: metadata }, { provide: token_1.METADATA_PARAMETERS, useValue: parameters }], propertyKey);
    await new multi_middleware_1.MultiMiddleware(handlers).handle(currentInjector);
}
exports.handlerMetadataMethodOrProperty = handlerMetadataMethodOrProperty;
async function getPlatformModule(injector) {
    const platformModules = await Promise.all(injector.getMulti(token_1.PLATFORM_MODULE));
    return platformModules.map(module => {
        if (Array.isArray(module)) {
            return module;
        }
        else {
            return [module];
        }
    }).flat();
}
exports.getPlatformModule = getPlatformModule;
async function processRootModule(type, root) {
    const platformModules = await getPlatformModule(root);
    const injector = await processModule(type, root, 'root', platformModules);
    return injector;
}
exports.processRootModule = processRootModule;
async function processModule(type, root, name, modules) {
    const def = getTypeDef(type);
    def.imports = [...modules || [], ...def.imports || []];
    const _providers = await createModuleProviders(def, root);
    // typeinjector
    const decorators = decorator_1.store.getDecorators(type);
    const { classes, properties, parameters } = decorators;
    const _parameters = (injector) => Promise.all((parameters || []).map(parameter => {
        const handlers = injector.getMulti(parameter.metadataKey);
        const parameterInjector = injector.create([{ provide: token_1.METADATA, useValue: parameter }]);
        return new multi_middleware_1.MultiMiddleware(handlers).handle(parameterInjector);
    }));
    const injector = root.create([], name || type);
    const { controllers, imports, providers, id, dev } = def;
    const removes = injector.use([
        ..._providers,
        { provide: token_1.NG_MODULE, useValue: type },
        {
            provide: token_1.INSTANCE,
            useFactory: (injector) => injector.get(type),
            deps: [injector_1.Injector]
        }, {
            provide: token_1.DESIGN_TYPE,
            useFactory: () => (0, metadata_1.getDesignType)(type)
        }, {
            provide: token_1.DESIGN_PARAM_TYPES,
            useFactory: () => (0, metadata_1.getDesignParamTypes)(type) || []
        }, {
            provide: token_1.DESIGN_RETURN_TYPE,
            useFactory: () => (0, metadata_1.getDesignReturnType)(type)
        }, {
            provide: token_1.CLASS_METADATAS,
            useValue: classes
        },
        {
            provide: type,
            useFactory: (injector) => {
                const args = _parameters(injector);
                const paramTypes = injector.get(token_1.DESIGN_PARAM_TYPES);
                const _args = paramTypes.map((param, index) => {
                    if (Reflect.has(args, index)) {
                        return Reflect.get(args, index);
                    }
                    return injector.get(param);
                });
                return new type(..._args);
            },
            deps: [injector_1.Injector]
        }
    ]);
    injector.use([{
            provide: token_1.UNINSTALL_HOOK,
            useValue: {
                type,
                removes
            },
            multi: true
        }], 'root');
    if (imports && imports.length > 0) {
        await Promise.all(imports.map(async (imp) => {
            if ((0, types_1.isType)(imp)) {
                await processModule(imp, injector);
            }
            else if ((0, decorator_1.isModuleWidthProvider)(imp)) {
                const { module, providers } = imp;
                await processModule(module, injector);
            }
            else if (imp) {
                // const { main, exportName } = imp;
                // const type = await import(main).then(res => Reflect.get(res, exportName));
                // await processModule(type, injector);
            }
        }));
    }
    await Promise.all((controllers || []).map(c => handlerType(injector, c)));
    // const typeInjector = await handlerType(injector, type);
    if (!!dev) {
        const watcher = injector.get(watcher_1.Watcher);
        if (!id) {
            throw new Error(`in dev module, module id must be __filename`);
        }
        if (watcher) {
            watcher.watch(id, type);
        }
        else {
            console.log(`dev module is open, but not found watcher`);
        }
    }
    const instance = injector.get(type);
    if ((0, decorator_1.isOnInit)(instance)) {
        await instance.onInit(root);
    }
    if ((0, decorator_1.isDoBootstrap)(instance)) {
        await instance.doBootstrap(root);
    }
    return injector;
}
exports.processModule = processModule;
async function createModuleProviders(def, injector) {
    let _allProviders = [];
    const { controllers, imports, providers } = def || {};
    if (imports && imports.length > 0) {
        await Promise.all(imports.map(async (imp) => {
            if ((0, types_1.isType)(imp)) {
                const providers = await createModuleProviders(getTypeDef(imp), injector);
                _allProviders = [
                    ..._allProviders,
                    ...providers
                ];
            }
            else if ((0, decorator_1.isModuleWidthProvider)(imp)) {
                const { module, providers } = imp;
                const moduleProviders = await createModuleProviders(getTypeDef(module), injector);
                _allProviders = [
                    ..._allProviders,
                    ...moduleProviders,
                    ...providers
                ];
            }
            else {
                // const { main, exportName } = imp;
                // const type = await import(main).then(res => Reflect.get(res, exportName));
                // const providers = await createModuleProviders(getTypeDef(type), injector);
                // _allProviders = [
                //     ..._allProviders,
                //     ...providers
                // ]
            }
        }));
    }
    _allProviders = [
        ..._allProviders,
        ...providers || []
    ];
    return _allProviders;
}
exports.createModuleProviders = createModuleProviders;
function getTypeDef(type) {
    if (!type)
        return {};
    const decorators = decorator_1.store.getDecorators(type);
    const { classes, properties, parameters } = decorators;
    const item = classes.find(it => it.metadataKey === decorator_1.ModuleMetadataKey);
    return item && item.options || {};
}
exports.getTypeDef = getTypeDef;
function isNgModuleType(type) {
    const decorators = decorator_1.store.getDecorators(type);
    const { classes, properties, parameters } = decorators;
    const item = classes.find(it => it.metadataKey === decorator_1.ModuleMetadataKey);
    return !!item;
}
exports.isNgModuleType = isNgModuleType;
async function handlerType(injector, type) {
    const decorators = decorator_1.store.getDecorators(type);
    const { classes, properties, parameters } = decorators;
    const _parameters = (injector) => Promise.all((parameters || []).map(parameter => {
        const handlers = injector.getMulti(parameter.metadataKey);
        const parameterInjector = injector.create([{ provide: token_1.METADATA, useValue: parameter }], 'parameters');
        return new multi_middleware_1.MultiMiddleware(handlers).handle(parameterInjector);
    }));
    const typeInjector = injector.create([], type);
    typeInjector.use([{
            provide: token_1.INSTANCE,
            useFactory: (injector) => injector.get(type),
            deps: [injector_1.Injector]
        }, {
            provide: token_1.DESIGN_TYPE,
            useFactory: () => (0, metadata_1.getDesignType)(type)
        }, {
            provide: token_1.DESIGN_PARAM_TYPES,
            useFactory: () => (0, metadata_1.getDesignParamTypes)(type) || []
        }, {
            provide: token_1.DESIGN_RETURN_TYPE,
            useFactory: () => (0, metadata_1.getDesignReturnType)(type)
        }, {
            provide: token_1.CLASS_METADATAS,
            useValue: classes
        },
        {
            provide: type,
            useFactory: (injector) => {
                const args = _parameters(injector);
                const paramTypes = injector.get(token_1.DESIGN_PARAM_TYPES);
                const _args = paramTypes.map((param, index) => {
                    if (Reflect.has(args, index)) {
                        return Reflect.get(args, index);
                    }
                    return injector.get(param);
                });
                return new type(..._args);
            },
            deps: [injector_1.Injector]
        }]);
    await Promise.all([...classes].map(async (cls) => {
        const handlers = typeInjector.getMulti(cls.metadataKey);
        if (handlers && handlers.length > 0) {
            const _injector = typeInjector.create([
                { provide: token_1.METADATA, useValue: cls },
                { provide: token_1.METADATAS, useValue: decorators },
                { provide: token_1.CURRENT_TYPE, useValue: type },
            ]);
            await new multi_middleware_1.MultiMiddleware(handlers).handle(_injector);
        }
    }));
    await Promise.all(properties.map(p => handlerMetadataMethodOrProperty(typeInjector, p)));
    return typeInjector;
}
exports.handlerType = handlerType;
