{"name": "@nger/core", "version": "1.10.38", "description": "", "main": "dist/core.js", "module": "build/core.es.js", "types": "dist/core.d.ts", "publishConfig": {"access": "public"}, "author": "", "license": "ISC", "dependencies": {"@nger/core": "file:", "path-to-regexp": "^6.2.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.5.5"}, "devDependencies": {"@types/node": "^17.0.24"}, "scripts": {"tsc": "tsc", "tsc:build": "tsc", "rollup": "rollup -c"}, "readme": "## Plugin\n## Middleware\n\n## PlatformInit \n> when platform bootstrap init\n## AppInit\n> when app bootstrap init\n\n\nPlatformInit->Plugin->AppInit\nRequest->Middleware->ControllerMethod\n"}