import { Injector } from "./injector";
import { Plugin, Middleware, PlatformInit, AppInit, LoadAddon, AppStart } from "./middleware";
import { MultiMiddleware } from "./multi-middleware";

export class App {
    plugins: Plugin[] = [];
    middlewares: Middleware[] = [];
    injector: Injector;
    constructor(injector?: Injector) {
        this.injector = injector || Injector.create([], undefined, 'root')
        this.injector.use({ provide: App, useValue: this }, 'root')
    }
    async bootstrap(): Promise<Injector> {
        /**
         * plugin run
         */
        this.plugins = this.injector.getMulti(Plugin);
        await new MultiMiddleware(this.plugins).handle(this.injector);
        /**
         * platform init run
         */
        const platformInits = this.injector.getMulti(PlatformInit);
        await new MultiMiddleware(platformInits).handle(this.injector);
        /**
         * app init run
         */
        const appInits = this.injector.getMulti(AppInit);
        await new MultiMiddleware(appInits).handle(this.injector);
        /**
         * after platform and app init , load cloud app
         */
        const loadAddons = this.injector.getMulti(LoadAddon);
        await new MultiMiddleware(loadAddons).handle(this.injector)

        const appStarts = this.injector.getMulti(AppStart);
        await new MultiMiddleware(appStarts).handle(this.injector)
        return this.injector;
    }
}
