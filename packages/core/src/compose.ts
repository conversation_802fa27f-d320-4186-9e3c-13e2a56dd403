import { Injector } from "./injector";
import { NEXT, Next } from "./token";

export interface CanCompose {
    (injector: Injector, next?: Next): any | Promise<any>;
}


export function composeAsync(middleware: CanCompose[]) {
    return (injector: Injector, next?: Next) => {
        let index = -1
        return dispatch(0);
        function dispatch(i: number): void {
            if (i <= index) throw new Error('next() called multiple times')
            index = i
            let fn = middleware[i]
            if (i === middleware.length) fn = next!
            if (!fn) return;
            try {
                const next = dispatch.bind(null, i + 1);
                const nextInjector = injector.create([{ provide: NEXT, useValue: next }], `next`)
                return fn(nextInjector, dispatch.bind(null, i + 1)) as void;
            } catch (err) {
                throw err;
            }
        }
    }
}
export function compose(middleware: CanCompose[]) {
    return (injector: Injector, next?: Next) => {
        let index = -1
        return dispatch(0);
        function dispatch(i: number): Promise<any> {
            if (i <= index) return Promise.reject(new Error('next() called multiple times'));
            index = i;
            let fn = middleware[i];
            if (i === middleware.length) {
                fn = next!
            }
            if (!fn) return Promise.resolve();
            try {
                const next = dispatch.bind(null, i + 1);
                const nextInjector = injector.create([{ provide: NEXT, useValue: next }], `next`);
                return Promise.resolve(fn(nextInjector, dispatch.bind(null, i + 1)));
            } catch (err) {
                return Promise.reject(err);
            }
        }
    }
}