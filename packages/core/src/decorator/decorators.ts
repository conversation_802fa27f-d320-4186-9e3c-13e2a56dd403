import { Decorator } from "../middleware";
import { StaticProvider } from "../provider";
import { StringToken, Type, Token } from "../types";
import { createClassDecorator, createMethodDecorator, createParameterDecorator } from "./util";
/**
 * injectable
 */
export const InjectableMetadataKey: StringToken<Decorator> = `@nger/core injectable metadata key`;
export interface InjectableDef {
    providedIn?: Type<any> | 'root' | 'platform' | 'any' | null;
}
export const Injectable = createClassDecorator<InjectableDef>(InjectableMetadataKey);
/**
 * controller
 */
export const ControllerMetadataKey: StringToken<Decorator> = `@nger/core controller metadata key`;
export const Controller = createClassDecorator<string>(ControllerMetadataKey);
/**
 * page
 */
export const PageMetadataKey: StringToken<Decorator> = `@nger/core page metadata key`;
export interface PageDef {
    path: string;
    templateUrl: string;
    styleUrls?: string[];
}
export const Page = createClassDecorator<PageDef>(PageMetadataKey);
/**
 * module
 */
export interface ModuleWidthProvider<T> {
    module: Type<T>;
    providers: StaticProvider<any>[];
}
export interface ModuleStatic {
    main: string;
    exportName: string;
}
export type ModuleImport = Type<any> | ModuleWidthProvider<any> | ModuleStatic;
export function isModuleWidthProvider<T>(val: any): val is ModuleWidthProvider<T> {
    return val && Reflect.has(val, 'module') && Reflect.has(val, 'providers')
}
export interface ModuleDef {
    id?: string;
    imports?: ModuleImport[];
    providers?: StaticProvider<any>[];
    controllers?: Type<any>[];
    dev?: boolean;
}

export const ModuleMetadataKey: StringToken<Decorator> = `@nger/core module metadata key`;
export const Module = createClassDecorator<ModuleDef>(ModuleMetadataKey);

export const InjectMetadataKey: StringToken<Decorator> = `@nger/core inject metadata key`;
export const Inject = createParameterDecorator<Token<any>>(InjectMetadataKey);

export const ParamsMetadataKey: StringToken<Decorator> = `@nger/core params metadata key`;
export const Params = createParameterDecorator<string>(ParamsMetadataKey);

export const StateMetadatakey: StringToken<Decorator> = `@nger/core state metadata key`
export const State = createParameterDecorator<string>(StateMetadatakey)


export function createHttpMethodDecorator(key: string, method: string) {
    const decorator = createMethodDecorator<{ method: string, path: string }>(key)
    return (path: string) => decorator({ method, path })
}

export const GetMetadataKey: StringToken<Decorator> = `@nger/http get metadata key`;
export const Get = createHttpMethodDecorator(GetMetadataKey, 'get');
