import { Injector } from "../injector";

export interface OnInstall { 
    onInstall(): Promise<void>;
}
export interface OnUnInstall { }
export interface OnUpgrade { }
export interface OnWatch { }

export interface OnInit {
    onInit(injector: Injector): Promise<void> | void;
}
export function isOnInit(val: any): val is OnInit {
    return val && Reflect.has(val, 'onInit')
}
export interface OnDestory { }
export interface OnBootstrap {
    doBootstrap(injector: Injector): Promise<void> | void;
}

export function isDoBootstrap(val: any): val is OnBootstrap {
    return val && Reflect.has(val, 'doBootstrap')
}
export interface DoRegister { }


