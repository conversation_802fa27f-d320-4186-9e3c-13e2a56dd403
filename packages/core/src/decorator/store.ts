/*
 * @Author: imeepos
 * @Date: 2022-04-12 07:46:41
 * @LastEditors: imeepos
 * @LastEditTime: 2022-04-12 13:36:19
 * @Description: 
 * email:<EMAIL> mobile:18639118753 wechat:meepo_brothet realname:yang ming ming
 * Copyright (c) 2022 by imeepos/guanjieinc.com, All Rights Reserved. 
 */

import { IMetadataClass, IMetadataMethodOrProperty, IMetadataParameter, isIMetadataMethodOrProperty, isIMetadataParameter, Type } from "../types";

class DecoratorStorage<K, V extends { metadataKey: string, id: string, options?: any }> {
    private _map: Map<K, Set<V>> = new Map();
    forEach(callbackfn: (value: Set<V>, key: K, map: Map<K, Set<V>>) => void, thisArg?: any) {
        this._map.forEach(callbackfn, thisArg)
    }
    toKeys(): K[] {
        const result: K[] = [];
        this._map.forEach((v, k) => result.push(k));
        return result;
    }
    get(key: K): Set<V> {
        if (this._map.has(key)) {
            return this._map.get(key)!
        }
        const val = new Set<V>()
        this._map.set(key, val);
        return val
    }
    setV(key: K, val: V) {
        const vals = this.get(key);
        if (vals.has(val)) {
            return this;
        } else {
            vals.add(val);
            return this;
        }
    }

    getMetadata<T extends V>(key: K, metadataKey: string): T {
        const set = this.get(key)
        return [...set].find(it => it.metadataKey === metadataKey) as T;
    }

    getDecorators(key: Type<any>): {
        classes: IMetadataClass<any>[],
        properties: IMetadataMethodOrProperty<any>[],
        parameters: IMetadataParameter<any>[]
    } {
        const decorators = this.get(key as any);
        const propertiesAndParameters = this.get(key.prototype);
        const children: IMetadataMethodOrProperty<any>[] = [];
        const parameters: IMetadataParameter<any>[] = [...decorators].filter(it => isIMetadataParameter(it)) as any[];
        const classes: IMetadataClass<any>[] = [...decorators].filter(it => !isIMetadataParameter(it)) as any[];
        propertiesAndParameters.forEach((val) => {
            if (isIMetadataMethodOrProperty(val)) {
                val.parameters = [...propertiesAndParameters].filter(it => {
                    if (isIMetadataParameter(it)) {
                        return it.propertyKey == val.propertyKey
                    }
                    return false;
                }) as any[];
                children.push(val)
            }
        })
        return {
            classes: classes,
            properties: children,
            parameters
        }
    }
}

export const store = new DecoratorStorage();