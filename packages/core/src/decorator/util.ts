import { store } from './store';
/**
 * database decorator
 */
export function createClassDecorator<T>(metadataKey: string) {
    return (options?: T) => {
        return (target: any) => {
            store.setV(target, { metadataKey, options } as any)
        }
    }
}

export function createIdClassDecorator<T>(metadataKey: string) {
    return (id: string, options?: T) => {
        return (target: any) => {
            store.setV(target, { metadataKey, id, options } as any)
        }
    }
}

export function createPropertyDecorator<T>(metadataKey: string, config?: PropertyDescriptor) {
    return (options?: T): PropertyDecorator => {
        return (target: any, propertyKey: any, descriptor?: TypedPropertyDescriptor<T>) => {
            // insert class_decorator values(key,id)
            if (config && descriptor) {
                const { enumerable, configurable, writable } = config;
                if (typeof enumerable !== 'undefined') {
                    descriptor.enumerable = !!enumerable;
                }
                if (typeof configurable !== 'undefined') {
                    descriptor.configurable = !!configurable;
                }
                if (typeof writable !== 'undefined') {
                    descriptor.writable = !!writable;
                }
            }
            store.setV(target, { metadataKey, propertyKey,descriptor,  options } as any)
            return descriptor;
        }
    }
}
export function createMethodDecorator<T>(metadataKey: string) {
    return (options?: T): MethodDecorator => {
        return (target: any, propertyKey: any, descriptor: TypedPropertyDescriptor<any>) => {
            store.setV(target, { metadataKey, id: propertyKey, propertyKey, options } as any)
            return descriptor;
        }
    }
}
export function createParameterDecorator<T>(metadataKey: string) {
    return (options?: T): ParameterDecorator => {
        return (target: any, propertyKey: any, parameterIndex: number) => {
            // insert class_decorator values(key,id)
            store.setV(target, { metadataKey, propertyKey, parameterIndex, options } as any)
        }
    }
}

