import { Observable } from 'rxjs';
export interface Defer<T> extends Promise<T> {
    resolve: (value?: T) => void;
    reject: (reason?: any) => void;
}
export function defer<T>(): Defer<T> {
    let resolve!: (value?: T) => void;
    let reject!: (reason?: any) => void;
    const promise = new Promise<T>((_resolve, _reject) => {
        resolve = _resolve as any;
        reject = _reject;
    }) as Defer<T>;
    promise.resolve = resolve;
    promise.reject = reject;
    return promise;
}
export interface NgerObservable<T> extends Observable<T> {
    next: (value?: T) => void;
    error: (err?: any) => void;
    complete: () => void;
}
export function observable<T>(): NgerObservable<T> {
    let next!: (value?: T) => void;
    let error!: (err?: any) => void;
    let complete!: () => void;
    const obser = new Observable<T>((sub) => {
        next = sub.next.bind(sub);
        error = sub.error.bind(sub);
        complete = sub.complete.bind(sub);
        obser.next = next;
        obser.error = error;
        obser.complete = complete;
    }) as NgerObservable<T>;
    return obser;
}

export async function* observableToAsyncGenerator<T>(observable: Observable<T>) {
    let nextData: Defer<T> = defer();
    const sub = observable.subscribe({
        next(data) {
            const n = nextData;
            nextData = defer();
            n.resolve(data);
        },
        error(err) {
            nextData.reject(err);
        },
        complete() {
            const n = nextData;
            nextData = null as any;
            n.resolve();
        }
    });
    try {
        for (; ;) {
            const value = await nextData;
            if (!nextData) break;
            yield value;
        }
    } finally {
        sub.unsubscribe();
    }
}