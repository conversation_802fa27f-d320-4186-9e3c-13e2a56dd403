import {
    isTypeProvider,
    isUseClassProvider, isUseExistProvider, isUseFactoryProvider, isUseValueProvider,
    Provider, StaticProvider, TypeProvider, UseClassProvider, UseFactoryProvider, UseValueProvider
} from "./provider";
import { InjectionToken, Token, Type } from "./types";
export function isRegExp(val: any): val is RegExp {
    return isObject(val) && objectToString(val) === '[object RegExp]';
}
export function objectToString(o: any): string {
    return Object.prototype.toString.call(o);
}
export function isObject(val: any): val is object {
    return typeof val === 'object' && val !== null
}
export function isDate(d: any): d is Date {
    return isObject(d) && objectToString(d) === '[object Date]';
}
export function isPrimitive(arg: any) {
    return arg === null ||
        typeof arg === 'boolean' ||
        typeof arg === 'number' ||
        typeof arg === 'string' ||
        typeof arg === 'symbol' ||  // ES6 symbol
        typeof arg === 'undefined';
}
export function pad(n: number): string {
    return n < 10 ? '0' + n.toString(10) : n.toString(10);
}
export function isString(val: any): val is string {
    return typeof val === 'string'
}
export type ProvideIn = Type<any> | 'root' | 'platform' | 'any' | string | symbol | null | undefined;
export const INJECTOR_GET = new InjectionToken<<T>(token: Token<T>, notfound?: unknown, from?: ProvideIn, to?: ProvideIn)=> T>(`@nger/core not found`)
export abstract class Injector {
    name: ProvideIn = null;
    abstract delete(filter: (token: Token<any>, record: Record<any>) => boolean): void;
    abstract deleteMulti(filter: (token: Token<any>, record: Record<any>) => boolean, from: ProvideIn): void;
    abstract get<T>(token: Token<T>, notfound?: unknown, from?: ProvideIn, to?: ProvideIn): T;
    abstract getMulti<T>(token: Token<T>, from?: ProvideIn, to?: ProvideIn): T[];
    abstract use(providers: StaticProvider<any>[] | StaticProvider<any>, name?: ProvideIn): Function[];
    abstract create(providers: StaticProvider<any>[], name?: ProvideIn): Injector;
    abstract getString(to: string): Record<any>[];
    static create(providers: StaticProvider<any>[], parent?: Injector, name?: ProvideIn) {
        return new NgerInjector(providers, parent, name);
    }
}

export interface Record<T> {
    token: Token<T>;
    factory: (...args: any[]) => T;
    value: T | undefined;
    title?: string;
}
import { match, MatchResult } from 'path-to-regexp'
import { getDesignParamTypes } from "./metadata";
let _current: Injector | undefined;
let _preInjector: Injector | undefined;
function setCurrentInjector(injector: Injector) {
    _preInjector = _current;
    _current = injector;
}
export function getInjector() {
    return _current!;
}
function reSetInjector() {
    _current = _preInjector;
}
export const ngerInjectorStore: Map<ProvideIn, Injector> = new Map();
export const ngerInjectors: Injector[] = [];
export class NgerInjector extends Injector {
    private map: Map<Token<any>, Record<any>> = new Map();
    private multiMap: Map<Token<any>, Set<Record<any>>> = new Map();
    parent: Injector | undefined;
    removes: Function[] = [];
    constructor(
        providers: StaticProvider<any>[],
        parent?: Injector,
        name?: ProvideIn
    ) {
        super();
        this.parent = parent;
        this.removes = this.use([...providers, { provide: Injector, useValue: this }]);
        this.name = name;
        ngerInjectorStore.set(name, this);
        ngerInjectors.push(this);
    }
    getString(to: string) {
        const records: Record<any>[] = [];
        if(this.name === to){
            this.map.forEach((val,key)=>{
                if(typeof key === 'string'){
                    records.push(val)
                }
            })
            return records;
        }
        if(this.parent) {
            return this.parent.getString(to)
        }
        return records;
    }
    use(
        providers: StaticProvider<any>[] | StaticProvider<any>,
        name?: ProvideIn
    ): Function[] {
        const removes: Function[] = [];
        if (name) {
            if (this.name === name) {
                if (Array.isArray(providers)) {
                    removes.push(...providers.map(provider => this.processProviderAndType(provider)).flat())
                } else {
                    removes.push(...this.processProviderAndType(providers))
                }
            }
            if (this.parent) {
                removes.push(...this.parent.use(providers, name))
            }
        } else {
            if (Array.isArray(providers)) {
                removes.push(...providers.map(provider => this.processProviderAndType(provider)).flat())
            } else {
                removes.push(...this.processProviderAndType(providers))
            }
        }
        if(name && name === this.name){
            const get = this.get(INJECTOR_GET)
            if(get && typeof get === 'function') {
                const old = this.get.bind(this);
                this._get = old;
                this.get = get.bind(this);
            }
        }
        return removes;
    }
    create(providers: StaticProvider<any>[], name?: ProvideIn): Injector {
        return new NgerInjector(providers, this, name)
    }
    delete(filter: (token: Token<any>, record: Record<any>) => boolean) {
        const items: Token<any>[] = [];
        this.map.forEach((it, key) => {
            if (filter(key, it)) {
                items.push(key)
            }
        })
        items.map((key) => {
            this.map.delete(key)
        })
    }
    deleteMulti(filter: (token: Token<any>, record: Record<any>) => boolean, from: ProvideIn) {
        if(from){
           if(from !== this.name){
                return this.parent.deleteMulti(filter, from)
           }
        }
        this.multiMap.forEach((it, key) => {
            const removes: Record<any>[] = [];
            it.forEach(item => {
                if (filter(key, item)) {
                    removes.push(item)
                }
            });
            removes.map(remove => it.delete(remove))
        })
    }
    tryGetMulti<T>(token: Token<T>, to?: ProvideIn): T[] {
        try {
            setCurrentInjector(this);
            let values: T[] = [];
            const set = this.multiMap.get(token)
            if (set) {
                [...set].map((record: Record<T>) => {
                    if (typeof record.value === 'undefined') {
                        record.value = record.factory();
                    }
                    values.push(record.value)
                });
                if (this.name && this.name === to) {
                    return values;
                }
                if (this.parent) {
                    const parentValue = this.parent.getMulti(token, to);
                    if (Array.isArray(parentValue)) {
                        values.push(...parentValue);
                    }
                }
                return values;
            }
            if (this.name && this.name === to) {
                return values;
            }
            if (this.parent) {
                return this.parent.getMulti(token, to)
            }
            return values;
        } finally {
            reSetInjector()
        }
    }
    getMulti<T>(token: Token<T>, from?: ProvideIn, to?: ProvideIn): T[] {
        if (from) {
            if (this.name === from) {
                return this.tryGetMulti(token, to)
            } else if (this.parent) {
                return this.parent.getMulti(token, from, to)
            } else {
                throw new Error(`can not found injector: ${from.toString()}`)
            }
        }
        return this.tryGetMulti(token, to)
    }
    getThis(token: Token<any>){
        const record = this.map.get(token);
        if (record) {
            if (typeof record.value === 'undefined') {
                record.value = record.factory();
            }
            return record.value;
        }
    }
    tryGet<T>(token: Token<T>, notfound?: unknown, to?: ProvideIn) {
        if (typeof token === 'undefined' || token === null) {
            debugger;
            return;
        }
        try {
            setCurrentInjector(this);
            const record = this.map.get(token);
            if (record) {
                if (typeof record.value === 'undefined') {
                    record.value = record.factory();
                }
                return record.value;
            }
            if (typeof token === 'string') {
                const keys = [...this.map.keys()];
                let result: MatchResult<any> | undefined;
                const _token = keys.find(key => {
                    if (isRegExp(key) || isString(key)) {
                        const _match = match(key, { decode: decodeURIComponent });
                        const _result = _match(token);
                        if (_result) {
                            result = _result;
                            return true;
                        }
                        return false;
                    }
                })!;
                const record = this.map.get(_token)
                if (record) {
                    return record.factory(result)
                }
            }
            if (to) {
                if (this.name === to) {
                    return notfound as T;
                }
            }
            if (this.parent) {
                return this.parent.get(token, notfound)
            }
            return notfound as T;
        } finally {
            reSetInjector()
        }
    }
    _get: <T>(token: Token<T>, notfound?: unknown, from?: ProvideIn, to?: ProvideIn)=> T;
    get<T>(token: Token<T>, notfound?: unknown, from?: ProvideIn, to?: ProvideIn): T {
        if (from) {
            if (this.name === from) {
                return this.tryGet(token, notfound)
            } else if (this.parent) {
                return this.parent.get(token, notfound, from)
            } else {
                throw new Error(`can not found injector: ${from.toString()}`)
            }
        }
        return this.tryGet(token, notfound, to)
    }
    private processProviderAndType<T>(provider: Provider<T> | TypeProvider<T>): Function[] {
        if (isTypeProvider(provider)) {
            // TODO: type provider
            const toProvider = this.typeToProvider(provider)
            return this.processProvider(toProvider)
        } else {
            return this.processProvider(provider)
        }
    }
    private typeToProvider<T>(type: TypeProvider<T>): UseClassProvider<T> {
        return {
            provide: type,
            useClass: type,
            deps: getDesignParamTypes(type)
        }
    }
    private processProvider<T>(provider: Provider<T>): Function[] {
        const removes: Function[] = [];
        const record = this.providerToRecord(provider);
        if (!!provider.multi) {
            // multi
            if (this.multiMap.has(provider.provide)) {
                const set = this.multiMap.get(provider.provide)!;
                removes.push(() => set.has(record) && set.delete(record))
                set.add(record)
            } else {
                const set = new Set([record])
                this.multiMap.set(provider.provide, set)
                removes.push(() => this.multiMap.has(record) && this.multiMap.delete(provider.provide))
            }
        } else {
            this.map.set(record.token, record);
            removes.push(() => this.map.has(record.token) && this.map.delete(record.token))
        }
        return removes;
    }
    private providerToRecord<T>(provider: Provider<T>): Record<T> {
        if (isUseValueProvider(provider)) {
            return this.useValueToRecord(provider);
        }
        else if (isUseClassProvider(provider)) {
            return this.useClassProviderToRecord(provider);
        }
        else if (isUseFactoryProvider(provider)) {
            return this.useFactoryProviderToRecord(provider);
        } else if (isUseExistProvider(provider)) {
            if(provider.isClass){
                return {
                    token: provider.provide,
                    factory: () => {
                        const that = this;
                        return function Existing() {
                            return that.get<any>(provider.useExisting)
                        } as any;
                    },
                    value: undefined
                }
            }
            return {
                token: provider.provide,
                factory: () => {
                    return this.get<any>(provider.useExisting);
                },
                value: undefined
            }
        } else  {
            return {
                token: provider.provide,
                factory: () => {
                    return {} as T;
                },
                value: undefined
            }
        }
    }
    private useFactoryProviderToRecord<T>(provider: UseFactoryProvider<T>) {
        return {
            token: provider.provide,
            factory: (...args: any[]) => {
                if (args.length > 0) {
                    return provider.useFactory(...args);
                } else {
                    const deps: any[] = provider.deps || [];
                    const _args = deps.map(dep => this.get(dep))
                    return provider.useFactory(..._args);
                }
            },
            value: undefined
        }
    }
    private useClassProviderToRecord<T>(provider: UseClassProvider<T>) {
        return {
            token: provider.provide,
            factory: (...args: any[]) => {
                if (args.length > 0) {
                    return new provider.useClass(...args);
                } else {
                    const deps: any[] = provider.deps || [];
                    const _args = deps.map(dep => this.get(dep))
                    return new provider.useClass(..._args);
                }
            },
            value: undefined
        }
    }
    private useValueToRecord<T>(provider: UseValueProvider<T>) {
        return {
            token: provider.provide,
            factory: () => {
                return provider.useValue;
            },
            value: undefined
        }
    }
}
