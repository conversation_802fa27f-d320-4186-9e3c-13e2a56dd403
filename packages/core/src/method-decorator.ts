import { ControllerMeta<PERSON><PERSON><PERSON> } from "./decorator";
import { Injector } from "./injector";
import { Decorator } from "./middleware";
import { CLASS_METADATAS, CONTEXT, INSTANCE, MATCH_RESULT, METADATA, METADATA_PARAMETERS, NEXT, Next, NG_MODULE, UNINSTALL_HOOK } from "./token";
import { IMetadataClass, IMetadataMethodOrProperty } from "./types";

export class CoreMethodDecorator extends Decorator {
    constructor(private method: string) {
        super(`method decorator`)
    }
    toUrl(path: string = '') {
        return path.split('/').filter(it => !!it).join('/')
    }
    async handle(injector: Injector, next: Next): Promise<void> {
        const parent = injector.get<IMetadataMethodOrProperty<any>>(METADATA)
        const options = parent.options;
        const classMetadatas = injector.get(CLASS_METADATAS);
        const controllerMetadata = classMetadatas.find((it: IMetadataClass<any>) => it.metadataKey === ControllerMetadataKey)
        let path = options.path;
        if (controllerMetadata) {
            const url = `${this.toUrl(controllerMetadata.options || '')}/${this.toUrl(path || parent.propertyKey)}`
            path = `/${this.toUrl(url)}`
        }
        const provide = `${this.method}${path}`;
        const parameterFactory = injector.get(METADATA_PARAMETERS);
        const removes = injector.use([{
            provide: provide,
            useFactory: (result) => {
                const instance = injector.get(INSTANCE);
                const method = Reflect.get(instance, parent.propertyKey);
                const handler = async (ctx: any, next: Next, hInjector?: Injector) => {
                    const rootInjector = hInjector || injector;
                    const _injector = rootInjector.create([
                        { provide: MATCH_RESULT, useValue: result },
                        { provide: INSTANCE, useValue: instance },
                        { provide: CONTEXT, useValue: ctx },
                        { provide: NEXT, useValue: next }
                    ], 'handler');
                    const parameters = await parameterFactory(_injector);
                    return method.bind(instance)(...parameters)
                }
                return handler;
            }
        }], 'root');
        const currentModule = injector.get(NG_MODULE)
        injector.use([{
            provide: UNINSTALL_HOOK,
            useValue: { removes, type: currentModule },
            multi: true
        }], 'root')
        if (next) await next()
    }
}