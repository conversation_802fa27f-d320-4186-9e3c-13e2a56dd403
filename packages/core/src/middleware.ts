import { Injector } from "./injector";
import { Next } from "./token";
export abstract class Middleware {
    name: string;
    version: string = '0.0.0';
    status: number = 2;
    constructor(name: string) {
        this.name = name;
    }
    abstract handle(injector: Injector, next?: Next): Promise<any> | any;
}
export abstract class Plugin extends Middleware { }
export abstract class Decorator extends Middleware { }
export abstract class PlatformInit extends Middleware { }
export abstract class AppInit extends Middleware { }
export abstract class LoadAddon extends Middleware { }
export abstract class AppStart extends Middleware { }
