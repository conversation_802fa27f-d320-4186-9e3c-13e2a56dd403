import { Injector } from "./injector";
import { Middleware } from "./middleware";
import { Next } from "./token";
import { compose } from "./compose";
export class MultiMiddleware extends Middleware {
    constructor(private middlewares: Middleware[]) {
        super('@nger/core/MiltiMiddleware');
    }
    async handle(injector: Injector, next?: Next): Promise<void> {
        const middlewares = [...this.middlewares || []].map(mid => {
            if (typeof mid === 'function') {
                return mid;
            } else {
                return mid.handle.bind(mid)
            }
        });
        return compose(middlewares)(injector, next)
    }
}
