import { MatchR<PERSON>ult } from "path-to-regexp";
import { InjectMetadata<PERSON>ey, ParamsMetadataKey } from "./decorator";
import { Injector } from "./injector";
import { Decorator } from "./middleware";
import { StaticProvider } from "./provider";
import {  MATCH_RESULT, METADATA, Next } from "./token";

export class InjectDecorator extends Decorator {
    constructor() {
        super(ParamsMetadataKey)
    }
    handle(injector: Injector) {
        const metadata = injector.get(METADATA)
        if (metadata.options) {
            return injector.get(metadata.options)
        }
        return injector;
    }
}

export class ParamsDecorator extends Decorator {
    constructor() {
        super(ParamsMetadataKey)
    }
    handle(injector: Injector, next: Next) {
        const result = injector.get<MatchResult>(MATCH_RESULT)
        const metadata = injector.get(METADATA)
        if (metadata.options) {
            return Reflect.get(result.params, metadata.options)
        }
        return { ...result.params };
    }
}

export const paramDecoratorProviders: StaticProvider<any>[] = [{
    provide: ParamsMetadataKey,
    useFactory: () => new ParamsDecorator(),
    multi: true
}, {
    provide: InjectMetadataKey,
    useFactory: () => new InjectDecorator(),
    multi: true
}]