import { App } from "./app";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "./error-handler";
import { Injector } from "./injector";
import { paramDecoratorProviders } from "./param-decorator";
import { StaticProvider } from "./provider";
import { ADDON_ROOT, APP_ROOT, ATTACHMENT_ROOT, CONFIG_ROOT } from "./token";
import { InjectionToken, Type } from "./types";
import { processRootModule } from "./util";
export class PlatformRef {
    injector: Injector;
    type: Type<any>;
    constructor(injector: Injector) {
        this.injector = injector;
    }
    async bootstrap(type: Type<any>) {
        this.type = type;
        return this.reload();
    }
    private async reload() {
        const appInjector = await processRootModule(this.type, this.injector);
        const app = new App(appInjector);
        await app.bootstrap();
        return appInjector;
    }
}

let _platform: PlatformRef;
export function createPlatform(injector: Injector): PlatformRef {
    _platform = injector.get(PlatformRef);
    return _platform;
}
export function getPlatform(): PlatformRef | null {
    return _platform ? _platform : null;
}

export function createPlatformFactory(
    parentPlatformFactory: ((extraProviders?: StaticProvider<any>[]) => PlatformRef) | null,
    name: string,
    providers: StaticProvider<any>[] = []
): (extraProviders?: StaticProvider<any>[]) => PlatformRef {
    const desc = `Platform: ${name}`;
    const marker = new InjectionToken(desc);
    return (extraProviders: StaticProvider<any>[] = []) => {
        let platform = getPlatform();
        if (!platform) {
            if (parentPlatformFactory) {
                parentPlatformFactory(providers.concat(extraProviders).concat({ provide: marker, useValue: true }));
            } else {
                const injectedProviders: StaticProvider<any>[] = providers.concat(extraProviders).concat({ provide: marker, useValue: true });
                const topInjector = Injector.create([], undefined, 'top');
                const databaseInjector = Injector.create([], topInjector, 'db');
                const redisInjector = Injector.create([], databaseInjector, 'redis');
                createPlatform(Injector.create(injectedProviders, redisInjector, 'platform'));
            }
        }
        return assertPlatform(marker);
    };
}

export function assertPlatform(requiredToken: any): PlatformRef {
    const platform = getPlatform();
    if (!platform) {
        const errorMessage = 'No platform exists!';
        throw new Error(errorMessage);
    }
    if (!platform.injector.get(requiredToken, null)) {
        throw new Error('A platform with a different configuration has been created. Please destroy it first.');
    }
    return platform;
}

export const coreProviders: StaticProvider<any>[] = [
    {
        provide: ErrorHandler,
        useFactory: () => new ErrorHandler()
    },
    {
        provide: PlatformRef,
        useFactory: (injector: Injector) => new PlatformRef(injector),
        deps: [Injector]
    },
    {
        provide: ADDON_ROOT,
        useFactory: (injector: Injector) => {
            const root = injector.get(APP_ROOT);
            return `${root}/addons`
        },
        deps: [Injector]
    },
    {
        provide: ATTACHMENT_ROOT,
        useFactory: (injector: Injector) => {
            const root = injector.get(APP_ROOT);
            return `${root}/attachments`
        },
        deps: [Injector]
    },
    {
        provide: CONFIG_ROOT,
        useFactory: (injector: Injector) => {
            const root = injector.get(APP_ROOT);
            return `${root}/config`
        },
        deps: [Injector]
    },
    ...paramDecoratorProviders
]

export const platformCore = createPlatformFactory(null, 'core', coreProviders);
