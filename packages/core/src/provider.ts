import { Token, Type } from "./types";

export interface Provider<T> {
    provide: Token<T>;
    multi?: boolean;
}

export interface UseValueProvider<T> extends Provider<T> {
    useValue: T;
}

export function isUseValueProvider<T>(val: Provider<T>): val is UseValueProvider<T> {
    return val && Reflect.has(val, 'useValue')
}

export function useValue<T>(token: Token<T>, value: T, multi?: boolean): UseValueProvider<T> {
    return {
        provide: token,
        useValue: value,
        multi
    }
}

export interface UseClassProvider<T> extends Provider<T> {
    useClass: Type<T>;
    deps?: unknown[];
}

export function isUseClassProvider<T>(val: Provider<T>): val is UseClassProvider<T> {
    return val && Reflect.has(val, 'useClass')
}

export function useClass<T>(token: Token<T>, cls: Type<T>, deps: any[] = [], multi?: boolean): UseClassProvider<T> {
    return {
        provide: token,
        useClass: cls,
        multi,
        deps
    }
}


export function useMultiClass<T>(token: Token<T>, cls: Type<T>, deps: any[] = []) {
    return useClass(token, cls, deps, true)
}


export interface UseFactoryProvider<T> extends Provider<T> {
    useFactory: Factory<T>;
    deps?: unknown[];
}

export type Factory<T> = (...args: any[]) => T;

export function isUseFactoryProvider<T>(val: Provider<T>): val is UseFactoryProvider<T> {
    return val && Reflect.has(val, 'useFactory')
}

export function useFactory<T>(token: Token<T>, factory: Factory<T>, deps: any[] = [], multi?: boolean): UseFactoryProvider<T> {
    return {
        provide: token,
        useFactory: factory,
        multi,
        deps: deps
    }
}

export function useMultiFactory<T>(token: Token<T>, factory: Factory<T>, deps: any[] = []) {
    return useFactory(token, factory, deps, true)
}

export interface TypeProvider<T> extends Type<T> { }

export function isTypeProvider<T>(val: any): val is TypeProvider<T> {
    return val && typeof val === 'function'
}

export interface UseExistProvider<T> extends Provider<T> {
    useExisting: Token<T>;
    isClass?: boolean;
}
export function isUseExistProvider<T>(val: any): val is UseExistProvider<T> {
    return val && Reflect.has(val, 'useExisting')
}
export type StaticProvider<T> = UseExistProvider<T> | UseValueProvider<T> | UseClassProvider<T> | UseFactoryProvider<T> | TypeProvider<T>;
