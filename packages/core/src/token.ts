import { IMetadata, IMetadataClass, IMetadataMethodOrProperty, InjectionToken, Type } from "./types";
export interface Next {
    (): Promise<void> | void;
}
export const NEXT = new InjectionToken<() => void>(`@nger/core next function`);
export const CONTEXT = new InjectionToken<any>(`@nger/core context function`);

export const INSTANCE = new InjectionToken<any>(`@nger/core instance`);

export const METADATA = new InjectionToken<IMetadata<any>>(`@nger/core metadata`)
export interface Decorators {
    classes: Set<IMetadataClass<any>>;
    properties: IMetadataMethodOrProperty<any>[];
}
export const METADATAS = new InjectionToken<Decorators>(`@nger/core metadatas`)

export const CLASS_METADATAS = new InjectionToken<IMetadataClass<any>[]>(`@nger/core class metadatas`)
export const METADATA_PARAMETERS = new InjectionToken<Function>(`@nger/core current type`)
export const CURRENT_TYPE = new InjectionToken<Type<any>>(`@nger/core current type`)
import { MatchResult } from 'path-to-regexp';
export const MATCH_RESULT = new InjectionToken<MatchResult>(`@nger/core match result`)
export const DESIGN_TYPE = new InjectionToken<any>(`@nger/core design type`)
export const DESIGN_PARAM_TYPES = new InjectionToken<any[]>(`@nger/core design param types`)
export const DESIGN_RETURN_TYPE = new InjectionToken<any>(`@nger/core design return type`)
// cli
export const ARGS = new InjectionToken<any>(`@nger/core args`);
// paths
export const APP_ROOT = new InjectionToken<string>(`@nger/core app root`);
export const APP_THEME = new InjectionToken<string>(`@nger/core app theme`);
/**
 * 应用系统根目录
 */
export const ADDON_ROOT = new InjectionToken<string>(`@nger/core addon root`);
/**
 * 附件系统根目录
 */
export const ATTACHMENT_ROOT = new InjectionToken<string>(`@nger/core attachment root`);
/**
 * 配置文件根目录
 */
export const CONFIG_ROOT = new InjectionToken<string>(`@nger/core config root`);
/**
 * 系统配置
 */
export const CONFIG = new InjectionToken<any>(`@nger/core config`);
/**
 * 是否安装
 */
export const INSTALL_LOCK = new InjectionToken<boolean>(`@nger/core install lock`);
/**
 * 应用ID
 */
export const APP_ID = new InjectionToken<string>(`@nger/core app id`);
// platform regist module
export type PlatformModule = Type<any> | Promise<Type<any> | Type<any>[] | Promise<Type<any>[]>>;
export const PLATFORM_MODULE = new InjectionToken<PlatformModule>(`@nger/core platform module`);
// cloud url
export const CLOUD_URL = new InjectionToken<string>(`@nger/core cloud url`);
/**
 * web site url
 */
export const WEB_SITE = new InjectionToken<string>(`@nger/core web site url`);
/**
 * uninstall hook
 */
export const UNINSTALL_HOOK = new InjectionToken<{ type: Type<any>, removes: Function[] }>(`@nger/core uninstall hook`)
/**
 * 系统内置module
 */
export const NG_MODULE = new InjectionToken<Type<any>>(`@nger/core ng module`)
export const STATE = new InjectionToken<any>(`@nger/core state`)
export const NOT_FOUND = new InjectionToken<any>(`@nger/core not found`)
