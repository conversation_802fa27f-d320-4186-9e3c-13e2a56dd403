

export type StringToken<T> = string & { type?: T };
export type NumberToken<T> = number & { type?: T };
export type SymbolToken<T> = symbol & { type?: T };
export type BooleanToken<T> = boolean & { type?: T };
export type DateToken<T> = Date & { type?: T };
export type ObjectToken<T> = object & { type?: T };
export type RegExpToken<T> = RegExp & { type?: T };

export interface Type<T> extends Function {
    new(...args: any[]): T;
}
export function isType<T>(val: any): val is Type<T> {
    return typeof val === 'function'
}
export interface AbstractType<T> extends Function {
    prototype: T;
}

export class InjectionToken<T>{
    constructor(private readonly desc: string) { }
}

export type Token<T> =
    StringToken<T>
    | NumberToken<T>
    | SymbolToken<T>
    | BooleanToken<T>
    | DateToken<T>
    | ObjectToken<T>
    | InjectionToken<T>
    | AbstractType<T>
    | Type<T>
    | RegExpToken<T>;


export interface IMetadataClass<O> {
    id: string;
    metadataKey: string;
    options: O;
}
export interface IMetadataMethodOrProperty<O> {
    id: string;
    metadataKey: string;
    propertyKey: string;
    options: O;
    parameters: IMetadataParameter<any>[];
}
export function isIMetadataMethodOrProperty<O>(val: any): val is IMetadataMethodOrProperty<O> {
    return val && Reflect.has(val, 'propertyKey') && !isIMetadataParameter(val)
}
export interface IMetadataParameter<O> {
    id: string;
    metadataKey: string;
    propertyKey: string;
    parameterIndex: number;
    options: O;
}
export function isIMetadataParameter<O>(val: any): val is IMetadataParameter<O> {
    return val && Reflect.has(val, 'parameterIndex')
}
export type IMetadata<O> = IMetadataClass<O> | IMetadataMethodOrProperty<O> | IMetadataParameter<O>;