import { isD<PERSON><PERSON><PERSON><PERSON>p, isModuleWidthProvider, isOnInit, ModuleDef, ModuleMetadataKey, store } from "./decorator";
import { Injector, ProvideIn } from "./injector";
import { getDesignParamTypes, getDesignReturnType, getDesignType } from "./metadata";
import { Decorator } from "./middleware";
import { MultiMiddleware } from "./multi-middleware";
import { StaticProvider } from "./provider";
import {
    CURRENT_TYPE, METADATA, METADATAS, CLASS_METADATAS, METADATA_PARAMETERS,
    INSTANCE, DESIGN_TYPE, DESIGN_PARAM_TYPES, DESIGN_RETURN_TYPE, PLATFORM_MODULE, NG_MODULE, Next, NEXT, UNINSTALL_HOOK
} from "./token";
import { IMetadataMethodOrProperty, isType, Type } from "./types";
import { Watcher } from "./watcher";

export function toError(msg: string) {
    return { msg, data: null }
}
export function toSuccess<T>(data: T) {
    return { msg: 'ok', data }
}
export function toJson<T>(msg: string, data: T) {
    return {
        msg, data
    }
}
export interface Payload<T> {
    action: string;
    data: T;
}
export function toPayload<T>(action: string, data: T): Payload<T> {
    return {
        action,
        data
    }
}
function getProvideInName(provideIn: ProvideIn) {
    if (provideIn) {
        let obj: any = provideIn;
        if (Reflect.has(obj, `name`)) {
            return Reflect.get(obj, `name`);
        }
        return provideIn.toString();
    }
    return ``
}
export async function handlerMetadataMethodOrProperty(injector: Injector, metadata: IMetadataMethodOrProperty<any>) {
    const handlers = injector.getMulti<Decorator>(metadata.metadataKey);
    const parameters = (paramsInjector: Injector) => {
        return Promise.all((metadata.parameters || [])
            .sort((a, b) => a.parameterIndex - b.parameterIndex)
            .map(parameter => {
                const handlers = injector.getMulti<Decorator>(parameter.metadataKey)
                paramsInjector.use([{ provide: METADATA, useValue: parameter }]);
                return new MultiMiddleware(handlers).handle(paramsInjector);
            }))
    }
    const parent = injector.name;
    const propertyKey = Symbol.for(getProvideInName(parent) + '.' + metadata.propertyKey);
    const currentInjector = injector.create([{ provide: METADATA, useValue: metadata }, { provide: METADATA_PARAMETERS, useValue: parameters }], propertyKey)
    await new MultiMiddleware(handlers).handle(currentInjector)
}
export async function getPlatformModule(injector: Injector): Promise<Type<any>[]> {
    const platformModules = await Promise.all(injector.getMulti(PLATFORM_MODULE));
    return platformModules.map(module => {
        if (Array.isArray(module)) {
            return module;
        } else {
            return [module]
        }
    }).flat() as Type<any>[];
}
export async function processRootModule(type: Type<any>, root: Injector) {
    const platformModules = await getPlatformModule(root);
    const injector = await processModule(type, root, 'root', platformModules);
    return injector;
}

export async function processModule(type: Type<any>, root: Injector, name?: ProvideIn, modules?: Type<any>[]): Promise<Injector> {
    const def = getTypeDef(type);
    def.imports = [...modules || [], ...def.imports || []];
    const _providers = await createModuleProviders(def, root)
    // typeinjector
    const decorators = store.getDecorators(type)
    const { classes, properties, parameters } = decorators;
    const _parameters = (injector: Injector) => Promise.all((parameters || []).map(parameter => {
        const handlers = injector.getMulti<Decorator>(parameter.metadataKey)
        const parameterInjector = injector.create([{ provide: METADATA, useValue: parameter }]);
        return new MultiMiddleware(handlers).handle(parameterInjector);
    }));
    const injector = root.create([], name || type);
    const { controllers, imports, providers, id, dev } = def as ModuleDef;

    const removes = injector.use([
        ..._providers,
        { provide: NG_MODULE, useValue: type },
        {
            provide: INSTANCE,
            useFactory: (injector: Injector) => injector.get(type),
            deps: [Injector]
        }, {
            provide: DESIGN_TYPE,
            useFactory: () => getDesignType(type)
        }, {
            provide: DESIGN_PARAM_TYPES,
            useFactory: () => getDesignParamTypes(type) || []
        }, {
            provide: DESIGN_RETURN_TYPE,
            useFactory: () => getDesignReturnType(type)
        }, {
            provide: CLASS_METADATAS,
            useValue: classes
        },
        {
            provide: type,
            useFactory: (injector: Injector) => {
                const args = _parameters(injector);
                const paramTypes = injector.get(DESIGN_PARAM_TYPES)
                const _args = paramTypes.map((param, index) => {
                    if (Reflect.has(args, index)) {
                        return Reflect.get(args, index)
                    }
                    return injector.get(param)
                })
                return new type(..._args);
            },
            deps: [Injector]
        }
    ]);

    injector.use([{
        provide: UNINSTALL_HOOK,
        useValue: {
            type,
            removes
        },
        multi: true
    }], 'root');
    if (imports && imports.length > 0) {
        await Promise.all(imports.map(async imp => {
            if (isType(imp)) {
                await processModule(imp, injector);
            } else if (isModuleWidthProvider(imp)) {
                const { module, providers } = imp;
                await processModule(module, injector)
            } else if (imp) {
                // const { main, exportName } = imp;
                // const type = await import(main).then(res => Reflect.get(res, exportName));
                // await processModule(type, injector);
            }
        }))
    }
    await Promise.all((controllers || []).map(c => handlerType(injector, c)));
    // const typeInjector = await handlerType(injector, type);
    if (!!dev) {
        const watcher = injector.get(Watcher)
        if (!id) {
            throw new Error(`in dev module, module id must be __filename`)
        }
        if (watcher) {
            watcher.watch(id!, type)
        } else {
            console.log(`dev module is open, but not found watcher`)
        }
    }
    const instance = injector.get(type);
    if (isOnInit(instance)) {
        await instance.onInit(root);
    }
    if (isDoBootstrap(instance)) {
        await instance.doBootstrap(root);
    }
    return injector;
}

export async function createModuleProviders(def: ModuleDef, injector: Injector): Promise<StaticProvider<any>[]> {
    let _allProviders: StaticProvider<any>[] = [];
    const { controllers, imports, providers } = def || {};
    if (imports && imports.length > 0) {
        await Promise.all(imports.map(async imp => {
            if (isType(imp)) {
                const providers = await createModuleProviders(getTypeDef(imp), injector);
                _allProviders = [
                    ..._allProviders,
                    ...providers
                ]
            } else if (isModuleWidthProvider(imp)) {
                const { module, providers } = imp;
                const moduleProviders = await createModuleProviders(getTypeDef(module), injector)
                _allProviders = [
                    ..._allProviders,
                    ...moduleProviders,
                    ...providers
                ]
            } else {
                // const { main, exportName } = imp;
                // const type = await import(main).then(res => Reflect.get(res, exportName));
                // const providers = await createModuleProviders(getTypeDef(type), injector);
                // _allProviders = [
                //     ..._allProviders,
                //     ...providers
                // ]
            }
        }))
    }
    _allProviders = [
        ..._allProviders,
        ...providers || []
    ]
    return _allProviders;
}

export function getTypeDef(type: Type<any>) {
    if (!type) return {};
    const decorators = store.getDecorators(type)
    const { classes, properties, parameters } = decorators;
    const item = classes.find(it => it.metadataKey === ModuleMetadataKey);
    return item && item!.options || {};
}

export function isNgModuleType(type: Type<any>) {
    const decorators = store.getDecorators(type)
    const { classes, properties, parameters } = decorators;
    const item = classes.find(it => it.metadataKey === ModuleMetadataKey);
    return !!item;
}

export async function handlerType(injector: Injector, type: Type<any>) {
    const decorators = store.getDecorators(type)
    const { classes, properties, parameters } = decorators;
    const _parameters = (injector: Injector) => Promise.all((parameters || []).map(parameter => {
        const handlers = injector.getMulti<Decorator>(parameter.metadataKey)
        const parameterInjector = injector.create([{ provide: METADATA, useValue: parameter }], 'parameters');
        return new MultiMiddleware(handlers).handle(parameterInjector);
    }));
    const typeInjector = injector.create([], type);
    typeInjector.use([{
        provide: INSTANCE,
        useFactory: (injector: Injector) => injector.get(type),
        deps: [Injector]
    }, {
        provide: DESIGN_TYPE,
        useFactory: () => getDesignType(type)
    }, {
        provide: DESIGN_PARAM_TYPES,
        useFactory: () => getDesignParamTypes(type) || []
    }, {
        provide: DESIGN_RETURN_TYPE,
        useFactory: () => getDesignReturnType(type)
    }, {
        provide: CLASS_METADATAS,
        useValue: classes
    },
    {
        provide: type,
        useFactory: (injector: Injector) => {
            const args = _parameters(injector);
            const paramTypes = injector.get(DESIGN_PARAM_TYPES)
            const _args = paramTypes.map((param, index) => {
                if (Reflect.has(args, index)) {
                    return Reflect.get(args, index)
                }
                return injector.get(param)
            })
            return new type(..._args);
        },
        deps: [Injector]
    }]);
    await Promise.all([...classes].map(async (cls) => {
        const handlers = typeInjector.getMulti<Decorator>(cls.metadataKey);
        if (handlers && handlers.length > 0) {
            const _injector = typeInjector.create([
                { provide: METADATA, useValue: cls },
                { provide: METADATAS, useValue: decorators },
                { provide: CURRENT_TYPE, useValue: type },
            ]);
            await new MultiMiddleware(handlers).handle(_injector);
        }
    }));
    await Promise.all(properties.map(p => handlerMetadataMethodOrProperty(typeInjector, p)));
    return typeInjector;
}
