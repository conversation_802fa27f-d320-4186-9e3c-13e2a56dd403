c11:10:33 - Starting compilation in watch mode...


11:10:36 - Found 0 errors. Watching for file changes.
c19:42:54 - File change detected. Starting incremental compilation...

src/util.ts(37,23): error TS2304: Cannot find name 'PLATFORM_ASYNC_MODULE'.

19:42:57 - Found 1 error. Watching for file changes.
c19:43:00 - File change detected. Starting incremental compilation...


19:43:01 - Found 0 errors. Watching for file changes.
c19:43:12 - File change detected. Starting incremental compilation...


19:43:12 - Found 0 errors. Watching for file changes.
c19:43:16 - File change detected. Starting incremental compilation...


19:43:16 - Found 0 errors. Watching for file changes.
c19:43:25 - File change detected. Starting incremental compilation...


19:43:25 - Found 0 errors. Watching for file changes.
c19:43:27 - File change detected. Starting incremental compilation...


19:43:28 - Found 0 errors. Watching for file changes.
c19:43:28 - File change detected. Starting incremental compilation...


19:43:28 - Found 0 errors. Watching for file changes.
c19:43:33 - File change detected. Starting incremental compilation...


19:43:33 - Found 0 errors. Watching for file changes.
c19:45:14 - File change detected. Starting incremental compilation...

src/token.ts(38,1): error TS1005: '>' expected.

19:45:15 - Found 1 error. Watching for file changes.
c19:45:15 - File change detected. Starting incremental compilation...

src/token.ts(38,1): error TS1005: '>' expected.

19:45:15 - Found 1 error. Watching for file changes.
c19:45:27 - File change detected. Starting incremental compilation...

src/token.ts(38,1): error TS1005: '>' expected.

19:45:27 - Found 1 error. Watching for file changes.
c19:45:36 - File change detected. Starting incremental compilation...


19:45:36 - Found 0 errors. Watching for file changes.
c19:45:45 - File change detected. Starting incremental compilation...

src/util.ts(38,29): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is missing the following properties from type 'Type<any>': apply, call, bind, prototype, and 4 more.
src/util.ts(39,32): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is not assignable to type 'Type<any>'.

19:45:45 - Found 2 errors. Watching for file changes.
c19:45:46 - File change detected. Starting incremental compilation...

src/util.ts(38,29): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is missing the following properties from type 'Type<any>': apply, call, bind, prototype, and 4 more.
src/util.ts(39,32): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is not assignable to type 'Type<any>'.

19:45:46 - Found 2 errors. Watching for file changes.
c19:46:23 - File change detected. Starting incremental compilation...

src/util.ts(38,29): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is missing the following properties from type 'Type<any>': apply, call, bind, prototype, and 4 more.
src/util.ts(39,32): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is not assignable to type 'Type<any>'.

19:46:23 - Found 2 errors. Watching for file changes.
c19:46:42 - File change detected. Starting incremental compilation...

src/util.ts(38,29): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is missing the following properties from type 'Type<any>': apply, call, bind, prototype, and 4 more.
src/util.ts(39,32): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is not assignable to type 'Type<any>'.

19:46:42 - Found 2 errors. Watching for file changes.
c19:46:48 - File change detected. Starting incremental compilation...

src/util.ts(40,29): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is missing the following properties from type 'Type<any>': apply, call, bind, prototype, and 4 more.
src/util.ts(41,32): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is not assignable to type 'Type<any>'.

19:46:48 - Found 2 errors. Watching for file changes.
c19:47:14 - File change detected. Starting incremental compilation...

src/util.ts(47,29): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is missing the following properties from type 'Type<any>': apply, call, bind, prototype, and 4 more.
src/util.ts(48,32): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is not assignable to type 'Type<any>'.

19:47:15 - Found 2 errors. Watching for file changes.
c19:47:19 - File change detected. Starting incremental compilation...

src/util.ts(47,29): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is missing the following properties from type 'Type<any>': apply, call, bind, prototype, and 4 more.
src/util.ts(48,32): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is not assignable to type 'Type<any>'.

19:47:19 - Found 2 errors. Watching for file changes.
c19:47:22 - File change detected. Starting incremental compilation...

src/util.ts(47,29): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is missing the following properties from type 'Type<any>': apply, call, bind, prototype, and 4 more.
src/util.ts(48,32): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is not assignable to type 'Type<any>'.

19:47:23 - Found 2 errors. Watching for file changes.
c19:47:23 - File change detected. Starting incremental compilation...

src/util.ts(47,29): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is missing the following properties from type 'Type<any>': apply, call, bind, prototype, and 4 more.
src/util.ts(48,32): error TS2345: Argument of type 'Type<any> | Type<any>[]' is not assignable to parameter of type 'Type<any>'.
  Type 'Type<any>[]' is not assignable to type 'Type<any>'.

19:47:23 - Found 2 errors. Watching for file changes.
c19:47:36 - File change detected. Starting incremental compilation...


19:47:36 - Found 0 errors. Watching for file changes.
c20:19:32 - File change detected. Starting incremental compilation...


20:19:33 - Found 0 errors. Watching for file changes.
c20:19:50 - File change detected. Starting incremental compilation...

src/util.ts(10,17): error TS2323: Cannot redeclare exported variable 'toJson'.
src/util.ts(10,17): error TS2393: Duplicate function implementation.
src/util.ts(20,17): error TS2323: Cannot redeclare exported variable 'toJson'.
src/util.ts(20,17): error TS2393: Duplicate function implementation.

20:19:50 - Found 4 errors. Watching for file changes.
c20:19:55 - File change detected. Starting incremental compilation...


20:19:56 - Found 0 errors. Watching for file changes.
c20:19:56 - File change detected. Starting incremental compilation...


20:19:56 - Found 0 errors. Watching for file changes.
c20:19:59 - File change detected. Starting incremental compilation...

src/util.ts(14,14): error TS18004: No value exists in scope for the shorthand property 'msg'. Either declare one or provide an initializer.

20:20:00 - Found 1 error. Watching for file changes.
c20:20:03 - File change detected. Starting incremental compilation...


20:20:05 - Found 0 errors. Watching for file changes.
c20:20:11 - File change detected. Starting incremental compilation...


20:20:12 - Found 0 errors. Watching for file changes.
c20:20:12 - File change detected. Starting incremental compilation...


20:20:12 - Found 0 errors. Watching for file changes.
c20:26:01 - File change detected. Starting incremental compilation...


20:26:01 - Found 0 errors. Watching for file changes.
c20:26:15 - File change detected. Starting incremental compilation...


20:26:15 - Found 0 errors. Watching for file changes.
c20:26:22 - File change detected. Starting incremental compilation...


20:26:23 - Found 0 errors. Watching for file changes.
c20:26:27 - File change detected. Starting incremental compilation...


20:26:27 - Found 0 errors. Watching for file changes.
c20:26:46 - File change detected. Starting incremental compilation...


20:26:46 - Found 0 errors. Watching for file changes.
c20:26:46 - File change detected. Starting incremental compilation...


20:26:46 - Found 0 errors. Watching for file changes.
c20:32:35 - File change detected. Starting incremental compilation...

src/app.ts(11,9): error TS2322: Type 'Injector | NgerInjector' is not assignable to type 'Injector'.
  Type 'NgerInjector' is not assignable to type 'Injector'.
src/injector.ts(82,9): error TS2322: Type 'NgerInjector' is not assignable to type 'Injector'.
  Types of property 'getMulti' are incompatible.
    Type '<T>(token: Token<T>, to: any) => T[]' is not assignable to type '<T>(token: Token<T>) => T[]'.
src/injector.ts(82,44): error TS2345: Argument of type 'this' is not assignable to parameter of type 'Injector | undefined'.
  Type 'NgerInjector' is not assignable to type 'Injector'.
    Type 'this' is not assignable to type 'Injector'.
      Type 'NgerInjector' is not assignable to type 'Injector'.
        Types of property 'getMulti' are incompatible.
          Type '<T>(token: Token<T>, to: any) => T[]' is not assignable to type '<T>(token: Token<T>) => T[]'.
src/injector.ts(84,5): error TS2416: Property 'getMulti' in type 'NgerInjector' is not assignable to the same property in base type 'Injector'.
  Type '<T>(token: Token<T>, to: any) => T[]' is not assignable to type '<T>(token: Token<T>) => T[]'.
src/platform.ts(50,32): error TS2345: Argument of type 'NgerInjector' is not assignable to parameter of type 'Injector'.

20:32:36 - Found 5 errors. Watching for file changes.
c20:32:36 - File change detected. Starting incremental compilation...

src/app.ts(11,9): error TS2322: Type 'Injector | NgerInjector' is not assignable to type 'Injector'.
  Type 'NgerInjector' is not assignable to type 'Injector'.
src/injector.ts(82,9): error TS2322: Type 'NgerInjector' is not assignable to type 'Injector'.
  Types of property 'getMulti' are incompatible.
    Type '<T>(token: Token<T>, to: any) => T[]' is not assignable to type '<T>(token: Token<T>) => T[]'.
src/injector.ts(82,44): error TS2345: Argument of type 'this' is not assignable to parameter of type 'Injector | undefined'.
  Type 'NgerInjector' is not assignable to type 'Injector'.
    Type 'this' is not assignable to type 'Injector'.
      Type 'NgerInjector' is not assignable to type 'Injector'.
        Types of property 'getMulti' are incompatible.
          Type '<T>(token: Token<T>, to: any) => T[]' is not assignable to type '<T>(token: Token<T>) => T[]'.
src/injector.ts(84,5): error TS2416: Property 'getMulti' in type 'NgerInjector' is not assignable to the same property in base type 'Injector'.
  Type '<T>(token: Token<T>, to: any) => T[]' is not assignable to type '<T>(token: Token<T>) => T[]'.
src/platform.ts(50,32): error TS2345: Argument of type 'NgerInjector' is not assignable to parameter of type 'Injector'.

20:32:37 - Found 5 errors. Watching for file changes.
c20:32:40 - File change detected. Starting incremental compilation...


20:32:41 - Found 0 errors. Watching for file changes.
c20:33:31 - File change detected. Starting incremental compilation...


20:33:32 - Found 0 errors. Watching for file changes.
c20:33:54 - File change detected. Starting incremental compilation...


20:33:55 - Found 0 errors. Watching for file changes.
c20:33:55 - File change detected. Starting incremental compilation...


20:33:55 - Found 0 errors. Watching for file changes.
c20:34:02 - File change detected. Starting incremental compilation...


20:34:03 - Found 0 errors. Watching for file changes.
c20:34:03 - File change detected. Starting incremental compilation...


20:34:03 - Found 0 errors. Watching for file changes.
c20:34:13 - File change detected. Starting incremental compilation...


20:34:14 - Found 0 errors. Watching for file changes.
c20:34:14 - File change detected. Starting incremental compilation...


20:34:14 - Found 0 errors. Watching for file changes.
c20:34:24 - File change detected. Starting incremental compilation...


20:34:25 - Found 0 errors. Watching for file changes.
c20:34:25 - File change detected. Starting incremental compilation...


20:34:25 - Found 0 errors. Watching for file changes.
c20:41:44 - File change detected. Starting incremental compilation...


20:41:44 - Found 0 errors. Watching for file changes.
c20:42:20 - File change detected. Starting incremental compilation...


20:42:21 - Found 0 errors. Watching for file changes.
c20:47:48 - File change detected. Starting incremental compilation...


20:47:48 - Found 0 errors. Watching for file changes.
c20:47:55 - File change detected. Starting incremental compilation...


20:47:55 - Found 0 errors. Watching for file changes.
c20:47:56 - File change detected. Starting incremental compilation...


20:47:56 - Found 0 errors. Watching for file changes.
c20:47:59 - File change detected. Starting incremental compilation...


20:47:59 - Found 0 errors. Watching for file changes.
c20:48:55 - File change detected. Starting incremental compilation...

src/platform.ts(2,10): error TS2305: Module '"./cloud/app-manager"' has no exported member 'LoadAddonSystem'.

20:48:55 - Found 1 error. Watching for file changes.
c20:49:08 - File change detected. Starting incremental compilation...

src/platform.ts(2,10): error TS2305: Module '"./cloud/app-manager"' has no exported member 'LoadAddonSystem'.

20:49:09 - Found 1 error. Watching for file changes.
c20:49:12 - File change detected. Starting incremental compilation...

src/platform.ts(2,10): error TS2305: Module '"./cloud/app-manager"' has no exported member 'LoadAddonSystem'.

20:49:12 - Found 1 error. Watching for file changes.
c20:49:13 - File change detected. Starting incremental compilation...

src/platform.ts(2,10): error TS2305: Module '"./cloud/app-manager"' has no exported member 'LoadAddonSystem'.

20:49:13 - Found 1 error. Watching for file changes.
c20:49:14 - File change detected. Starting incremental compilation...

src/platform.ts(2,10): error TS2305: Module '"./cloud/app-manager"' has no exported member 'LoadAddonSystem'.

20:49:14 - Found 1 error. Watching for file changes.
c20:49:21 - File change detected. Starting incremental compilation...

src/platform.ts(87,42): error TS2304: Cannot find name 'LoadAddonSystem'.

20:49:21 - Found 1 error. Watching for file changes.
c20:49:26 - File change detected. Starting incremental compilation...


20:49:26 - Found 0 errors. Watching for file changes.
c20:50:29 - File change detected. Starting incremental compilation...


20:50:29 - Found 0 errors. Watching for file changes.
c20:55:26 - File change detected. Starting incremental compilation...


20:55:26 - Found 0 errors. Watching for file changes.
c20:55:43 - File change detected. Starting incremental compilation...


20:55:44 - Found 0 errors. Watching for file changes.
c20:58:37 - File change detected. Starting incremental compilation...


20:58:38 - Found 0 errors. Watching for file changes.
c20:59:13 - File change detected. Starting incremental compilation...


20:59:14 - Found 0 errors. Watching for file changes.
c20:59:46 - File change detected. Starting incremental compilation...


20:59:46 - Found 0 errors. Watching for file changes.
c20:59:50 - File change detected. Starting incremental compilation...


20:59:50 - Found 0 errors. Watching for file changes.
c20:59:50 - File change detected. Starting incremental compilation...


20:59:50 - Found 0 errors. Watching for file changes.
