{"apps": [{"name": "master", "script": "dist/master.js", "min_uptime": "200s", "max_restarts": 1000, "max_memory_restart": "2048M", "watch": false, "instances": 1, "env": {"NODE_ENV": "prod"}, "merge_logs": false, "combine_logs": false, "autorestart": true}, {"name": "consumer", "script": "dist/consumer.js", "min_uptime": "200s", "max_restarts": 1000, "max_memory_restart": "1024M", "watch": false, "instances": 3, "merge_logs": false, "combine_logs": false, "env": {"NODE_ENV": "prod"}, "autorestart": true}]}