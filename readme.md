```
ssh root@************** -p 25446
GkXVrnlt55zm
verdaccio
http://**************:4873/
pnpm i --registry=http://**************:4873
pnpm i superagent superagent-proxy socks-proxy-agent --registry=http://**************:4873
```


### 爬虫平台 crawling_platform
* id
* title
* name
* config
* total
* create_date
* update_date

### 爬虫关键字 crawling_keyword
* id
* keyword 关键字
* progress 进度【crawling_url进度】
* create_date
* update_date

## 爬虫任务 crawling_task
* id 唯一标识
* title 任务标题
* description 任务描述
* status -1 fail 0 wait 1 success 
* type `list`,`detail`,`comment`,`like`,`share`,`nlp`,`search`
* payload 附加数据
* create_date
* update_date
* run_date
* total 0 执行次数
* schedule '' 无定时任务



## 连接搜集
1. mq 消息中心
2. 


sudo docker run --name my-weibo-master -p 3000:3000 -e MQ_HOST=************* -e MQ_PORT=15672 -e MQ_USER=imeepos -e MQ_PASS=123qwe -e REDIS_URL=redis://localhost:6379 -e NLP_URL=http://*************:8081 -d weibo-master:v1.91


人民、新华、央视
澎湃、新京报、南方
新浪、搜狐、网易、凤凰、腾讯
今日头条、一点资讯、天天快报
趣头条
新浪网


新华社,南方周末,澎湃新闻,人民日报,南方都市报
36氪,鲜果

Zaker,今日头条

npm set registry http://localhost:4873/
npm adduser --registry http://localhost:4873/
npm profile set password --registry http://localhost:4873/

yarn config set registry http://localhost:4873/
// .yarnrc.yml
npmRegistryServer: "http://localhost:4873/"
unsafeHttpWhitelist:
- localhost:4873

pnpm set registry http://localhost:4873/
pnpm adduser --registry http://localhost:4873/
pnpm profile set password --registry http://localhost:4873/



人民、新华、央视、澎湃
新浪、搜狐




```ts
import urllib.request
 
url = 'https://weibo.com/u/7520422345'
 
headers = {
    # cookie中携带着你的登录信息  如果登录之后的cookie  那么我们就可以携带着cookie进入到任何页面
    'cookie': 'SINAGLOBAL=2294898703994.832.1635084315080; ULV=1640505725013:2:1:1:1876518562754.7322.1640505725006:1635084315166; SUBP=0033WrSXqPxfM725Ws9jqgMF55529P9D9W5dr4dlFfTvuiqf6A4ekXI05JpX5KMhUgL.FoMfeo5XS0qRehz2dJLoIEXLxK-LBo5L12qLxKqL1KqLBo.LxKqL1heLBoeLxK-LBo.LBozLxK.LBK-LB-Bt; ALF=1675437950; SSOLoginState=1643901951; SCF=AgK1PwWYJAlqYTBkpgwPwQFkXZnci0Rarnh0fobF2vNgAsocdLeMns-KsPVurq6SU9NSk7zKS25RCP9ZxNezKUE.; SUB=_2A25M_4OvDeRhGeFL6VIV9yjEyz6IHXVvjPJnrDV8PUNbmtANLXHnkW9NQkNykYY9FIeI0w91iVPtHRg8L7UJ0OQa; XSRF-TOKEN=70SbPiv7y1dIKYanvw2VSl0k; WBPSESS=kyTAq6c3qXeGLwO0O6zVtx1Rcz23WLek-mKU_qe7Q87M_tSFbXM0RxddXAKV4wXDJ-LSe-4YmpYT6qC21nMIEfuBQ0QdaJ8Hrjb4wRxmlc_pYC_Ze6KF_ZGytTag81PZW5IbUrZjbV5k3QeCBy43ZA==',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
    # referer  判断当前路径是不是由上一个路径进来的  一般情况下  用于图片的防盗链
    'referer': 'https://weibo.cn/'
}
 
# 请求对象的定制
request = urllib.request.Request(url=url,headers=headers)
 
# 模拟浏览器向服务器发送请求
response = urllib.request.urlopen(request)
 
# 获取响应的数据
content = response.read().decode('utf-8')
 
# 将数据保存到本地
with open ('weibo.html','w',encoding='utf-8') as fp:
    fp.write(content)
```

x 


192.168.2.127


2022年8月8日--系统升级

1. 优化爬虫爬取评论速度，速度提升两倍左右，每天可达600万评论左右
2. 优化下拉选择下拉数据过多造成的页面卡顿，现定位50条，获取更多数据，请输入关键字搜索
3. 搜索框增加操作符`为空`、`等于`、`不等于`、`不为空`、`开头`、`结尾`、`包含`、`大于`、`小于`、`大于等于`、`小于等于`等，便于灵活搜索想要数据
4. 优化页面打开速度，增加前端缓存，减少服务端压力
5. 修复城市列表加载数据不完整

2022年8月9日--系统升级
1. 长文本表格内折、省略号、复制等快捷功能
2. 添加时间搜索
3. 算法部署对接
4. `由于博主设置，目前内容暂不可见。` 任务一直卡住问题
5. 新增`评论文本长度`
6. 限制城市名称唯一，避免多人操作冲突


ssh
1. spacy
    - 分词、词性标注、词干化、命名实体识别、名词短语提取

## 踩坑了

*********** alma 123qwe

内网IP ************* （通过爬虫服务器访问）
root 123qwe

postgres 端口 15432
密码 pg123qwe

rabbitmq 是 15672  管理 18080 
guest、guest  

## 任务设计

1. 话题分页任务
> 根据关键字，话题列表搜索，每个任务负责某一页话题数据抓取，每条话题创建《话题任务》
2. 话题详情任务--话题详情连接
> 根据连接，抓取话题详情并入库，并创建《帖子分页任务》
3. 帖子分页任务
> 根据连接，每个任务负责某一页帖子抓取，每个帖子创建《帖子任务》
4. 帖子任务
> 根据连接，抓取帖子详情并入库， 并创建《评论抓取任务》
5. 评论抓取任务
> 根据要求抓取评论及子评论
6. 用户信息抓取

## 交付内容：
1. 首批测试数据（157个事件数据）（按提供数据结构表爬取并整理）；
2. 第二批数据（事件数据）
3. 微博、知乎及其他一个平台爬虫源码
4. 一个数据查询、处理调取的平台系统

## 交付时间：
### 事件数据：
1. 首批测试数据：2022年 8月2日前交付微博平台，其余平台8月5日交付
2. 第二批数据（事件数据）：2022年8月17日前（待定）
3. 爬虫源码：2022年8月22日前
4. 两个爬虫平台系统：2022年8月30日前
5. 整体（三个）2022年9月30日前

## 工作计划
### 2022/7/13
1. 了解项目需求，确定技术方案及可行性
2. 按照需求，构建五层数据库结构，并完成主流媒体数据批量导入数据库
3. 商讨算法及模型处理办法-外包专业算法团队
### 2022/7/14
1. 完成微博扫码登录
2. 根据关键字 抓取主题
### 2022/7/15
1. 根据主题抓取评论/子评论
2. 字数统计
### 2022/7/16
1. 创建消息队列，构建任务池
2. 多节点执行抓取任务
3. 拿到部分测试数据，对接算法
### 2022/7/17
1. 事件的增删改查页面，根据关键字下发抓取主题任务
2. 根据主题，下发抓取评论任务
### 2022/7/18
1. 登录及账号功能
2. 用户增删改查
### 2022/7/19
1. UI界面美化
2. 服务器环境到位--建议
### 2022/7/20 - 2022/7/23
1. 培训讲解
2. 上线试运行
3. 协助算法执行结果，进行入库存储
4. 根据反馈进行操作流程调整
### 2022/7/24-29
1. 根据问题优化
2. BUG及问题修复
3. 完成基础数据采集工作
### 2022/7/30--2025/7/30
1. 持续优化
