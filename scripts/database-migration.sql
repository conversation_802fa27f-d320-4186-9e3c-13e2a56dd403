-- 微博爬虫项目数据库迁移脚本
-- 版本: 2.0
-- 日期: 2024-01-01
-- 说明: 从 Node.js + @nger 迁移到 Rust + SeaORM

-- ============================================================================
-- 1. 备份现有数据
-- ============================================================================

-- 创建备份表
CREATE TABLE IF NOT EXISTS spilder_topic_backup AS SELECT * FROM spilder_topic WHERE 1=0;
CREATE TABLE IF NOT EXISTS spilder_topic_article_backup AS SELECT * FROM spilder_topic_article WHERE 1=0;
CREATE TABLE IF NOT EXISTS spilder_topic_article_comment_backup AS SELECT * FROM spilder_topic_article_comment WHERE 1=0;
CREATE TABLE IF NOT EXISTS spilder_account_backup AS SELECT * FROM spilder_account WHERE 1=0;

-- 执行备份
INSERT INTO spilder_topic_backup SELECT * FROM spilder_topic;
INSERT INTO spilder_topic_article_backup SELECT * FROM spilder_topic_article;
INSERT INTO spilder_topic_article_comment_backup SELECT * FROM spilder_topic_article_comment;
INSERT INTO spilder_account_backup SELECT * FROM spilder_account;

-- ============================================================================
-- 2. 平台管理表 (wb_platform) 扩展
-- ============================================================================

BEGIN;
    -- 添加新字段
    ALTER TABLE wb_platform ADD COLUMN IF NOT EXISTS api_config JSONB;
    ALTER TABLE wb_platform ADD COLUMN IF NOT EXISTS rate_limit INTEGER DEFAULT 1000;
    ALTER TABLE wb_platform ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
    ALTER TABLE wb_platform ADD COLUMN IF NOT EXISTS last_check_time TIMESTAMPTZ;
    ALTER TABLE wb_platform ADD COLUMN IF NOT EXISTS error_count INTEGER DEFAULT 0;
    
    -- 初始化数据
    UPDATE wb_platform SET is_active = true WHERE status = 1;
    UPDATE wb_platform SET is_active = false WHERE status = 0;
    UPDATE wb_platform SET last_check_time = NOW() WHERE id IS NOT NULL;
    
    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_wb_platform_name ON wb_platform(name);
    CREATE INDEX IF NOT EXISTS idx_wb_platform_status ON wb_platform(status);
    CREATE INDEX IF NOT EXISTS idx_wb_platform_active ON wb_platform(is_active);
COMMIT;

-- ============================================================================
-- 3. 事件管理表 (wb_event) 扩展
-- ============================================================================

BEGIN;
    -- 添加新字段
    ALTER TABLE wb_event ADD COLUMN IF NOT EXISTS status INTEGER DEFAULT 1;
    ALTER TABLE wb_event ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 5;
    ALTER TABLE wb_event ADD COLUMN IF NOT EXISTS start_time TIMESTAMPTZ;
    ALTER TABLE wb_event ADD COLUMN IF NOT EXISTS end_time TIMESTAMPTZ;
    ALTER TABLE wb_event ADD COLUMN IF NOT EXISTS auto_crawl BOOLEAN DEFAULT true;
    ALTER TABLE wb_event ADD COLUMN IF NOT EXISTS crawl_interval INTEGER DEFAULT 3600;
    
    -- 初始化数据
    UPDATE wb_event SET status = 1 WHERE id IS NOT NULL;
    UPDATE wb_event SET priority = 5 WHERE id IS NOT NULL;
    UPDATE wb_event SET auto_crawl = true WHERE id IS NOT NULL;
    
    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_wb_event_status ON wb_event(status);
    CREATE INDEX IF NOT EXISTS idx_wb_event_priority ON wb_event(priority);
    CREATE INDEX IF NOT EXISTS idx_wb_event_time_range ON wb_event(start_time, end_time);
COMMIT;

-- ============================================================================
-- 4. 话题表 (spilder_topic) 扩展
-- ============================================================================

BEGIN;
    -- 添加新字段
    ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS crawl_status INTEGER DEFAULT 0;
    ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS last_crawl_time TIMESTAMPTZ;
    ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS next_crawl_time TIMESTAMPTZ;
    ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS crawl_count INTEGER DEFAULT 0;
    ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS error_count INTEGER DEFAULT 0;
    ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS is_trending BOOLEAN DEFAULT false;
    ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS sentiment_score FLOAT;
    ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS article_count INTEGER DEFAULT 0;
    ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS comment_count INTEGER DEFAULT 0;
    ALTER TABLE spilder_topic ADD COLUMN IF NOT EXISTS hash_tags TEXT[];
    
    -- 初始化数据
    UPDATE spilder_topic SET crawl_status = 2 WHERE id IS NOT NULL; -- 标记为已完成
    UPDATE spilder_topic SET last_crawl_time = create_date WHERE create_date IS NOT NULL;
    UPDATE spilder_topic SET crawl_count = 1 WHERE id IS NOT NULL;
    
    -- 更新统计数据
    UPDATE spilder_topic SET article_count = (
        SELECT COUNT(*) FROM spilder_topic_article WHERE tid = spilder_topic.id
    );
    UPDATE spilder_topic SET comment_count = (
        SELECT COUNT(*) FROM spilder_topic_article_comment WHERE tid = spilder_topic.id
    );
    
    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_spilder_topic_platform ON spilder_topic(platform);
    CREATE INDEX IF NOT EXISTS idx_spilder_topic_eid ON spilder_topic(eid);
    CREATE INDEX IF NOT EXISTS idx_spilder_topic_status ON spilder_topic(crawl_status);
    CREATE INDEX IF NOT EXISTS idx_spilder_topic_trending ON spilder_topic(is_trending);
    CREATE INDEX IF NOT EXISTS idx_spilder_topic_create_date ON spilder_topic(create_date);
    CREATE INDEX IF NOT EXISTS idx_spilder_topic_read_count ON spilder_topic(read_count);
COMMIT;

-- ============================================================================
-- 5. 文章表 (spilder_topic_article) 扩展
-- ============================================================================

BEGIN;
    -- 添加新字段
    ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS crawl_status INTEGER DEFAULT 0;
    ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS last_crawl_time TIMESTAMPTZ;
    ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS sentiment_score FLOAT;
    ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS readability_score FLOAT;
    ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS word_count INTEGER DEFAULT 0;
    ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS is_original BOOLEAN DEFAULT true;
    ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS parent_id INTEGER;
    ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS media_urls TEXT[];
    ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS mentions TEXT[];
    ALTER TABLE spilder_topic_article ADD COLUMN IF NOT EXISTS hashtags TEXT[];
    
    -- 初始化数据
    UPDATE spilder_topic_article SET crawl_status = 2 WHERE id IS NOT NULL;
    UPDATE spilder_topic_article SET last_crawl_time = create_date WHERE create_date IS NOT NULL;
    UPDATE spilder_topic_article SET word_count = LENGTH(text_raw) / 5 WHERE text_raw IS NOT NULL;
    
    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_spilder_topic_article_tid ON spilder_topic_article(tid);
    CREATE INDEX IF NOT EXISTS idx_spilder_topic_article_eid ON spilder_topic_article(eid);
    CREATE INDEX IF NOT EXISTS idx_spilder_topic_article_uid ON spilder_topic_article(uid);
    CREATE INDEX IF NOT EXISTS idx_spilder_topic_article_create_at ON spilder_topic_article(create_at);
    CREATE INDEX IF NOT EXISTS idx_spilder_topic_article_status ON spilder_topic_article(crawl_status);
    CREATE INDEX IF NOT EXISTS idx_spilder_topic_article_sentiment ON spilder_topic_article(sentiment_score);
COMMIT;

-- ============================================================================
-- 6. 评论表 (spilder_topic_article_comment) 扩展
-- ============================================================================

BEGIN;
    -- 添加新字段
    ALTER TABLE spilder_topic_article_comment ADD COLUMN IF NOT EXISTS crawl_status INTEGER DEFAULT 0;
    ALTER TABLE spilder_topic_article_comment ADD COLUMN IF NOT EXISTS last_crawl_time TIMESTAMPTZ;
    ALTER TABLE spilder_topic_article_comment ADD COLUMN IF NOT EXISTS sentiment_score FLOAT;
    ALTER TABLE spilder_topic_article_comment ADD COLUMN IF NOT EXISTS word_count INTEGER DEFAULT 0;
    ALTER TABLE spilder_topic_article_comment ADD COLUMN IF NOT EXISTS reply_count INTEGER DEFAULT 0;
    ALTER TABLE spilder_topic_article_comment ADD COLUMN IF NOT EXISTS depth INTEGER DEFAULT 0;
    
    -- 初始化数据
    UPDATE spilder_topic_article_comment SET crawl_status = 2 WHERE id IS NOT NULL;
    UPDATE spilder_topic_article_comment SET last_crawl_time = created_at WHERE created_at IS NOT NULL;
    UPDATE spilder_topic_article_comment SET word_count = LENGTH(text) / 5 WHERE text IS NOT NULL;
    UPDATE spilder_topic_article_comment SET depth = 0 WHERE pid IS NULL;
    UPDATE spilder_topic_article_comment SET depth = 1 WHERE pid IS NOT NULL;
    
    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_spilder_comment_aid ON spilder_topic_article_comment(aid);
    CREATE INDEX IF NOT EXISTS idx_spilder_comment_tid ON spilder_topic_article_comment(tid);
    CREATE INDEX IF NOT EXISTS idx_spilder_comment_eid ON spilder_topic_article_comment(eid);
    CREATE INDEX IF NOT EXISTS idx_spilder_comment_uid ON spilder_topic_article_comment(uid);
    CREATE INDEX IF NOT EXISTS idx_spilder_comment_pid ON spilder_topic_article_comment(pid);
    CREATE INDEX IF NOT EXISTS idx_spilder_comment_created_at ON spilder_topic_article_comment(created_at);
    CREATE INDEX IF NOT EXISTS idx_spilder_comment_status ON spilder_topic_article_comment(crawl_status);
COMMIT;

-- ============================================================================
-- 7. 账号表 (spilder_account) 扩展
-- ============================================================================

BEGIN;
    -- 添加新字段
    ALTER TABLE spilder_account ADD COLUMN IF NOT EXISTS last_active_time TIMESTAMPTZ;
    ALTER TABLE spilder_account ADD COLUMN IF NOT EXISTS influence_score FLOAT DEFAULT 0;
    ALTER TABLE spilder_account ADD COLUMN IF NOT EXISTS activity_level INTEGER DEFAULT 1;
    ALTER TABLE spilder_account ADD COLUMN IF NOT EXISTS is_bot BOOLEAN DEFAULT false;
    ALTER TABLE spilder_account ADD COLUMN IF NOT EXISTS account_age INTEGER;
    ALTER TABLE spilder_account ADD COLUMN IF NOT EXISTS avg_posts_per_day FLOAT DEFAULT 0;
    
    -- 初始化数据
    UPDATE spilder_account SET last_active_time = NOW() WHERE id IS NOT NULL;
    UPDATE spilder_account SET activity_level = 1 WHERE id IS NOT NULL;
    
    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_spilder_account_uid_platform ON spilder_account(uid, platform);
    CREATE INDEX IF NOT EXISTS idx_spilder_account_platform ON spilder_account(platform);
    CREATE INDEX IF NOT EXISTS idx_spilder_account_type ON spilder_account(type);
    CREATE INDEX IF NOT EXISTS idx_spilder_account_influence ON spilder_account(influence_score);
COMMIT;

-- ============================================================================
-- 8. 创建新的系统表
-- ============================================================================

-- 爬虫任务表
CREATE TABLE IF NOT EXISTS spider_crawl_tasks (
    id SERIAL PRIMARY KEY,
    task_type VARCHAR(50) NOT NULL,
    task_name VARCHAR(255) NOT NULL,
    platform VARCHAR(50) NOT NULL,
    target_id INTEGER,
    target_url TEXT,
    priority INTEGER DEFAULT 5,
    status INTEGER DEFAULT 0,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    scheduled_time TIMESTAMPTZ DEFAULT NOW(),
    started_time TIMESTAMPTZ,
    completed_time TIMESTAMPTZ,
    error_message TEXT,
    result_data JSONB,
    config_data JSONB,
    created_date TIMESTAMPTZ DEFAULT NOW(),
    updated_date TIMESTAMPTZ DEFAULT NOW()
);

-- 爬虫日志表
CREATE TABLE IF NOT EXISTS spider_crawl_logs (
    id SERIAL PRIMARY KEY,
    task_id INTEGER REFERENCES spider_crawl_tasks(id),
    log_level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    details JSONB,
    created_date TIMESTAMPTZ DEFAULT NOW()
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value TEXT,
    config_type VARCHAR(50) DEFAULT 'string',
    description TEXT,
    is_encrypted BOOLEAN DEFAULT false,
    created_date TIMESTAMPTZ DEFAULT NOW(),
    updated_date TIMESTAMPTZ DEFAULT NOW()
);

-- 数据统计表
CREATE TABLE IF NOT EXISTS data_statistics (
    id SERIAL PRIMARY KEY,
    stat_date DATE NOT NULL,
    platform VARCHAR(50) NOT NULL,
    event_id INTEGER,
    topic_count INTEGER DEFAULT 0,
    article_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    user_count INTEGER DEFAULT 0,
    avg_sentiment FLOAT DEFAULT 0,
    total_interactions BIGINT DEFAULT 0,
    created_date TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(stat_date, platform, event_id)
);

-- ============================================================================
-- 9. 创建索引
-- ============================================================================

-- 任务表索引
CREATE INDEX IF NOT EXISTS idx_spider_tasks_type ON spider_crawl_tasks(task_type);
CREATE INDEX IF NOT EXISTS idx_spider_tasks_platform ON spider_crawl_tasks(platform);
CREATE INDEX IF NOT EXISTS idx_spider_tasks_status ON spider_crawl_tasks(status);
CREATE INDEX IF NOT EXISTS idx_spider_tasks_priority ON spider_crawl_tasks(priority);
CREATE INDEX IF NOT EXISTS idx_spider_tasks_scheduled ON spider_crawl_tasks(scheduled_time);

-- 日志表索引
CREATE INDEX IF NOT EXISTS idx_spider_logs_task_id ON spider_crawl_logs(task_id);
CREATE INDEX IF NOT EXISTS idx_spider_logs_level ON spider_crawl_logs(log_level);
CREATE INDEX IF NOT EXISTS idx_spider_logs_created ON spider_crawl_logs(created_date);

-- 配置表索引
CREATE INDEX IF NOT EXISTS idx_system_configs_key ON system_configs(config_key);

-- 统计表索引
CREATE INDEX IF NOT EXISTS idx_data_stats_date ON data_statistics(stat_date);
CREATE INDEX IF NOT EXISTS idx_data_stats_platform ON data_statistics(platform);
CREATE INDEX IF NOT EXISTS idx_data_stats_event ON data_statistics(event_id);

-- ============================================================================
-- 10. 创建复合索引
-- ============================================================================

-- 话题表复合索引
CREATE INDEX IF NOT EXISTS idx_topic_platform_eid_status ON spilder_topic(platform, eid, crawl_status);
CREATE INDEX IF NOT EXISTS idx_topic_create_date_platform ON spilder_topic(create_date, platform);

-- 文章表复合索引
CREATE INDEX IF NOT EXISTS idx_article_tid_create_at ON spilder_topic_article(tid, create_at);
CREATE INDEX IF NOT EXISTS idx_article_platform_uid ON spilder_topic_article(platform, uid);

-- 评论表复合索引
CREATE INDEX IF NOT EXISTS idx_comment_aid_created_at ON spilder_topic_article_comment(aid, created_at);
CREATE INDEX IF NOT EXISTS idx_comment_platform_uid ON spilder_topic_article_comment(platform, uid);

-- ============================================================================
-- 11. 创建全文搜索索引
-- ============================================================================

-- 为文本字段创建全文搜索索引
CREATE INDEX IF NOT EXISTS idx_topic_title_fts ON spilder_topic USING gin(to_tsvector('chinese', title));
CREATE INDEX IF NOT EXISTS idx_article_text_fts ON spilder_topic_article USING gin(to_tsvector('chinese', text_raw));
CREATE INDEX IF NOT EXISTS idx_comment_text_fts ON spilder_topic_article_comment USING gin(to_tsvector('chinese', text));

-- ============================================================================
-- 12. 数据完整性验证
-- ============================================================================

-- 验证迁移结果
DO $$
DECLARE
    topic_count INTEGER;
    article_count INTEGER;
    comment_count INTEGER;
    account_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO topic_count FROM spilder_topic;
    SELECT COUNT(*) INTO article_count FROM spilder_topic_article;
    SELECT COUNT(*) INTO comment_count FROM spilder_topic_article_comment;
    SELECT COUNT(*) INTO account_count FROM spilder_account;
    
    RAISE NOTICE '数据迁移完成统计:';
    RAISE NOTICE '话题数量: %', topic_count;
    RAISE NOTICE '文章数量: %', article_count;
    RAISE NOTICE '评论数量: %', comment_count;
    RAISE NOTICE '账号数量: %', account_count;
    
    -- 验证新字段是否正确添加
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'spilder_topic' AND column_name = 'crawl_status') THEN
        RAISE EXCEPTION '话题表新字段添加失败';
    END IF;
    
    RAISE NOTICE '数据库迁移验证通过!';
END $$;
