-- 微博爬虫项目数据库回滚脚本
-- 版本: 2.0
-- 日期: 2024-01-01
-- 说明: 回滚数据库迁移，恢复到原始状态

-- ============================================================================
-- 警告: 此脚本将删除所有新增的字段和表，请谨慎使用！
-- ============================================================================

-- 确认回滚操作
DO $$
BEGIN
    RAISE NOTICE '开始执行数据库回滚操作...';
    RAISE NOTICE '警告: 此操作将删除所有新增的字段和表!';
END $$;

-- ============================================================================
-- 1. 删除新创建的系统表
-- ============================================================================

-- 删除数据统计表
DROP TABLE IF EXISTS data_statistics CASCADE;

-- 删除系统配置表
DROP TABLE IF EXISTS system_configs CASCADE;

-- 删除爬虫日志表
DROP TABLE IF EXISTS spider_crawl_logs CASCADE;

-- 删除爬虫任务表
DROP TABLE IF EXISTS spider_crawl_tasks CASCADE;

-- ============================================================================
-- 2. 删除全文搜索索引
-- ============================================================================

DROP INDEX IF EXISTS idx_topic_title_fts;
DROP INDEX IF EXISTS idx_article_text_fts;
DROP INDEX IF EXISTS idx_comment_text_fts;

-- ============================================================================
-- 3. 删除复合索引
-- ============================================================================

-- 话题表复合索引
DROP INDEX IF EXISTS idx_topic_platform_eid_status;
DROP INDEX IF EXISTS idx_topic_create_date_platform;

-- 文章表复合索引
DROP INDEX IF EXISTS idx_article_tid_create_at;
DROP INDEX IF EXISTS idx_article_platform_uid;

-- 评论表复合索引
DROP INDEX IF EXISTS idx_comment_aid_created_at;
DROP INDEX IF EXISTS idx_comment_platform_uid;

-- ============================================================================
-- 4. 回滚账号表 (spilder_account)
-- ============================================================================

BEGIN;
    -- 删除新增索引
    DROP INDEX IF EXISTS idx_spilder_account_uid_platform;
    DROP INDEX IF EXISTS idx_spilder_account_platform;
    DROP INDEX IF EXISTS idx_spilder_account_type;
    DROP INDEX IF EXISTS idx_spilder_account_influence;
    
    -- 删除新增字段
    ALTER TABLE spilder_account DROP COLUMN IF EXISTS last_active_time;
    ALTER TABLE spilder_account DROP COLUMN IF EXISTS influence_score;
    ALTER TABLE spilder_account DROP COLUMN IF EXISTS activity_level;
    ALTER TABLE spilder_account DROP COLUMN IF EXISTS is_bot;
    ALTER TABLE spilder_account DROP COLUMN IF EXISTS account_age;
    ALTER TABLE spilder_account DROP COLUMN IF EXISTS avg_posts_per_day;
COMMIT;

-- ============================================================================
-- 5. 回滚评论表 (spilder_topic_article_comment)
-- ============================================================================

BEGIN;
    -- 删除新增索引
    DROP INDEX IF EXISTS idx_spilder_comment_aid;
    DROP INDEX IF EXISTS idx_spilder_comment_tid;
    DROP INDEX IF EXISTS idx_spilder_comment_eid;
    DROP INDEX IF EXISTS idx_spilder_comment_uid;
    DROP INDEX IF EXISTS idx_spilder_comment_pid;
    DROP INDEX IF EXISTS idx_spilder_comment_created_at;
    DROP INDEX IF EXISTS idx_spilder_comment_status;
    
    -- 删除新增字段
    ALTER TABLE spilder_topic_article_comment DROP COLUMN IF EXISTS crawl_status;
    ALTER TABLE spilder_topic_article_comment DROP COLUMN IF EXISTS last_crawl_time;
    ALTER TABLE spilder_topic_article_comment DROP COLUMN IF EXISTS sentiment_score;
    ALTER TABLE spilder_topic_article_comment DROP COLUMN IF EXISTS word_count;
    ALTER TABLE spilder_topic_article_comment DROP COLUMN IF EXISTS reply_count;
    ALTER TABLE spilder_topic_article_comment DROP COLUMN IF EXISTS depth;
COMMIT;

-- ============================================================================
-- 6. 回滚文章表 (spilder_topic_article)
-- ============================================================================

BEGIN;
    -- 删除新增索引
    DROP INDEX IF EXISTS idx_spilder_topic_article_tid;
    DROP INDEX IF EXISTS idx_spilder_topic_article_eid;
    DROP INDEX IF EXISTS idx_spilder_topic_article_uid;
    DROP INDEX IF EXISTS idx_spilder_topic_article_create_at;
    DROP INDEX IF EXISTS idx_spilder_topic_article_status;
    DROP INDEX IF EXISTS idx_spilder_topic_article_sentiment;
    
    -- 删除新增字段
    ALTER TABLE spilder_topic_article DROP COLUMN IF EXISTS crawl_status;
    ALTER TABLE spilder_topic_article DROP COLUMN IF EXISTS last_crawl_time;
    ALTER TABLE spilder_topic_article DROP COLUMN IF EXISTS sentiment_score;
    ALTER TABLE spilder_topic_article DROP COLUMN IF EXISTS readability_score;
    ALTER TABLE spilder_topic_article DROP COLUMN IF EXISTS word_count;
    ALTER TABLE spilder_topic_article DROP COLUMN IF EXISTS is_original;
    ALTER TABLE spilder_topic_article DROP COLUMN IF EXISTS parent_id;
    ALTER TABLE spilder_topic_article DROP COLUMN IF EXISTS media_urls;
    ALTER TABLE spilder_topic_article DROP COLUMN IF EXISTS mentions;
    ALTER TABLE spilder_topic_article DROP COLUMN IF EXISTS hashtags;
COMMIT;

-- ============================================================================
-- 7. 回滚话题表 (spilder_topic)
-- ============================================================================

BEGIN;
    -- 删除新增索引
    DROP INDEX IF EXISTS idx_spilder_topic_platform;
    DROP INDEX IF EXISTS idx_spilder_topic_eid;
    DROP INDEX IF EXISTS idx_spilder_topic_status;
    DROP INDEX IF EXISTS idx_spilder_topic_trending;
    DROP INDEX IF EXISTS idx_spilder_topic_create_date;
    DROP INDEX IF EXISTS idx_spilder_topic_read_count;
    
    -- 删除新增字段
    ALTER TABLE spilder_topic DROP COLUMN IF EXISTS crawl_status;
    ALTER TABLE spilder_topic DROP COLUMN IF EXISTS last_crawl_time;
    ALTER TABLE spilder_topic DROP COLUMN IF EXISTS next_crawl_time;
    ALTER TABLE spilder_topic DROP COLUMN IF EXISTS crawl_count;
    ALTER TABLE spilder_topic DROP COLUMN IF EXISTS error_count;
    ALTER TABLE spilder_topic DROP COLUMN IF EXISTS is_trending;
    ALTER TABLE spilder_topic DROP COLUMN IF EXISTS sentiment_score;
    ALTER TABLE spilder_topic DROP COLUMN IF EXISTS article_count;
    ALTER TABLE spilder_topic DROP COLUMN IF EXISTS comment_count;
    ALTER TABLE spilder_topic DROP COLUMN IF EXISTS hash_tags;
COMMIT;

-- ============================================================================
-- 8. 回滚事件管理表 (wb_event)
-- ============================================================================

BEGIN;
    -- 删除新增索引
    DROP INDEX IF EXISTS idx_wb_event_status;
    DROP INDEX IF EXISTS idx_wb_event_priority;
    DROP INDEX IF EXISTS idx_wb_event_time_range;
    
    -- 删除新增字段
    ALTER TABLE wb_event DROP COLUMN IF EXISTS status;
    ALTER TABLE wb_event DROP COLUMN IF EXISTS priority;
    ALTER TABLE wb_event DROP COLUMN IF EXISTS start_time;
    ALTER TABLE wb_event DROP COLUMN IF EXISTS end_time;
    ALTER TABLE wb_event DROP COLUMN IF EXISTS auto_crawl;
    ALTER TABLE wb_event DROP COLUMN IF EXISTS crawl_interval;
COMMIT;

-- ============================================================================
-- 9. 回滚平台管理表 (wb_platform)
-- ============================================================================

BEGIN;
    -- 删除新增索引
    DROP INDEX IF EXISTS idx_wb_platform_name;
    DROP INDEX IF EXISTS idx_wb_platform_status;
    DROP INDEX IF EXISTS idx_wb_platform_active;
    
    -- 删除新增字段
    ALTER TABLE wb_platform DROP COLUMN IF EXISTS api_config;
    ALTER TABLE wb_platform DROP COLUMN IF EXISTS rate_limit;
    ALTER TABLE wb_platform DROP COLUMN IF EXISTS is_active;
    ALTER TABLE wb_platform DROP COLUMN IF EXISTS last_check_time;
    ALTER TABLE wb_platform DROP COLUMN IF EXISTS error_count;
COMMIT;

-- ============================================================================
-- 10. 删除备份表 (可选)
-- ============================================================================

-- 注意: 如果需要保留备份数据，请注释掉以下语句
-- DROP TABLE IF EXISTS spilder_topic_backup;
-- DROP TABLE IF EXISTS spilder_topic_article_backup;
-- DROP TABLE IF EXISTS spilder_topic_article_comment_backup;
-- DROP TABLE IF EXISTS spilder_account_backup;

-- ============================================================================
-- 11. 验证回滚结果
-- ============================================================================

DO $$
DECLARE
    topic_count INTEGER;
    article_count INTEGER;
    comment_count INTEGER;
    account_count INTEGER;
    new_table_count INTEGER;
BEGIN
    -- 统计原有表的记录数
    SELECT COUNT(*) INTO topic_count FROM spilder_topic;
    SELECT COUNT(*) INTO article_count FROM spilder_topic_article;
    SELECT COUNT(*) INTO comment_count FROM spilder_topic_article_comment;
    SELECT COUNT(*) INTO account_count FROM spilder_account;
    
    -- 检查新表是否已删除
    SELECT COUNT(*) INTO new_table_count 
    FROM information_schema.tables 
    WHERE table_name IN ('spider_crawl_tasks', 'spider_crawl_logs', 'system_configs', 'data_statistics');
    
    RAISE NOTICE '数据库回滚完成统计:';
    RAISE NOTICE '话题数量: %', topic_count;
    RAISE NOTICE '文章数量: %', article_count;
    RAISE NOTICE '评论数量: %', comment_count;
    RAISE NOTICE '账号数量: %', account_count;
    RAISE NOTICE '新表数量: % (应为0)', new_table_count;
    
    -- 验证新字段是否已删除
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'spilder_topic' AND column_name = 'crawl_status') THEN
        RAISE WARNING '警告: 话题表新字段未完全删除';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'spilder_topic_article' AND column_name = 'crawl_status') THEN
        RAISE WARNING '警告: 文章表新字段未完全删除';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'spilder_topic_article_comment' AND column_name = 'crawl_status') THEN
        RAISE WARNING '警告: 评论表新字段未完全删除';
    END IF;
    
    IF new_table_count = 0 THEN
        RAISE NOTICE '数据库回滚验证通过!';
    ELSE
        RAISE WARNING '警告: 部分新表未完全删除';
    END IF;
END $$;

-- ============================================================================
-- 12. 清理操作
-- ============================================================================

-- 更新表统计信息
ANALYZE spilder_topic;
ANALYZE spilder_topic_article;
ANALYZE spilder_topic_article_comment;
ANALYZE spilder_account;
ANALYZE wb_platform;
ANALYZE wb_event;

-- 清理无用的统计信息
SELECT pg_stat_reset();

RAISE NOTICE '数据库回滚操作完成!';
RAISE NOTICE '建议重启应用程序以确保更改生效。';
