FROM python:3.7.13
RUN apt-get update
RUN apt-get install -y libgl1-mesa-dev ffmpeg libsm6 libxext6
WORKDIR /usr/src/app
COPY . .
RUN python -m pip install --upgrade pip
RUN python -m pip install opencv-python opencv-python-headless -i https://pypi.tuna.tsinghua.edu.cn/simple
RUN python -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

EXPOSE 8082

CMD ["python","api.py"]