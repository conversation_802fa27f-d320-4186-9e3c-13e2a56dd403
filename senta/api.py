from flask import Flask, request
from flask_cors import CORS, cross_origin
from cut import cut, TAG_TITLE
from senta import senta
import json

app = Flask(__name__)
cors = CORS(app)

@app.route('/')
@cross_origin()
def index():
    return 'welcome to use nger api!'

@app.route('/tag_titles')
@cross_origin()
def get_tag_titles():
    return TAG_TITLE

@app.route('/cut', methods=["POST"])
@cross_origin()
def http_cut():
    try:
        form = request.json
        texts = form['texts']
        return cut(texts=texts)
    except Exception as e:
        print(e)
        return {}

@app.route('/senta', methods=["POST"])
@cross_origin()
def http_senta():
    try:
        form = request.json
        texts = form['texts']
        return senta(texts)
    except Exception as e:
        print(e)
        return {}

app.run(host='0.0.0.0', port=8082)
