import paddlehub as hub

ernie_gen_lover_words = hub.<PERSON><PERSON><PERSON>(name="ernie_gen_lover_words")

test_texts = ['情人节', '故乡', '小编带大家了解一下程序员情人节']
results = ernie_gen_lover_words.generate(texts=test_texts, use_gpu=True, beam_width=5)
for result in results:
    print(result)


ernie_gen_poetry = hub.Mo<PERSON><PERSON>(name="ernie_gen_poetry")

test_texts = ['昔年旅南服，始识王荆州。', '高名出汉阴，禅阁跨香岑。']
results = ernie_gen_poetry.generate(texts=test_texts, use_gpu=True, beam_width=5)
for result in results:
    print(result)

import paddlehub as hub

# 在模型定义时，可以通过设置line=4或8指定输出绝句或律诗，设置word=5或7指定输出五言或七言。
# 默认line=4, word=7 即输出七言绝句。
ernie_gen_acrostic_poetry = hub.<PERSON><PERSON><PERSON>(name="ernie_gen_acrostic_poetry", line=4, word=7)

test_texts = ['我喜欢你']
results = ernie_gen_acrostic_poetry.generate(texts=test_texts, use_gpu=True, beam_width=5)
for result in results:
    print(result)


import paddlehub as hub

ernie_gen_couplet = hub.Module(name="ernie_gen_couplet")

test_texts = ["人增福寿年增岁", "风吹云乱天垂泪"]
results = ernie_gen_couplet.generate(texts=test_texts, use_gpu=True, beam_width=5)
for result in results:
    print(result)

    