import os

class Dispose(object):
    def dispose(self):
        ...

class Config(object):
    def __init__(self) -> None:
        self.env = os.environ

    @staticmethod
    def set(name: str, val: str):
        if val is None:
            return
        os.environ.putenv(name, val)
    
    def get(self, name: str, default: str = None):
        val = self.env.get(name)
        if val is not None:
            return val
        return default
