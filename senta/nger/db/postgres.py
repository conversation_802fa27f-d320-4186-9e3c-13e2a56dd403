from nger.core import Dispose, Config
import psycopg2
import psycopg2.errors

class Postgres(Dispose):

    def __init__(self, config: Config):
        host = config.get('POSTGRES_HOST', '*************')
        # host = config.get('POSTGRES_HOST', '***************')
        # port = config.get('POSTGRES_PORT', '5432')
        port = config.get('POSTGRES_PORT', '15432')
        # username = config.get('POSTGRES_USERNAME', 'imeepos')
        username = config.get('POSTGRES_USERNAME', 'user')
        # password = config.get('POSTGRES_PASSWORD', '123qwe')
        password = config.get('POSTGRES_PASSWORD', 'mm123qwe')
        database = config.get('POSTGRES_DATABASE', 'weibo')
        self.connection = psycopg2.connect(database=database, user=username, password=password, host=host, port=port)

    def dispose(self):
        self.connection.close()

    def query(self, sql: str):
        try:
            cursor = self.connection.cursor()
            cursor.execute(sql)
        except psycopg2.errors.UniqueViolation as e:
            self.connection.rollback()
        finally:
            cursor.close()

    def fetchone(self, sql: str):
        cursor = self.connection.cursor()
        cursor.execute(sql)
        item = cursor.fetchone()
        cursor.close()
        return item
    
    def fetchmany(self, sql: str, size: int):
        cursor = self.connection.cursor()
        cursor.execute(sql)
        item = cursor.fetchmany(size)
        cursor.close()
        return item

    def fetchall(self, sql: str):
        cursor = self.connection.cursor()
        cursor.execute(sql)
        item = cursor.fetchall()
        cursor.close()
        return item
    
    @staticmethod
    def create():
        config = Config()
        return Postgres(config)