import matplotlib.pyplot as plt
import numpy as np
from asyncore import write
import pandas as pd



fig = plt.figure()

with open('data/2022-10-17-count.csv') as v:
    lines = v.readlines()
    for line in lines:
        list = line.split(',')
        for l in list:
            print(str(l).strip())


# month = df.iloc[:,0].unique()

# for m in month:
#     ax = fig.add_subplot()
#     x = []
#     y = []
#     ax.plot(x, y)

# plt.grid()
# plt.show()
# df_n = pd.DataFrame({

# })
# filename = '9.19-pd.xlsx'
# writer = pd.ExcelWriter(filename)
# df_n.to_excel(writer,sheet_name='政府公告')
# writer.save()

# x = []