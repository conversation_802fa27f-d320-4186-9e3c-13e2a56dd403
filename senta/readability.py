import re
import numpy as np

def get_sentences(doc: str):
    line_break = re.compile('[\r\n]')
    delimiter = re.compile('[，。？！；,\.\?\!\;\s]')
    sentences = []
    for line in line_break.split(doc):
        line = line.strip()
        if line == '':
            continue
        if not line:
            continue
        for sent in delimiter.split(line):
            sent = sent.strip()
            if sent in [',', '.', '?', '!', ';', '，', '。', '？', '！', '；',' ']:
                continue
            if not sent:
                continue
            sentences.append(sent)
    return sentences

def readability(text: str, word_num: int):
    sentences = get_sentences(text)
    zi_num = []
    for sent in sentences:
        zi_num.append(len(sent))
    zi_num_sum = np.sum(zi_num)
    # 生僻字 常用词 标点符号 句子长度 单词个数 文字个数
    return 0.01 + 0.02 * len(sentences) - word_num * 0.01 + 0.08 * zi_num_sum

def ari(text: str,words: int):
    sentences = get_sentences(text)
    sentences_length = len(sentences)
    characters = len(text)
    ARI = 4.71 * (characters / words) + 0.5 * (words / sentences_length) - 21.43
    return ARI