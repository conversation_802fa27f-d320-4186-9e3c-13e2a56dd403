# -*- coding: utf-8 -*-
# !/usr/bin/python

import os
import json
import matplotlib.pyplot as plt
import senta
import pandas as pd
from cut import cut
from wordcloud import WordCloud

def read_file(root: str):
    with open(root,'r',encoding='utf-8') as f:
        dict = json.load(f)
        return dict

def read_dir(root: str):
    dirs = os.listdir(root)
    for dir in dirs:
        isDir = os.path.isdir(os.path.join(root,dir))
        if isDir:
            yield (dir,read_dir(os.path.join(root,dir)))
        else:
            yield (dir,read_file(os.path.join(root, dir)))

current_directory = os.path.dirname(os.path.abspath(__file__))
path = os.path.join(current_directory,'data','weibo')

# 微博媒体新闻15314篇
news_num = 0
comment_num = 0
share_num = 0
like_num = 0

count = []

file_name = '2022-10-17'

article_texts = []
comment_texts = []
share_texts = []

stop_words = []
with open('stop_words.txt','r',encoding='utf-8') as s:
    for line in s.readlines():
        stop_words.append(line.strip())

def sort(x,y):
    list = []
    for i, itemX in enumerate(x):
        itemY = y[i]
        list.append((int(itemX),itemY))
    list.sort()
    return list

def draw(x,y,label):
    list = sort(x,y)
    xs = []
    ys = []
    for (itemX,itemY) in list:
        xs.append(itemX)
        ys.append(itemY)
    plt.clf()
    plt.title(label)
    plt.grid(linestyle=":")
    plt.plot(xs, ys)
    plt.subplots_adjust(wspace=0.2, hspace=0.4)
    plt.tight_layout()
    plt.bar(xs,ys)
    filename = 'data/images/new_count/'+label+ '.jpg'
    plt.savefig(filename)

def filter_word():
    ...

def createWordCloud(texts, label, isCut=True):
    if len(texts) == 0:
        return
    plt.clf()
    if isCut:
        text_cut = cut_count(texts, stops=stop_words)
        if len(text_cut) == 0:
            return
    else:
        text_cut = texts
    wc = WordCloud(font_path='simfang.ttf',background_color='white',width=980,height=760)
    wc.generate(' '.join(text_cut))
    plt.imshow(wc,interpolation='bilinear')
    plt.axis("off")
    filename = 'data/images/new_count/'+label+ '.jpg'
    plt.savefig(filename)

def cut_count(texts, stops):
    if len(texts) ==0:
        return []
    list = cut(texts)
    words = []
    for word, word_num, text_readability in list:
        for item in word['word']:
            if item in stops:
                continue
            words.append(item)
    return words

def get_emotion(texts):
    if len(texts)==0:
        return 1
    semotion = senta.senta(texts)
    df = pd.DataFrame(semotion)
    des = df.describe()
    m = des.get('positive_probs')
    return m['mean']

users = []
comment_users = []
share_users = []
like_users = []

for (month, monthList) in read_dir(path):
    for (day, dayList) in monthList:
        fig = plt.figure()
        sub_plot = fig.add_subplot()
        sub_plot.set_title(month+'-'+day+'-new-count')
        x = []
        count_y = []
        like_y = []
        share_y = []
        comment_y = []
        emotion_y = []
        emotion_common_y = []
        emotion_share_y = []
        common_texts = []
        share_texts = []
        texts = []
        for (hour,hourList) in dayList:
            hour_num = [0,0,0,0]
            for (file, fileList) in hourList:
                # 计数
                like = fileList['like']
                share = fileList['share']
                comment = fileList['comment']
                data = fileList['data']
                user = fileList['user']
                like_len = len(like)
                share_len = len(share)
                comment_len = len(comment)
                hour_num[0] = hour_num[0] +1
                hour_num[1] = hour_num[1] +like_len
                hour_num[2] = hour_num[2] +share_len
                hour_num[3] = hour_num[3] +comment_len
                users.append(user['screen_name'])
                for c in comment:
                    # comment_users.append()
                    user = c['user']
                    comment_users.append(user['screen_name'])
                    common_texts.append(c['text_raw'])
                for c in like:
                    like_users.append(c['user']['screen_name'])
                for c in share:
                    user = c['user']
                    share_users.append(user['screen_name'])
                    share_texts.append(c['text_raw'])
                texts.append(data['text_raw'])
            x.append(hour)
            count_y.append(hour_num[0])
            like_y.append(hour_num[1])
            share_y.append(hour_num[2])
            comment_y.append(hour_num[3])
            
            article_emotion = get_emotion(texts)
            emotion_y.append(article_emotion)

            comment_emotion =get_emotion(common_texts)
            emotion_common_y.append(comment_emotion)

            share_emotion =get_emotion(share_texts)
            emotion_share_y.append(share_emotion)

            draw(x,count_y,month + '_' + day +'_count')
            draw(x,like_y,month + '_' + day +'_like')
            draw(x,share_y,month + '_' + day +'_share')
            draw(x,emotion_y,month + '_' + day +'_emotion')
            draw(x,emotion_common_y,month + '_' + day +'_comment_emotion')
            draw(x,emotion_share_y,month + '_' + day +'_share_emotion')
        
        createWordCloud(texts,month + '_' + day)
        createWordCloud(common_texts,month + '_' + day +'_comment')
        createWordCloud(share_texts,month + '_' + day +'_share')


createWordCloud(users,'user',isCut=False)
createWordCloud(comment_users,'comment_users',isCut=False)
createWordCloud(share_users,'share_users',isCut=False)
createWordCloud(like_users,'like_users',isCut=False)

# for (month, monthList) in read_dir(path):
#     for (day, dayList) in monthList:
#         if day == 16:
#             print(day)
#         sub_plot = fig.add_subplot()
#         x = range(12)
#         y = []
#         for (hour,hourList) in dayList:
#             hour_num = [0,0,0,0]
#             for (file, fileList) in hourList:
#                 # 计数
#                 like = fileList['like']
#                 share = fileList['share']
#                 comment = fileList['comment']
#                 data = fileList['data']
#                 like_len = len(like)
#                 share_len = len(share)
#                 comment_len = len(comment)

#                 hour_num[0] = hour_num[0] +1
#                 hour_num[1] = hour_num[1] +like_len
#                 hour_num[2] = hour_num[2] +share_len
#                 hour_num[3] = hour_num[3] +comment_len

#                 news_num +=1
#                 like_num += like_len
#                 share_num += share_len
#                 comment_num += comment_len
#                 topic_struct = []
#                 try:
#                     topic_struct = data['topic_struct']
#                 except Exception as e:
#                     ...
#                 topic_title = []
#                 for topic in topic_struct:
#                     topic_title.append(topic['topic_title'])

#                 shares = []
#                 for s in share:
#                     shares.append([
#                         s['created_at'],
#                         s['user']['screen_name'],
#                         s['text_raw'],
#                         s['source'],
#                         s['reposts_count'],
#                         s['comments_count'],
#                         s['attitudes_count'],
#                     ])
#                 likes = []
#                 for l in like:
#                     likes.append([
#                         l['user']['screen_name']
#                     ])
#                 comments = []
#                 for c in comment:
#                     children = []
#                     try:
#                         children = c['children']
#                     except Exception as e:
#                         ...
#                     childrens =  []
#                     for child in children:
#                         childrens.append([
#                             child['created_at'],
#                             child['user']['screen_name'],
#                             child['text_raw'],
#                             child['like_counts']
#                         ])
#                     comments.append([
#                         c['created_at'],
#                         c['user']['screen_name'],
#                         c['text_raw'],
#                         c['like_counts'],
#                         childrens
#                     ])
#                 list = [
#                     data['created_at'],
#                     data['id'],
#                     data['text_raw'],
#                     data['textLength'],
#                     data['pic_num'],
#                     data['reposts_count'],
#                     data['comments_count'],
#                     data['attitudes_count'],
#                     topic_title,
#                     share_len,
#                     like_len,
#                     comment_len,
#                     shares,
#                     likes,
#                     comments
#                 ]
#                 article_texts.append(data['text_raw'])
#                 # save_to_csv(os.path.join(current_directory,'data/'+file_name+'.csv'),[list],True)
#             count.append([
#                 month,day, hour,hour_num
#             ])
#         sub_plot.plot(x,y)
# save_to_csv(os.path.join(current_directory,'data/'+file_name+'-count.csv'),count,True)
