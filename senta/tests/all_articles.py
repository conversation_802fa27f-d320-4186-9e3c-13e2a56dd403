
from nger.db.postgres import Postgres
import numpy as np

def all_articles(psize: int):
    count = count_articles()
    total = int(np.ceil(count/psize))
    pg = Postgres.create()
    for i in range(total):
        page = i + 1
        sql = "select id,create_at,eid, platform,uid,text_raw,tid from public.spilder_topic_article limit " + \
            str(psize)+" offset "+ str((page-1)*psize)
        events = pg.fetchall(sql)
        yield events
    pg.dispose()

def all_event_articles(eid, psize: int):
    count = count_event_article(eid)
    total = int(np.ceil(count/psize))
    pg = Postgres.create()
    for i in range(total):
        page = i + 1
        sql = "select id,create_at,eid, platform,uid,text_raw,tid from public.spilder_topic_article where eid='"+str(eid)+"' limit " + \
            str(psize)+" offset "+ str((page-1)*psize)
        events = pg.fetchall(sql)
        yield events
    pg.dispose()

def count_event_article(eid):
    pg = Postgres.create()
    sql = "select count(1) from public.spilder_topic_article where eid='"+str(eid)+"'"
    item = pg.fetchone(sql)
    pg.dispose()
    return item[0]

def count_articles():
    pg = Postgres.create()
    sql = "select count(1) from public.spilder_topic_article"
    item = pg.fetchone(sql)
    pg.dispose()
    return item[0]
