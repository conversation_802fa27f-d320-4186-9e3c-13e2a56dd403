
from nger.db.postgres import Postgres
import numpy as np

def all_components(psize: int):
    count = count_components()
    total = int(np.ceil(count/psize))
    pg = Postgres.create()
    for i in range(total):
        page = i + 1
        sql = "select id,eid,uid,text from public.spilder_topic_article_comment where pid is NULL limit " + \
            str(psize)+" offset "+ str((page-1)*psize)
        events = pg.fetchall(sql)
        yield events
    pg.dispose()

def count_components():
    pg = Postgres.create()
    sql = "select count(1) from public.spilder_topic_article_comment where pid is NULL"
    item = pg.fetchone(sql)
    pg.dispose()
    return item[0]
