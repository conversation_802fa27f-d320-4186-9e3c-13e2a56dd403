from ast import Expression
import paddlehub as hub
from all_articles import all_event_articles
from all_events import all_events
import re
import csv
import pandas as pd
import numpy as np
import os

from save_to_csv import save_to_csv

lac = hub.Module(name="lac")
senta = hub.Module(name="senta_bilstm")

def get_sentences(doc: str):
    line_break = re.compile('[\r\n]')
    delimiter = re.compile('[，。？！；,\.\?\!\;]')
    sentences = []
    for line in line_break.split(doc):
        line = line.strip()
        if line == '':
            continue
        if not line:
            continue
        for sent in delimiter.split(line):
            sent = sent.strip()
            if sent in [',','.','?','!',';','，','。','？','！','；']:
                continue
            if not sent:
                continue
            sentences.append(sent)
    return sentences

def get_readability(text: str, num_word):
    sentences = get_sentences(text)
    zi_num_pre_sent = []
    for sent in sentences:
        zi_num_pre_sent.append(len(sent))
    max_zi_num_per_sent = np.sum(zi_num_pre_sent)
    return 0.01 + 0.02 * len(sentences) - num_word * 0.01 + 0.08 * max_zi_num_per_sent

events = all_events()
for eid, title in events:
    dirname = os.path.join(os.getcwd(), 'data/source', str(eid)+'@'+title)
    article_path = os.path.join(dirname, 'article.csv')
    print(title)
    try:
        os.makedirs(dirname)
    except Exception:
        pass
    articles = all_event_articles(eid, 100)
    csv_file = open('article.csv', 'a', encoding='utf-8', newline='')
    csv_data = csv.writer(csv_file)
    fieldnames = ['id', 'eid', 'uid', 'text', 'sentiment_label',
                  'positive_probs', 'negative_probs', 'a', 'n', 'd', 'v']
    csv_data.writerow(fieldnames)
    for articleList in articles:
        texts = []
        list = []
        for id,create_at,eid, platform,uid,text_raw,tid in articleList:
            text: str = str(text_raw).replace(u'\u200b', '').replace(
                u'\ufeff', '').replace(u'\ue601', '')
            text = re.sub(r'\【\#.*?\#\】', '', text).strip()
            text = re.sub(r'\#.*?\#', '', text).strip()
            text = re.sub(r'http.*\w', '', text)
            texts.append(text)
        if len(texts) > 0:
            input_dict = {"text": texts}
            sentiments = senta.sentiment_classify(data=input_dict)
            fencis = lac.cut(text=texts, use_gpu=False,
                            batch_size=1, return_tag=True)
            for article, sentiment, fenci in zip(articleList, sentiments, fencis):
                tag = pd.Series(fenci['tag'])
                tag_counts = tag.value_counts()
                text = sentiment['text']
                sentiment_label = sentiment['sentiment_label']
                positive_probs = sentiment['positive_probs']
                negative_probs = sentiment['negative_probs']
                readability = get_readability(text, len(fenci['tag']))
                id,create_at,eid, platform,uid,text_raw,tid = article
                l = len(text)
                list.append([
                    id, create_at, eid, platform, tid,l,tag_counts.get(
                        'n'),[], tag_counts.get('d'),[], tag_counts.get('a'),[], tag_counts.get('v'),[],0,[],0,[],0,[],readability,positive_probs,0
                ])
        save_to_csv(article_path,list, True)
    csv_file.close()
