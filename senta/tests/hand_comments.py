from all_comments import all_components
import re
import paddlehub as hub
import csv

senta = hub.Module(name="senta_bilstm")
comment_pages = all_components(10000)
csv_file = open('comment.csv', 'a', encoding='utf-8', newline='')
csv_data = csv.writer(csv_file)
page_id = 0

for page in comment_pages:
    page_id += 1
    texts = []
    for id, eid, uid, text in page:
        text: str = str(text).replace(u'\u200b', '').replace(
            u'\ufeff', '').replace(u'\ue601', '')
        text = re.sub(r'回复\@.*\:', '', text).strip()
        texts.append(text)
    input_dict = {"text": texts}
    results = senta.sentiment_classify(data=input_dict)
    list = []
    for article, sentiment in zip(page, results):
        text_re = sentiment['text']
        sentiment_label = sentiment['sentiment_label']
        positive_probs = sentiment['positive_probs']
        negative_probs = sentiment['negative_probs']
        id, eid, uid, text = article
        csv_data.writerow([id, eid, uid, text, text_re,
                          sentiment_label, positive_probs, negative_probs])

csv_file.close()
