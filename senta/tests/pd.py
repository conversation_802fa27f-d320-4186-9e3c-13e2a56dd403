from asyncore import write
import pandas as pd


df = pd.read_excel('9.19.xlsx', sheet_name=0)

df_n = pd.DataFrame({
    "热搜事件": df['热搜事件'],
    "政府第一次公告时间": df['政府第一次公告时间   (如：2020.08.02 17:30)'],
    "政府第一次公告内容": df['政府第一次公告内容'],
    "政府第二次公告时间": df['政府第二次公告时间   (如：2020.08.02 17:30)'],
    "政府第二次公告内容": df['政府第二次公告内容'],
    "政府第三次公告时间": df['政府第三次公告时间            (如：2020.08.02 17:30)'],
    "政府第三次公告内容": df['政府第三次公告内容'],
    "政府第四次公告时间": df['政府第四次公告时间     (如：2020.08.02 17:30)'],
    "政府第四次公告内容": df['政府第四次公告内容'],
    "政府第五次公告时间": df['政府第五次公告时间   (如：2020.08.02 17:30)'],
    "政府第五次公告内容": df['政府第五次公告内容'],
    "政府第六次公告时间": df['政府第六次公告时间    (如：2020.08.02 17:30)'],
    "政府第六次公告内容": df['政府第六次公告内容'],
    "政府第七次公告时间": df['政府第七次公告时间            (如：2020.08.02 17:30)'],
    "政府第七次公告内容": df['政府第七次公告内容'],
    "政府第八次公告时间": df['政府第八次公告时间            (如：2020.08.02 17:30)'],
    "政府第八次公告内容": df['政府第八次公告内容'],
    "政府第九次公告时间": df['政府第九次公告时间            (如：2020.08.02 17:30)'],
    "政府第九次公告内容": df['政府第九次公告内容'],
    "政府第十次公告时间": df['政府第十次公告时间            (如：2020.08.02 17:30)'],
    "政府第十次公告内容": df['政府第十次公告内容'],
    "政府第十一次公告时间": df['政府第十一次公告时间            (如：2020.08.02 17:30)'],
    "政府第十一次公告内容": df['政府第十一次公告内容']
})
filename = '9.19-pd.xlsx'
writer = pd.ExcelWriter(filename)
df_n.to_excel(writer,sheet_name='政府公告')
writer.save()