import paddlehub as hub

# 加载模型
model = hub.Module(name='ernie_zeus')

# 完形填空
# result = model.text_cloze(
#     text='她有着一双[MASK]的眼眸。' 
# )

# 文本情感分析
# result2 = model.custom_generation(
#     text='这家店味道不好，下次再也不来了，这评价是好还是坏？',
#     task_prompt='SentimentClassification' 
# )

result = model.novel_continuation(
    text='昆仑山可以说是天下龙脉的根源，所有的山脉都可以看作是昆仑的分支。这些分出来的枝枝杈杈，都可以看作是一条条独立的龙脉。' 
)


# print(result)
print(result)
