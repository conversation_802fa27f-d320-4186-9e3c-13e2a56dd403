import { Injectable, Injector } from "@nger/core";
import { Db } from "@nger/typeorm";
import { WbPlatform } from "../entities/platform";
import { Spilder, SpilderError } from "./spilder";
import { ToutiaoSpilder } from "./toutiao-spilder";
import { <PERSON><PERSON>Spilder } from "./weibo-spilder";
import { ZhihuSpilder } from "./zhihu-spilder";
@Injectable()
export class SpilderFactory {
    get db() {
        return this.injector.get(Db)
    }
    map: Map<string, Promise<Spilder>> = new Map();
    createing: Set<string> = new Set();
    constructor(private injector: Injector) { }
    async createSpilder(name: string, nocache: boolean = false) {
        const platform = await this.db.manager.findOne(WbPlatform, { where: { name: name } });
        if (!platform) {
            throw new Error(`平台不存在或已删除`);
        }
        switch (name) {
            case 'weibo':
                const weiboSpilder = new WeiboSpilder(this.injector, platform);
                await weiboSpilder.init();
                if (platform.cookies) {
                    const cookies = platform.cookies.map(c => {
                        c.url = `https://weibo.com/`
                        return c;
                    });
                    await weiboSpilder.setCookies(cookies)
                }
                return weiboSpilder;
            case 'zhihu':
                const zhihuSpilder = new ZhihuSpilder(this.injector, platform);
                await zhihuSpilder.init();
                if (platform.cookies) {
                    const cookies = platform.cookies.map(c => {
                        c.url = `https://www.zhihu.com/`
                        return c;
                    });
                    await zhihuSpilder.setCookies(cookies)
                }
                return zhihuSpilder;
            case 'toutiao':
                const toutiaoSpilder = new ToutiaoSpilder(this.injector, platform);
                // https://www.toutiao.com/
                if (platform.cookies) {
                    const cookies = platform.cookies.map(c => {
                        c.url = `https://www.toutiao.com/`
                        return c;
                    });
                    await toutiaoSpilder.setCookies(cookies)
                }
                return toutiaoSpilder;
            default:
                throw new SpilderError(-200, `暂不支持${platform.title}`)
        }
    }
    async create(name: string, nocache: boolean = false): Promise<Spilder> {
        if (this.map.has(name)) {
            const spilder = this.map.get(name)!;
            return spilder;
        }
        const spilder = this.createSpilder(name, nocache);
        this.map.set(name, spilder);
        return spilder;
    }

    async createNoCookie(name: string, nocoche: boolean = false): Promise<Spilder> {
        if (this.map.has(name)) {
            const spilder = this.map.get(name)!;
            return spilder;
        }
        const spilder = this.createSpilder(name, nocoche);
        this.map.set(name, spilder);
        return spilder;
    }
}
