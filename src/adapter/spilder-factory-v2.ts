import { Injectable, Injector } from "@nger/core";
import { TaskManager } from "@nger/rabbitmq";
import { Db } from "@nger/typeorm";
import { WbPlatform } from "../entities/platform";
import { SpilderError } from "./spilder";
import { <PERSON><PERSON><PERSON>Spilder } from "./toutiao-spilder";
import { <PERSON>boSpilder } from "./weibo-spilder";
import { ZhihuSpilder } from "./zhihu-spilder";

@Injectable()
export class SpilderFactoryV2 {
    get db() {
        return this.injector.get(Db)
    }
    get manager() {
        return this.injector.get(TaskManager)
    }
    constructor(private injector: Injector) { }
    async create(platformId: number) {
        const platform = await this.db.manager.findOne(WbPlatform, { where: { id: platformId } });
        if (!platform) {
            throw new Error(`平台不存在或已删除`);
        }
        switch (platform.name) {
            case 'weibo':
                const weiboSpilder = new WeiboSpilder(this.injector, platform);
                await weiboSpilder.init();
                if (platform.cookies) {
                    const cookies = platform.cookies.map(c => {
                        c.url = `https://weibo.com/`
                        return c;
                    });
                    await weiboSpilder.setCookies(cookies)
                }
                return weiboSpilder;
            case 'zhihu':
                const zhihuSpilder = new ZhihuSpilder(this.injector, platform);
                await zhihuSpilder.init();
                if (platform.cookies) {
                    const cookies = platform.cookies.map(c => {
                        c.url = `https://www.zhihu.com/`
                        return c;
                    });
                    await zhihuSpilder.setCookies(cookies)
                }
                return zhihuSpilder;
            case 'toutiao':
                const toutiaoSpilder = new ToutiaoSpilder(this.injector, platform);
                // https://www.toutiao.com/
                if (platform.cookies) {
                    const cookies = platform.cookies.map(c => {
                        c.url = `https://www.toutiao.com/`
                        return c;
                    });
                    await toutiaoSpilder.setCookies(cookies)
                }
                return toutiaoSpilder;
            default:
                throw new SpilderError(-200, `暂不支持${platform.title}`)
        }
    }
}
