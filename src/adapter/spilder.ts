import { defer, Injector } from "@nger/core";
import { <PERSON><PERSON><PERSON> } from "@nger/puppeteer";
import { Db } from "@nger/typeorm";
import { Page } from "@nger/puppeteer";
import { WbPlatform } from "../entities/platform";
import { PUPPETEER_POOL } from "../tokens";


export abstract class Spilder {
    get db() {
        return this.injector.get(Db)
    }
    private _cookies!: string;
    get cookies() {
        if (this._cookies) {
            return this._cookies;
        }
        this._cookies = this.platform.cookies ? this.platform.cookies.map(c => `${c.name}=${c.value}`).join(';') : '';
        return this._cookies;
    }
    set cookies(cookies: string) {
        this._cookies = cookies;
    }
    delay(time: number) {
        const d = defer();
        setTimeout(() => {
            d.resolve();
        }, time);
        return d;
    }
    constructor(public injector: Injector, public platform: WbPlatform) { }
    page!: Page | undefined;
    browser!: Browser;
    get puppeteerPool(){
        return this.injector.get(PUPPETEER_POOL)
    }
    async init() {
        if (!this.page) {
            const browser = await this.puppeteerPool.acquire();
            this.browser = browser;
            this.page = await browser.newPage() as any;
        }
    }
    /**
     * 检查是否登录
     */
    // abstract isLogin(): Promise<boolean>;
    /**
     * 创建登录二维码
     */
    // abstract createLoginQrCode(): Promise<string | undefined>;
    /**
     * 检查二维码状态
     */
    // abstract checkScanStatus(): Promise<string | undefined>;
    /**
     * 检查登录成功
     */
    // abstract loginSuccess(): Promise<WbPlatform | undefined>;
    /**
     * 设置cookie
     * @param cookies 
     */
    // abstract setCookies(cookies: any[]): Promise<void>;

    abstract createTopicPageLinks(keyword: string): Promise<{ links: string[]; keyword: string; count: number }>;
    abstract getRealtimeArticleLinks(keyword: string, page?: number): Promise<{ links: string[]; keyword: string; count: number }>;

    abstract createTopicLinks(link: string): Promise<{ list: string, detail: string }[]>;

    abstract createTopicDetail(link: string): Promise<any>;
    abstract createTopicArticlePageLinks(link: string): Promise<string[]>;
    abstract createTopicArticleLinks(page: string): Promise<string[]>;
    abstract createTopicArticleDetail(link: string): Promise<any>;

    abstract loadCommentCount(id: number, uid: number): Promise<number>;
    abstract loadChildCommentCount(id: number, uid: number): Promise<number>;

    abstract loadComments(id: number, uid: number, total?: number): Promise<any>;
    abstract loadMoreComments(id: number, uid: number, max_id: number, total: number): Promise<any>;
    abstract loadChildComments(id: number, uid: number): Promise<any>;
    abstract loadMoreChildComments(id: number, uid: number, max_id: number, total: number): Promise<any>;
    abstract createAccount(uid: number): Promise<any>;
    abstract createTopicArticleLongTextDetail(id: string): Promise<any>;
}

export class SpilderError extends Error {
    code: number;
    constructor(code: number, msg: string) {
        super(msg);
        this.code = code;
    }
}
