#!/usr/bin/env node
import { config } from 'dotenv'
import { join } from 'path'
config({
    path: join(__dirname, '../config/product.env')
})
import "reflect-metadata";
process.env.TZ = 'Asia/Shanghai';
import { Module, platformCore, APP_ROOT, Injector } from '@nger/core';
import { RabbitmqModule, REDIS, RedisFactory, RedisModule, TasksModule } from './framework';
import { Task } from "./framework/tasks/task";
import { Db, TypeormModule } from "@nger/typeorm";
import { SpiderUser } from "./entities/spider_user";
import { EventEmitter } from "stream";
import { News } from "./entities/news";
import { coreProviders } from './tokens';
import { SysMessageEntity } from './entities/message';
import { Amqp } from './v2/amqp';
import { SystemMessage } from './v2/message';
import { User } from '@nger/entities';
import { CrawlingUrl } from './entities/crawling_url';
import { Keyword } from './entities/keyword';

EventEmitter.setMaxListeners(1000)

@Module({
    imports: [
        RabbitmqModule.forRoot(),
        TasksModule.forRoot(),
        RedisModule,
        TypeormModule.forEnv(),
        TypeormModule.forFeature([
            SpiderUser,
            News,
            SysMessageEntity,
            User,
            CrawlingUrl,
            Keyword
        ])
    ],
    providers: [
        ...coreProviders,
        {
            provide: Amqp,
            useFactory: () => {
                const options = {
                    hostname: process.env['MQ_HOST'] || '*************',
                    port: parseInt(process.env['MQ_PORT'] || '15672'),
                    username: process.env['MQ_USER'] || 'imeepos',
                    password: process.env['MQ_PASS'] || '123qwe',
                    protocol: 'amqp'
                }
                return new Amqp(options)
            },
            deps: []
        },
        {
            provide: SystemMessage,
            useFactory: (injector: Injector) => {
                const db = injector.get(Db)
                const amqp = injector.get(Amqp)
                return new SystemMessage(amqp, db)
            },
            deps: [Injector]
        }
    ]
})
export class AppModule { }

platformCore([{
    provide: APP_ROOT,
    useValue: process.cwd()
}]).bootstrap(AppModule)
    .then(async injector => {
        const tasks = injector.getMulti(Task)
        // const redis = await injector.get(REDIS)
        const db = injector.get(Db);
        console.log(`start task`)
        async function run() {
            try {
                const news = new News();
                news.type = 'null';
                news.comment = [];
                news.like = [];
                news.share = [];
                news.platform = ``;
                news.from = ``;
                news.data = {};
                news.user = {};
                news.aid = ``
                news.uid = ``;
                news.post_at = new Date();
                news.readablity = 0;
                news.word_num = 0;
                news.cut = {};
                news.keywords = '';
                news.negative_probs = 0;
                news.positive_probs = 1;
                news.sentiment_key = 'positive';
                news.sentiment_label = 1;
                // 分享情感
                news.share_cut = [];
                news.share_emotion = [];
                news.share_readablity_avg = 0
                news.share_word_num_avg = 0

                news.share_positive_avg = 0
                news.share_negative_avg = 0
                news.share_negative_count = 0;
                news.share_positive_count = 0;

                news.comment_cut = [];
                news.comment_emotion = [];
                news.comment_readablity_avg = 0
                news.comment_positive_avg = 0
                news.comment_negative_avg = 0
                news.comment_negative_count = 0;
                news.comment_positive_count = 0;
                news.comment_word_num_avg = 0;
                const { id } = await db.createEntityManager().save(News, news)
                if(id < 1638940) {
                    console.log(`id is ${id}`)
                    return false;
                }
                return true;
            } catch (e) {
                return false;
            }
        }
        let isSuccess = await run()
        while(!isSuccess){
            isSuccess = await run()
        }
        // const list = await db.query(`select n.from, platform from sys_news as n`);
        // await Promise.all(list.map(async (item: any) => {
        //     const _url = new URL(item.from);
        //     const id = _url.searchParams.get('id')
        //     await redis.set(`${item.platform}.${id}`,1)
        // }))
        await Promise.all(tasks.map(task => {
            return Promise.all([
                task.consumer().catch(e => { }),
                // task.dead().catch(e=>{})
            ])
        }))
        console.info('恭喜您主consumer启动成功')
    }).catch(e => {
        console.error(e)
    });

