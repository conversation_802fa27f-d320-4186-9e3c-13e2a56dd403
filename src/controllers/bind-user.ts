

import { Controller, Get, Injector, defer, Inject } from "@nger/core";
import { Subject } from "rxjs";
import { Post, Body } from '@nger/http'
import { ZhihuLoginService } from "../services/zhihu.login";
import { CONTEXT } from "@nger/http";
import { URLSearchParams } from "url";
import { ToutiaoLoginService } from "../services/toutiao.login";
import { QqNewLoginService } from "../services/qq-new.login";
import { Db } from '@nger/typeorm'
import { SpiderUser } from "../entities/spider_user";
import { PUPPETEER_POOL } from "../tokens";

@Controller('@nger/weibo/bind-user')
export class BindUserController {
    get zhihu() {
        return this.injector.get(ZhihuLoginService)
    }
    get toutiao() {
        return this.injector.get(ToutiaoLoginService)
    }
    get qqNew() {
        return this.injector.get(QqNewLoginService)
    }
    get db() {
        return this.injector.get(Db)
    }
    constructor(private injector: Injector) { }
    @Post('update_or_add_user')
    async updateOrAddUser(@Body() user: any) {
        const r = this.db.getRepository(SpiderUser)
        const item = await r.findOneBy({ username: user.username, platform: user.platform })
        if (item) {
            await r.update(item.id, {
                cookies: user.cookies,
                status: user.status,
            })
        } else {
            await r.insert({
                cookies: user.cookies,
                status: user.status,
                platform: user.platform,
                username: user.username
            })
        }
        return user;
    }

    @Get('login_qrcode')
    async login_qrcode(@Inject(CONTEXT) ctx: any) {
        const login_state = new Subject()
        const req = ctx.request;
        const query = req.query as URLSearchParams
        const platform = query.get('platform')
        switch (platform) {
            case 'weibo':
                this.open_weibo(login_state)
                break;
            case 'zhihu':
                this.zhihu.login(login_state)
                break;
            case 'toutiao':
                this.toutiao.login(login_state)
                break;
            case 'qq-new':
                this.qqNew.login(login_state)
                break;
            case 'wechat':
                break;
            case 'douyin':
                break;
            case 'baidu':
                break;
        }
        return login_state.asObservable()
    }
    private delay(time: number) {
        const d = defer();
        setTimeout(() => {
            d.resolve();
        }, time)
        return d;
    }
    get puppeteerPool(){
        return this.injector.get(PUPPETEER_POOL)
    }
    private async open_weibo(login_state: Subject<any>) {
        const browser = await this.puppeteerPool.acquire()
        const url = `https://s.weibo.com`;
        const ctx = browser.defaultBrowserContext();
        await ctx.overridePermissions(url, ['geolocation'])
        const page = await browser.newPage();
        await page.setJavaScriptEnabled(true);
        await page.setViewport({
            width: 1920,
            height: 768
        });
        await page.goto(url, {
            waitUntil: ['networkidle2']
        });
        await this.delay(20)
        const href = page.url();
        login_state.next({
            action: 'open',
            data: href
        });
        try {
            const loginCardBtnJpU1 = await page.waitForSelector('.LoginBtn_btn_10QRY').catch(e => {
                return null;
            });
            login_state.next({
                action: 'get_login_btn',
                data: href
            });
            if (loginCardBtnJpU1) {
                await loginCardBtnJpU1.click();
                login_state.next({
                    action: 'click_login_btn',
                    data: href
                })
                await page.waitForNavigation()
                const img = await page.waitForSelector('div.LoginPop_mabox_3Lyr6 > img').catch(e => {
                    return null;
                });
                if (!img) {
                    login_state.next({
                        action: 'get_login_fail',
                        data: href
                    });
                    login_state.complete();
                } else {
                    let loginImage = await page.$eval('div.LoginPop_mabox_3Lyr6 > img', (img) => (img as HTMLImageElement).src);
                    if (loginImage.startsWith('https://weibo.com/newlogin')) {
                        login_state.next({
                            action: 'get_login_fail',
                            data: loginImage
                        })
                        login_state.complete();
                        return;
                    }
                    login_state.next({
                        action: 'get_login_qrcode',
                        data: loginImage
                    });
                    // 检查是否扫码
                    const tip = await page.waitForSelector('span.woo-tip-text > div.LoginPop_t1_2fuX8', { timeout: 1000 * 60 });
                    if (tip) {
                        const txt = await page.$eval('span.woo-tip-text > div.LoginPop_t1_2fuX8', (div) => {
                            return (div as HTMLDivElement).innerText;
                        });
                        login_state.next({
                            action: 'get_scan_qrcode',
                            data: txt
                        })
                        // 扫描成功
                        await page.waitForNavigation()
                        const url = page.url()
                        if (url.startsWith('https://weibo.com/u/')) {
                            // user
                        }
                        // 获取用户信息
                        const cookies = await page.cookies('https://weibo.com/')
                        login_state.next({
                            action: 'login_success',
                            data: url,
                            cookies
                        });
                        const content = await page.content()
                        if (url.startsWith('https://s.weibo.com')) {
                            const re2 = /\$CONFIG\[\'(.*?)\'\]\s*=\s*\'(.*?)\'/g
                            const user: any = {}
                            for (let item of content.matchAll(re2)) {
                                const [, key, value] = item;
                                Reflect.set(user, key, value)
                            }
                            login_state.next({
                                action: 'get_userinfo',
                                data: {
                                    uid: user.uid,
                                    cookies,
                                    name: user.nick,
                                    avatar: user.avatar_large,
                                    gender: user.sex
                                }
                            })
                        } else {
                            const re = /window\.\$CONFIG\s*=\s*(.*)\s*;\s*}\s*catch\(e\)/g
                            const result = re.exec(content)
                            if (result && result.length > 0) {
                                const res = result[1]
                                try {
                                    const CONFIG = JSON.parse(res.trim())
                                    const user = CONFIG?.user;
                                    if (user) {
                                        const uid = user.id;
                                        const name = user.screen_name;
                                        const avatar = user.avatar_large;
                                        login_state.next({
                                            action: 'get_userinfo',
                                            data: {
                                                uid,
                                                cookies,
                                                name,
                                                avatar,
                                            }
                                        })
                                    }
                                } catch (e: any) { }
                            }

                        }
                    }
                    login_state.complete();
                }
            }
        } catch (e: any) {
            login_state.next({
                action: 'get_login_fail',
                data: e.message
            })
            login_state.error(e)
        } finally {
            await page.close();
            await this.puppeteerPool.destroy(browser)
            login_state.complete();
        }
    }
}
