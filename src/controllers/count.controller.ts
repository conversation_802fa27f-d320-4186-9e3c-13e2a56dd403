import { Controller, Get, Injector } from "@nger/core";
import { TaskManager } from "@nger/rabbitmq";
import { Db } from "@nger/typeorm";
import { SpilderAccount } from "../entities/spilder-account";
import { SpilderTopicArticle } from "../entities/spilder-topic-article";


@Controller('@nger/weibo/count')
export class CountController {
    get db() {
        return this.injector.get(Db)
    }
    get manager() {
        return this.injector.get(TaskManager)
    }
    constructor(private injector: Injector) { }

    @Get('line')
    line() {
        const article = this.db.manager.getRepository(SpilderTopicArticle)
        const sql = `select count(1) from spilder_topic_article group by eid`
    }
}
