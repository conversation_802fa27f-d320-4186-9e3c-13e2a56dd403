import { CONTEX<PERSON>, Controller, Get, Inject, Injector } from "@nger/core";
import { Context, Query } from "@nger/http";
import { TaskManager } from "@nger/rabbitmq";
import { Db } from "@nger/typeorm";
import { analysisEvent, CountArticle } from "../entities/count";
import { SpilderTopic } from "../entities/spilder-topic";
import { SpilderTopicArticle } from "../entities/spilder-topic-article";
import { SpilderTopicArticleComment } from "../entities/spilder-topic-article-comment";

@Controller('@nger/weibo/fenci')
export class FenciController {
    get db() {
        return this.injector.get(Db)
    }
    get manager() {
        return this.injector.get(TaskManager)
    }
    constructor(private injector: Injector) { }
    @Get('topic')
    async topic(@Query('page') page: number, @Inject(CONTEXT) ctx: Context) {
        page = page || 1;
        const psize = 1000;
        const topics = await this.db.manager.find(SpilderTopic, {
            skip: (Number(page) - 1) * psize,
            take: psize
        });
        if (topics.length > 0) {
            await Promise.all(topics.map(topic => {
                if (topic.summary.length > 0) {
                    return this.manager.send({
                        topic: '@nger/weibo/fenci',
                        data: {
                            id: topic.id,
                            type: 'topic',
                            text: topic.summary
                        }
                    })
                }
            }))
            ctx.redirect(`/@nger/weibo/fenci/topic?page=${Number(page) + 1}`)
            return;
        } else {
            return {
                code: 0,
                message: '任务完成'
            }
        }
    }

    @Get('article')
    async article(@Query('page') page: number, @Inject(CONTEXT) ctx: Context) {
        page = page || 1;
        const psize = 1000;
        const topics = await this.db.manager.find(SpilderTopicArticle, {
            skip: (Number(page) - 1) * psize,
            take: psize
        });
        if (topics.length > 0) {
            await Promise.all(topics.map(topic => {
                if (topic.text_raw.length > 0) {
                    let text = topic.text_raw;
                    // let reg = /[(\u4e00-\u9fa5)(\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3010|\u3011|\u007e|,|.|?|!|\"|\`|\')]+/g
                    // const res = text.match(reg)
                    // if (res) text = res[0];
                    if (text.length) {
                        return this.manager.send({
                            topic: '@nger/weibo/fenci',
                            data: {
                                id: topic.id,
                                type: 'article',
                                platform: topic.platform,
                                eid: topic.eid,
                                postAt: topic.create_at,
                                text: text,
                                pid: 0
                            }
                        })
                    }
                }
            }))
            ctx.redirect(`/@nger/weibo/fenci/article?page=${Number(page) + 1}`)
            return;
        } else {
            return {
                code: 0,
                message: '操作成功'
            }
        }
    }

    @Get('analysis')
    async analysisEvent(@Query('eid') eid: number, @Query('date') dates: string) {
        eid = Number(eid)
        const times = dates.split('|').map(it => new Date(it));
        const db = this.injector.get(Db)
        const start = await db.manager.findOne(CountArticle, { where: { eid }, order: { postAt: 'asc' } })
        const end = await db.manager.findOne(CountArticle, { where: { eid }, order: { postAt: 'desc' } })
        if (start) {
            times.unshift(start.postAt)
        }
        if (end) {
            times.push(end.postAt)
        }
        const res = await analysisEvent(eid)(db, times)
        return {
            eid,
            times,
            data: res
        };
    }

    @Get('comment')
    async comment(@Query('page') page: number, @Inject(CONTEXT) ctx: Context) {
        page = page || 1;
        const psize = 100;
        const topics = await this.db.manager.find(SpilderTopicArticleComment, {
            skip: (Number(page) - 1) * psize,
            take: psize
        });
        if (topics.length > 0) {
            await Promise.all(topics.map(topic => {
                if (topic.text.length > 0) {
                    this.manager.send({
                        topic: '@nger/weibo/fenci',
                        data: {
                            id: topic.id,
                            eid: topic.eid,
                            postAt: topic.created_at,
                            platform: topic.platform,
                            type: 'comment',
                            text: topic.text,
                            pid: topic.pid
                        }
                    })
                }
            }))
            ctx.redirect(`/@nger/weibo/fenci/comment?page=${Number(page) + 1}`)
            return;
        }
        return {
            code: 200,
            message: `操作成功`
        }
    }
}

/**
 * 
 * 60 * 7 = 420 * = 3000
 * 
 * 4-5 700 = 3500
 * 
 */

