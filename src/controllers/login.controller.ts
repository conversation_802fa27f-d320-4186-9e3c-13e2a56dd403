import { Controller, Get, Injector } from "@nger/core";
import { User } from "@nger/entities";
import { Body, Post } from "@nger/http";
import { TaskManager, RabbitmqStarter } from "@nger/rabbitmq";
import { Db } from "@nger/typeorm";
import { CryptoService } from "@nger/utils";
import { Page } from '@nger/puppeteer';
import { SpilderFactory } from "../adapter/spilder-factory";
import { WbPlatform } from "../entities/platform";
import { WbTask } from "../entities/task";
import { WebSocketServer } from 'ws';
import { catchError, combineLatest, from, interval, map, Observable, startWith } from 'rxjs'
import { SpilderTopic } from "../entities/spilder-topic";
import { SpilderTopicArticle } from "../entities/spilder-topic-article";
import { SpilderTopicArticleComment } from "../entities/spilder-topic-article-comment";
import { WbEvent } from "../entities/event";
import { SpilderAccount } from "../entities/spilder-account";

@Controller('@nger/weibo')
export class LoginController {
    page!: Page;
    get rabbitmqStarter() {
        return this.injector.get(RabbitmqStarter)
    }
    get webSocketServer() {
        return this.injector.get(WebSocketServer)
    }
    constructor(private injector: Injector) { }
    @Post('loginAdmin')
    async loginAdmin(@Body('username') username: string, @Body('password') password: string) {
        if (!username) {
            return {
                errno: -1,
                message: '用户名不能为空'
            }
        }
        if (!password) {
            return {
                errno: -1,
                message: '密码不能为空'
            }
        }
        if (!this.db) {
            return {
                errno: -1,
                message: '数据库连接失败'
            }
        }
        const old = await this.db.manager.findOne(User, { where: { username } });
        if (!old) {
            return {
                errno: -1,
                message: '用户不存在或已删除',
                data: { username }
            }
        }
        const crypto = this.injector.get(CryptoService)
        const psd = crypto.md5(password, old.salt)
        if (psd === old.password) {
            return {
                errno: 200,
                message: '恭喜您，登录成功',
                data: {
                    uid: old.uid,
                    username: old.username,
                    email: old.email
                }
            }
        }
        return {
            errno: -1,
            message: '对不起，密码不正确'
        }
    }
    @Post('schedule')
    async schedule(@Body('eid') eid: number, @Body('time') time: string) {
        let task = new WbTask();
        task.time = time;
        task.ids = [eid];
        task = await this.db.manager.save(WbTask, task);
        const manager = this.injector.get(TaskManager);
        await manager.send({
            topic: '@nger/weibo/schedule-task',
            data: task
        });
        return {
            errno: 200,
            message: '操作成功'
        }
    }

    @Post('topic_v1')
    async createTask(@Body() where: any){
        const manager = this.injector.get(TaskManager);
        if(!where.keyword){
            return {
                code: 0,
                msg: 'please select keyword'
            }
        }
        await manager.send({
            topic: '@nger/weibo/real-time-task',
            data: {
                type: 'weibo',
                eid: where.eid,
                keyword: where.keyword
            }
        }).catch(e => {
            console.error(e.message);
            throw e;
        })
        return {
            code: 200,
            msg: `成功创建1个爬取任务`
        }
    }

    /**
     * create topic page links task
     */
    @Post('topic')
    async topic(@Body() where: any) {
        const manager = this.injector.get(TaskManager);
        const platforms = await this.db.manager.find(WbPlatform)
        Promise.all(platforms.map(async it => {
            await manager.send({
                topic: '@nger/weibo/topic-page-links-task',
                data: {
                    type: it.name,
                    eid: where.eid,
                    keyword: where.keyword
                }
            }).catch(e => {
                console.error(e.message);
                throw e;
            })
        }))
        return {
            code: 200,
            msg: `成功创建${platforms.length}个爬取任务`
        }
    }
    get db() {
        return this.injector.get(Db)
    }
    get spilderFactory() {
        return this.injector.get(SpilderFactory)
    }
    @Get('count-info')
    countInfo() {
        const eventCount$ = () => from(this.db.manager.count(WbEvent)).pipe(startWith(0))
        const accountCount$ = () => from(this.db.manager.count(SpilderAccount)).pipe(startWith(0))
        const topicCount$ = () => from(this.db.manager.count(SpilderTopic)).pipe(startWith(0))
        const topicArticleCount$ = () => from(this.db.manager.count(SpilderTopicArticle)).pipe(startWith(0))
        const topicArticleCommentCount$ = () => from(this.db.manager.count(SpilderTopicArticleComment)).pipe(startWith(0))
        const interval$ = interval(1000)
        return combineLatest(
            [
                eventCount$(),
                accountCount$(),
                topicCount$(),
                topicArticleCount$(),
                topicArticleCommentCount$(),
                interval$
            ]
        ).pipe(
            map(([event, account, topic, article, comment, time], index) => {
                return {
                    event, account, topic, article, comment, time
                }
            }),
            catchError((e: any, caught: Observable<any>) => {
                console.log(e.message)
                return caught
            })
        )
    }
}
