import { Controller, Get, Inject } from "@nger/core";
import { Body, Post, QUERY, Query, REQUEST } from "@nger/http";
import { Db } from "@nger/typeorm";
import { Observable, startWith, Subject, switchMap, merge } from "rxjs";
import { SysMessageEntity } from "../entities/message";
import { Amqp } from "../v2/amqp";
import { SystemMessage } from "../v2/message";


@Controller('message')
export class MessageController {
    message!: SystemMessage;
    next: Subject<void> = new Subject();
    constructor(private db: Db, private amqp: Amqp) {
        this.message = new SystemMessage(this.amqp, this.db);
    }

    @Post('read')
    async read(@Body() body: any) {
        const sn = body.sn;
        await this.db.manager.update(SysMessageEntity, { sn }, { status: 1 });
        this.next.next();
        return {
            body
        }
    }

    @Get('/mine')
    mine(@Inject(QUERY) query: URLSearchParams) {
        const uid = query.get('uid')!;
        return merge(this.message.receive(), this.next).pipe(
            startWith({}),
            switchMap(() => {
                return this.findAndCount(uid)
            })
        );
    }

    private findAndCount(uid: string) {
        return new Observable((sub) => {
            this.db.manager.findAndCount(SysMessageEntity, {
                select: ['sn', 'data', 'type', 'status', 'send_date', 'from'],
                where: { to: uid, status: 0 },
                order: { send_date: 'desc' },
                take: 10
            }).then(([list, total]) => {
                sub.next({
                    list: list.map(item => {
                        item.data = JSON.parse(item.data)
                        return item;
                    }), total
                })
            }).catch(e => sub.error(e))
        })
    }
}