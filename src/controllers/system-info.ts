import { Controller, Get } from "@nger/core";
import { interval, map, zip, switchMap, from, catchError, of, Observable, startWith } from 'rxjs';
import { cpu, mem, netstat, drive } from 'node-os-utils'
import { platform } from 'os'
/**
 * [{"name":"WBPSESS","value":"MD8woSgWOYgA6XbWfXpGFoWMnnIEan99X5JgREhUAf0UwZ91RUL7JYNbeIVyU_u3E4GUvhn-p0iK-vlVrUAknFqh6cQCsMAIinPCxpwmyG9lcC-j_MrsDhF40J7QMdrqM1i1cMBoA7dcmbM8J284Mw==","domain":"weibo.com","path":"/","expires":1663813117.094376,"size":159,"httpOnly":true,"secure":true,"session":false,"sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"SCF","value":"Akdfljuy_LX6i-5A4u4EWM0VLLUe0a1u3h5vT6w7FqrCiU8Imd3lYgat4Jc4aphtkGIcm5ZxWI9_Gy9GDxwD-lI.","domain":".weibo.com","path":"/","expires":1979086716.885195,"size":91,"httpOnly":true,"secure":true,"session":false,"sameSite":"None","sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"SUB","value":"_2A25OLgQsDeRhGeFJ7FES9CvJyjWIHXVtWnLkrDV8PUNbmtANLW7xkW9NfyTxuSqhxRPm99bK__-tYJWbv1vHu0XU","domain":".weibo.com","path":"/","expires":-1,"size":93,"httpOnly":true,"secure":true,"session":true,"sameSite":"None","sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"ALF","value":"1695262714","domain":".weibo.com","path":"/","expires":1695262714.885537,"size":13,"httpOnly":false,"secure":true,"session":false,"sameSite":"None","sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"SUBP","value":"0033WrSXqPxfM725Ws9jqgMF55529P9D9Whz.n8cvBpxCSW1JQcNxBGM5JpX5KMhUgL.FoMNS0e0Sh-feK.2dJLoIp7LxKML1KBLBKnLxKqL1hnLBoMNS0M0e0BfSK24","domain":".weibo.com","path":"/","expires":1695262716.885337,"size":132,"httpOnly":false,"secure":true,"session":false,"sameSite":"None","sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"cross_origin_proto","value":"SSL","domain":".weibo.com","path":"/","expires":-1,"size":21,"httpOnly":false,"secure":false,"session":true,"sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"login_sid_t","value":"5df770257eb09808210b4641742d701a","domain":".weibo.com","path":"/","expires":-1,"size":43,"httpOnly":false,"secure":false,"session":true,"sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"XSRF-TOKEN","value":"katIZ2wYraGG4SOnRj0CXe_z","domain":"weibo.com","path":"/","expires":-1,"size":34,"httpOnly":false,"secure":true,"session":true,"sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"SSOLoginState","value":"1663319840","domain":".weibo.com","path":"/","expires":-1,"size":23,"httpOnly":false,"secure":true,"session":true,"sameSite":"None","sameParty":false,"sourceScheme":"Secure","sourcePort":443}]
 */
@Controller('@nger/system')
export class SystemInfo {
    @Get('info')
    info() {
        return interval(1000).pipe(
            startWith(0),
            switchMap(() => {
                const _cpu = from(cpu.usage())
                const _mem = from(mem.info())
                const _net = from(netstat.stats()).pipe(
                    map(res => {
                        if (typeof res === 'string') {
                            return []
                        }
                        if (Array.isArray(res)) {
                            return res;
                        }
                        return [];
                    })
                )
                const _drive = from(drive.info('/')).pipe(catchError((e) => {
                    return of({freeGb: 0, freePercentage: 0, totalGb: 0, usedGb: 0, usedPercentage: 0})
                })) as Observable<any>;
                return zip(_cpu, _mem, _net, _drive)
            }),
            map(([cpu, mem, net, drive]) => {
                const netData = net.reduce((a: any, b: any) => {
                    return {
                        inputBytes: Number(a.inputBytes) + Number(b.inputBytes),
                        outputBytes: Number(a.outputBytes) + Number(b.outputBytes)
                    }
                }, { inputBytes: 0, outputBytes: 0 })
                const driveData = {};
                Object.keys(drive).map(k => {
                    Reflect.set(driveData, k, Number(drive[k]))
                })
                return {
                    mem,
                    platform: platform(),
                    cpu,
                    net: netData,
                    drive: driveData
                }
            }),
            catchError((e) => {
                return of({})
            })
        )
    }
}