#!/usr/bin/env node
import "reflect-metadata";
process.env.TZ = 'Asia/Shanghai';
import { Module, platformCore, APP_ROOT } from '@nger/core';
import { ZookeeperModule } from '@nger/zookeeper';
import { Db, TypeormModule } from '@nger/typeorm';
import { UtilsModule } from '@nger/utils';
import { RabbitMqModule, RabbitmqStarter, TaskManager } from '@nger/rabbitmq';
import { WeiboNodeModule } from "./weibo-node.module";
import { SpilderTopic } from "./entities/spilder-topic";

@Module({
    imports: [
        TypeormModule.forRoot(),
        ZookeeperModule.forRoot(),
        RabbitMqModule.forRoot(),
        UtilsModule,
        WeiboNodeModule,
    ]
})
export class AppFenciModule { }

platformCore([{
    provide: APP_ROOT,
    useValue: process.cwd()
}]).bootstrap(AppFenciModule).then(async injector => {
    try {
        const rabbitmqStarter = injector.get(RabbitmqStarter)
        await rabbitmqStarter.start(false);
        const manager = injector.get(TaskManager)
        const db = injector.get(Db);
        const topics = await db.manager.find(SpilderTopic);
        await Promise.all(topics.map(topic => {
            if (topic.summary.length > 0) {
                manager.send({
                    topic: '@nger/weibo/fenci',
                    data: {
                        id: topic.id,
                        type: 'topic',
                        text: topic.summary
                    }
                })
            }
        }))
        console.log('恭喜您，爬虫节点启动成功')
    } catch (e: any) {
        console.log(e.message)
    }
});
