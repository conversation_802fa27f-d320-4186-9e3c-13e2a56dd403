#!/usr/bin/env node
import "reflect-metadata";
process.env.TZ = 'Asia/Shanghai';
import { Module, platformCore, APP_ROOT } from '@nger/core';
import { ZookeeperModule } from '@nger/zookeeper';
import { Db, TypeormModule } from '@nger/typeorm';
import { UtilsModule } from '@nger/utils';
import { RabbitMqModule, TaskManager } from '@nger/rabbitmq';
import { WeiboNodeModule } from "./weibo-node.module";
@Module({
    imports: [
        TypeormModule.forRoot(),
        ZookeeperModule.forRoot(),
        RabbitMqModule.forRoot(),
        UtilsModule,
        WeiboNodeModule,
    ]
})
export class AppFenciModule { }

platformCore([{
    provide: APP_ROOT,
    useValue: process.cwd()
}]).bootstrap(AppFenciModule).then(async injector => {
    try {
        const manager = injector.get(TaskManager);
        const db = injector.get(Db);
        manager.send({
            topic: '@nger/migration-data-task',
            data: {
                name: '',
                entities: []
            }
        })
    } catch (e: any) {
        console.log(e.message)
    }
});
