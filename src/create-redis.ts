#!/usr/bin/env node
import "reflect-metadata";
process.env.TZ = 'Asia/Shanghai';
import { Module, platformCore, APP_ROOT, Config } from '@nger/core';
import { ZookeeperModule } from '@nger/zookeeper';
import { Db, TypeormModule } from '@nger/typeorm';
import { UtilsModule } from '@nger/utils';
import { RabbitMqModule } from '@nger/rabbitmq';
import { WeiboNodeModule } from "./weibo-node.module";
import { SpilderTopicArticleComment } from "./entities/spilder-topic-article-comment";
import { CommentService } from "./services/comment.service";
import { createWriteStream } from "fs";
import { join } from "path";
@Module({
    imports: [
        TypeormModule.forRoot(),
        ZookeeperModule.forRoot(),
        RabbitMqModule.forRoot(),
        UtilsModule,
        WeiboNodeModule,
    ],
    providers: []
})
export class AppFenciModule { }

platformCore([{
    provide: APP_ROOT,
    useValue: process.cwd()
}]).bootstrap(AppFenciModule).then(async injector => {
    try {
        const db = injector.get(Db);
        const commentService = injector.get(CommentService)
        async function commentPage(page: number) {
            console.log(`comment page ${page}`)
            const deleteFile = createWriteStream(join(__dirname, `delete-${page}.log`))
            const psize = 10 * 10 * 10000;
            let comments = await db.manager.find(SpilderTopicArticleComment, { select: ['platform', 'cid'], take: psize, skip: (page - 1) * psize })
            if (comments.length > 0) {
                let needDeletes = commentService.upgrades(comments)
                deleteFile.write(JSON.stringify(needDeletes));
                needDeletes = [];
                comments = [];
                await commentPage(page + 1);
            } else {
                console.log(`comment complete`)
            }
        }
        await commentPage(1)
    } catch (e: any) {
        console.log(e.message)
    }
});

process.on('uncaughtException', (error: Error, origin: any) => {
    console.log(error.message)
})

process.on('unhandledRejection', (reason: unknown, promise: Promise<unknown>) => {
    console.log(reason)
    promise.catch(e => console.log(e.message))
})

