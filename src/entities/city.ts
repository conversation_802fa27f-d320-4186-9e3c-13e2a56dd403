import { Component, Widget } from '@nger/rest';
import { Column, Entity, Join<PERSON><PERSON>umn, PrimaryGeneratedColumn, Tree, TreeChildren, TreeParent } from 'typeorm'
/**
 * 全国地区代码表
 */
@Component({
    title: '地区'
})
@Entity({
    name: 'wb_city'
})
@Tree('closure-table')
export class WbCity {

    @Widget({
        title: '序号',
        hideInAdd: true,
        hideInSearch: true,
        sortable: true
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '城市名称'
    })
    @Column()
    title!: string;

    @Widget({
        title: '城市代码',
    })
    @Column({
        default: ''
    })
    code!: string;

    @Widget({
        title: '上级城市',
        hideInSearch: true,
        widget: {
            type: 'selector',
            from: 'wb_city',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    })
    @Column({
        nullable: true
    })
    pid!: number;

    @Widget({
        title: '上级城市'
    })
    @TreeParent()
    @JoinColumn({
        name: 'pid',
    })
    parent!: WbCity;

    @Widget({
        title: '下级城市'
    })
    @TreeChildren()
    children!: WbCity[];
}