import { Db } from '@nger/typeorm';
import { Between, Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { Component, Widget } from "@nger/rest";

/**
 * n 名词
 * d 副词
 * a 形容词
 * v 动词
 */

/**
 * 核心是帖子
 */
@Component({
    title: '帖子NLP'
})
@Entity({
    name: 'spilder_count_article'
})
export class CountArticle {
    @Widget({
        title: '编号'
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '发布时间'
    })
    @Column({
        name: 'post_at'
    })
    postAt!: Date

    @Widget({
        title: '事件'
    })
    @Column()
    eid!: number;

    @Widget({
        title: '平台'
    })
    @Column({
        default: ''
    })
    platform!: string;

    /**
     * 帖子
     */
    @Widget({
        title: '帖子'
    })
    @Column()
    aid!: number;
    /**
     * 长度
     */
    @Widget({
        title: '长度'
    })
    @Column({
        default: 0
    })
    l!: number;
    /**
     * 名词
     */
    @Widget({
        title: '名词N'
    })
    @Column({
        default: 0
    })
    n!: number;

    @Widget({
        title: '名词'
    })
    @Column('simple-array', {
        name: 'n_words'
    })
    nWords!: string[]

    /**
     * 副词
     */
    @Widget({
        title: '副词N'
    })
    @Column({
        default: 0
    })
    d!: number;

    @Widget({
        title: '副词'
    })
    @Column('simple-array', {
        name: 'd_words'
    })
    dWords!: string[]

    /**
     * 形容词
     */
    @Widget({
        title: '形容词N'
    })
    @Column({
        default: 0
    })
    a!: number;

    @Widget({
        title: '形容词'
    })
    @Column('simple-array', {
        name: 'a_words'
    })
    aWords!: string[]

    /**
     * 动词
     */
    @Widget({
        title: '动词N'
    })
    @Column({
        default: 0
    })
    v!: number;

    @Widget({
        title: '动词'
    })
    @Column('simple-array', {
        name: 'v_words'
    })
    vWords!: string[]

    @Widget({
        hideInTable: true
    })
    @Column({
        default: 0
    })
    negative!: number;

    @Widget({
        hideInTable: true
    })
    @Column('simple-array', {
        name: 'negative_words'
    })
    negativeWords!: string[]

    @Widget({
        hideInTable: true
    })
    @Column({
        default: 0
    })
    neutral!: number;

    @Widget({
        hideInTable: true
    })
    @Column('simple-array', {
        name: 'neutral_words'
    })
    neutralWords!: string[]

    @Widget({
        hideInTable: true
    })
    @Column({
        default: 0
    })
    positive!: number;

    @Widget({
        hideInTable: true
    })
    @Column('simple-array', {
        name: 'positive_words'
    })
    positiveWords!: string[]

    @Widget({
        title: '可读性'
    })
    @Column({
        type: 'float'
    })
    readability!: number;

    /**
     * 情感强度
     */
    @Widget({
        title: '情感强度'
    })
    @Column({
        default: 0
    })
    emotion!: number;
}


@Component({
    title: '评论NLP'
})
@Entity({
    name: 'spilder_count_article_comment'
})
export class CountArticleComment {
    @Widget({
        title: '编号'
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '发布时间'
    })
    @Column({
        name: 'post_at'
    })
    postAt!: Date

    @Widget({
        title: 'child'
    })
    @Column({
        name: 'is_child'
    })
    isChild!: boolean;

    @Widget({
        title: '事件'
    })
    @Column()
    eid!: number;

    @Widget({
        title: '平台'
    })
    @Column({
        default: ''
    })
    platform!: string;

    /**
     * 评论id
     */
     @Widget({
        title: '评论'
    })
    @Column()
    cid!: number;
    /**
     * 长度
     */
     @Widget({
        title: '长度'
    })
    @Column({
        default: 0
    })
    l!: number;
    /**
     * 名词
     */
    @Widget({
        title: '名词N'
    })
    @Column({
        default: 0
    })
    n!: number;

    @Widget({
        title: '名词'
    })
    @Column('simple-array', {
        name: 'n_words'
    })
    nWords!: string[]

    /**
     * 副词
     */
    @Widget({
        title: '副词N'
    })
    @Column({
        default: 0
    })
    d!: number;

    @Widget({
        title: '副词'
    })
    @Column('simple-array', {
        name: 'd_words'
    })
    dWords!: string[]

    /**
     * 形容词
     */
    @Widget({
        title: '形容词N'
    })
    @Column({
        default: 0
    })
    a!: number;

    @Widget({
        title: '形容词'
    })
    @Column('simple-array', {
        name: 'a_words'
    })
    aWords!: string[]

    /**
     * 动词
     */
     @Widget({
        title: '动词N'
    })
    @Column({
        default: 0
    })
    v!: number;
    @Widget({
        title: '动词'
    })
    @Column('simple-array', {
        name: 'v_words'
    })
    vWords!: string[]

    @Widget({
        hideInTable: true
    })
    @Column({
        default: 0
    })
    negative!: number;
    @Widget({
        hideInTable: true
    })
    @Column('simple-array', {
        name: 'negative_words'
    })
    negativeWords!: string[]
    @Widget({
        hideInTable: true
    })
    @Column({
        default: 0
    })
    neutral!: number;
    @Widget({
        hideInTable: true
    })
    @Column('simple-array', {
        name: 'neutral_words'
    })
    neutralWords!: string[]
    @Widget({
        hideInTable: true
    })
    @Column({
        default: 0
    })
    positive!: number;
    @Widget({
        hideInTable: true
    })
    @Column('simple-array', {
        name: 'positive_words'
    })
    positiveWords!: string[]
    @Widget({
        title: '可读性'
    })
    @Column({
        type: 'float'
    })
    readability!: number;
}

export function analysisEvent(eid: number) {
    async function getResult(db: Db, dates: Date[]) {
        const list: [Date, Date][] = [];
        const length = dates.length;
        for (let i = 0; i < dates.length; i++) {
            if (i + 1 < length) {
                list.push([
                    dates[i],
                    dates[i + 1]
                ])
            }
        }
        /**
         * 文章的总和
         */
        const SA = await Promise.all(list.map(([start, end]) => {
            return getSA(db, start, end)
        }));
        const SAC = await Promise.all(list.map(([start, end]) => {
            return getSAC(db, start, end)
        }));
        const COUNT_A = SA.map(it => count(it))
        const COUNT_C = SAC.map(it => count(it))
        const POS_A = SA.map(it => pos(it))
        const POS_C = SAC.map(it => pos(it))
        const NEG_A = SA.map(it => neg(it))
        const NEG_C = SAC.map(it => neg(it))
        const AVG_LEN_A = SA.map(it => avg_len(it))
        const AVG_LEN_C = SAC.map(it => avg_len(it))
        const CM = SA.filter(it => getCmCount(it))
        return {
            COUNT_A,
            COUNT_C,
            POS_A,
            POS_C,
            NEG_A,
            NEG_C,
            AVG_LEN_A,
            AVG_LEN_C,
            CM
        }
    }

    return getResult;

    function getCmCount(list: any[]) {
        return 0;
    }

    function avg_len(list: any[]) {
        return 0
    }

    function neg(list: any[]) {
        return 0;
    }

    function count(list: any[]) {
        return list.length;
    }
    /**
     * 正向情感总数/总数
     */
    function pos(list: (CountArticle | CountArticleComment)[]) {
        const positive = list.map(it => it.positive).reduce((a, b) => {
            return a + b
        }, 0)
        return positive / count(list);
    }
    /**
     * 平均长度
     */
    function avgLength(list: (CountArticle | CountArticleComment)[]) {
        const sum = list.map(li => li.l).reduce((a, b) => a + b, 0);
        return sum / count(list)
    }

    async function getSAC(db: Db, start: Date, end: Date) {
        return await db.manager.find(CountArticleComment, {
            where: {
                postAt: Between(start, end),
                eid: eid
            }
        })
    }

    async function getSA(db: Db, start: Date, end: Date) {
        return await db.manager.find(CountArticle, {
            where: {
                postAt: Between(start, end),
                eid: eid
            }
        })
    }
}

