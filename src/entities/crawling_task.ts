
export interface Request { }
export class CrawlingTask {
    id!: number;
    // 类型 search position
    type!: string;
    // 关键字
    condiction!: string;
    // 平台
    platform!: string;
    // 开始时间
    start!: Date;
    // 结束时间
    end!: Date;
    // 步长
    step!: number;
    // 定时
    schedule!: string;
}
// 引擎负责控制数据流在系统中所有组件中流动, 并在相应动作发生时触发事件
export class Engine {
    spiders: Spider[] = [];
    start() {
        this.spiders.map(spider => spider.start())
    }
    addUrl(url: string) { }
}
// 调度器从引擎接受request并将他们入队, 以便之后引擎请求他们时提供给引擎
export class Scheduler {
    addRequest(req: Request) { }
}
// 下载器负责获取页面数据并提供给引擎，而后提供给spider
export class Downloader {

}

export class Spider {
    start() {
        // 找到URL地址
    }
}
// 典型的处理有清理、 验证及持久化
export class Pipeline { }

// 下载器中间件是在引擎及下载器之间的特定钩子
export class DownloaderMiddleware { }
// Spider中间件是在引擎及Spider之间的特定钩子
export class SpiderMiddleware { }

