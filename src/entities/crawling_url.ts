import { <PERSON><PERSON><PERSON>, CreateDateC<PERSON>umn, <PERSON><PERSON>ty, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { Keyword } from './keyword';

@Entity({
    name: 'crawling_url'
})
export class CrawlingUrl {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({
        unique: true,
        type: 'varchar',
        width: 255
    })
    link!: string;

    @Column({
        type: 'varchar',
        width: 8
    })
    type!: string;

    @ManyToOne(() => Keyword, k => k.id, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'keyword_id'
    })
    keyword!: Keyword;

    @Column({
        name: 'keyword_id',
        type: 'int'
    })
    keyword_id!: number;

    @CreateDateColumn()
    create_date!: Date;

    @UpdateDateColumn()
    update_date!: Date;

    @Column({
        type: 'int4',
        default: 0
    })
    procress!: number;
}