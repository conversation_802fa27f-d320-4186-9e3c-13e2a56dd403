import { Component, Widget } from "@nger/rest";
import { Column, Entity, PrimaryColumn } from "typeorm";

@Component({
    title: '行业类型'
})
@Entity({
    name: 'wb_event_category'
})
export class WbEventCategory {
    @Widget({
        title: '类型编号',
        hideInSearch: true,
    })
    @PrimaryColumn()
    code!: string;

    @Widget({
        title: '类型名称'
    })
    @Column()
    title!: string;
}
