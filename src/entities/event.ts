
/**
 * 事件
 */

import { Injector, Next } from "@nger/core";
import { Context } from "@nger/http";
import { Component, Widget } from "@nger/rest";
import { Column, Entity, JoinColumn, ManyToOne, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import { WbCity } from "./city";
import { WbEventCategory } from "./event-category";
import { WbEventType } from "./event-type";
import { SpilderTopic } from "./spilder-topic";
import { SpilderTopicArticle } from "./spilder-topic-article";
import { SpilderTopicArticleComment } from "./spilder-topic-article-comment";

@Component({
    title: '事件',
    table: {
        loginWeibo: {
            title: '登录',
            icon: 'icon-denglu1',
            type: 'dropdown',
            handler: async (ctx: Context, next: Next, injector: Injector) => {
                // 搜索关键字
                if (next) await next();
            }
        },
        searchZhihu: {
            title: '知乎',
            icon: 'icon-shejiaotubiao-46',
            type: 'zhihu',
            handler: async (ctx: Context, next: Next, injector: Injector) => {
                // 搜索关键字
                if (next) await next();
            }
        },
        searchToutiao: {
            title: '头条',
            icon: 'icon-toutiaoyangshi',
            type: 'toutiao',
            handler: async (ctx: Context, next: Next, injector: Injector) => {
                // 搜索关键字
                if (next) await next();
            }
        }
    }
})
@Entity({
    name: 'wb_event'
})
export class WbEvent {

    @Widget({
        title: '序号',
        hideInAdd: true,
        hideInSearch: true,
        sortable: true,
        hideInEdit: true
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '事件名称'
    })
    @Column()
    title!: string;

    @Widget({
        title: '地域',
        hideInSearch: true,
        path: 'city.title',
        widget: {
            type: 'selector',
            from: 'wb_city',
            config: {
                label: 'title',
                value: 'code'
            }
        }
    })
    @Column()
    address!: string;

    @Widget({
        title: '地域'
    })
    @ManyToOne(() => WbCity, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'address',
        referencedColumnName: 'code'
    })
    city!: WbCity;

    @Widget({
        title: '事件性质',
        path: 'typeEntity.title',
        widget: {
            type: 'selector',
            from: 'wb_event_type',
            config: {
                value: 'code',
                label: 'title'
            }
        }
    })
    @Column({
        nullable: true
    })
    type!: string;

    @Widget({
        title: '事件性质'
    })
    @ManyToOne(() => WbEventType, {
        createForeignKeyConstraints: true
    })
    @JoinColumn({
        name: 'type',
        referencedColumnName: 'code'
    })
    typeEntity!: WbEventType;

    @Widget({
        title: '行业类型',
        path: 'categoryEntity.title',
        widget: {
            type: 'selector',
            from: 'wb_event_category',
            config: {
                value: 'code',
                label: 'title'
            }
        }
    })
    @Column({
        nullable: true
    })
    category!: string;

    @Widget({
        title: '行业类型'
    })
    @ManyToOne(() => WbEventCategory, {
        createForeignKeyConstraints: true
    })
    @JoinColumn({
        name: 'category',
        referencedColumnName: 'code'
    })
    categoryEntity!: WbEventCategory;

    @Widget({
        title: '备注',
        hideInSearch: true,
        hideInTable: true,
        required: false
    })
    @Column({
        default: '',
    })
    desc!: string;

    @Widget({
        title: '话题',
        widget: {
            type: 'array',
            where: {
                eid: 'id'
            }
        }
    })
    @OneToMany(() => SpilderTopic, it => it.eid, {
        createForeignKeyConstraints: false
    })
    topics!: SpilderTopic[];

    @Widget({
        title: '帖子',
        widget: {
            type: 'array',
            where: {
                eid: 'id'
            }
        }
    })
    @OneToMany(() => SpilderTopicArticle, it => it.eid, {
        createForeignKeyConstraints: false
    })
    articles!: SpilderTopicArticle[];

    @Widget({
        title: '评论',
        widget: {
            type: 'array',
            where: {
                eid: 'id'
            }
        }
    })
    @OneToMany(() => SpilderTopicArticleComment, it => it.eid, {
        createForeignKeyConstraints: false
    })
    comments!: SpilderTopicArticleComment[];

    @Widget({
        title: '关键字',
        hideInSearch: true,
        hideInTable: true
    })
    @Column('simple-array', {
        nullable: true
    })
    keywords!: string[];
}
