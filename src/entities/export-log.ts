import { Component, Widget } from "@nger/rest";
import { Column, Entity, PrimaryGeneratedColumn, CreateDateColumn } from "typeorm";

@Component({
    title: '导出日志',
    hideAdd: true,
    hideDelete: true,
    hideEdit: true
})
@Entity({
    name: 'spilder_export_log'
})
export class SpilderExportLog {

    @Widget({
        title: '序号',
        sortable: true
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '用户名',
    })
    @Column()
    username!: string;

    @Widget({
        title: '用户ID',
        hideInTable: true,
        hideInSearch: true
    })
    @Column()
    uid!: number;

    @Widget({
        title: '导出日期',
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true,
    })
    @CreateDateColumn()
    exportDate!: Date;

    @Widget({
        title: '事件ID',
        hideInTable: true,
        hideInSearch: true
    })
    @Column()
    eid!: number;

    @Widget({
        title: '事件名称'
    })
    @Column()
    event_title!: string;
}
