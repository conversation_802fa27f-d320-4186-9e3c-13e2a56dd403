import { Component, Widget } from "@nger/rest";
import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { WbEvent } from "./event";

@Component({
    title: '政府公告'
})
@Entity({
    name: 'spider_gonggao'
})
export class Gonggao {
    @Widget({
        title: '序号',
        sortable: true,
        hideInAdd: true,
        hideInEdit: true,
        hideInSearch: true,
        widget: {
            type: 'number'
        }
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '发布者',
        hideInSearch: true,
    })
    @Column({
        default: ''
    })
    author!: string;

    @Widget({
        title: '公告内容',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        type: 'text'
    })
    content!: string;

    @Widget({
        title: '发布时间',
        sortable: true,
        widget: {
            type: 'date',
            config: {
                showTime: true
            }
        }
    })
    @Column({
        nullable: true
    })
    post_at!: Date;

    @Widget({
        title: '所属事件',
        path: 'event.title',
        widget: {
            type: 'selector',
            from: 'wb_event',
            config: {
                value: 'id',
                label: 'title'
            }
        }
    })
    @Column({
        nullable: true
    })
    eid!: string;

    @Widget({
        title: '所属事件'
    })
    @ManyToOne(() => WbEvent, {
        createForeignKeyConstraints: true
    })
    @JoinColumn({
        name: 'eid',
        referencedColumnName: 'id'
    })
    event!: WbEvent;

    @Widget({
        title: '回应语气',
        hideInSearch: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                    label: '陈述',
                    value: 'A1'
                }, {
                    label: '疑问',
                    value: 'A2'
                }, {
                    label: '感叹',
                    value: 'A3'
                }, {
                    label: '祈祷',
                    value: 'A4'
                }]
            }
        }
    })
    @Column({
        default: ''
    })
    yuqi!: string;

    @Widget({
        title: '回应态度',
        hideInSearch: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                    label: '关心',
                    value: 'B1'
                }, {
                    label: '追责',
                    value: 'B2'
                }, {
                    label: '称赞',
                    value: 'B3'
                }, {
                    label: '补救',
                    value: 'B4'
                }, {
                    label: '完全否认',
                    value: 'B5'
                }, {
                    label: '部分否认',
                    value: 'B6'
                }, {
                    label: '解释',
                    value: 'B7'
                }, {
                    label: '承诺',
                    value: 'B8'
                }, {
                    label: '转移',
                    value: 'B9'
                }]
            }
        }
    })
    @Column({
        default: ''
    })
    taidu!: string;

    @Widget({
        title: '情感强度',
        hideInSearch: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                    label: '客观',
                    value: 0
                },{
                    label: '较弱',
                    value: 1
                },{
                    label: '弱',
                    value: 2
                },{
                    label: '强',
                    value: 3
                },{
                    label: '级强',
                    value: 4
                }]
            }
        }
    })
    @Column()
    qiangdu!: string;

    @Widget({
        title: '回应方式',
        hideInSearch: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                    label: '响应型',
                    value: 'D1'
                }, {
                    label: '处置弱型',
                    value: 'D2'
                }, {
                    label: '处置强型',
                    value: 'D3'
                }, {
                    label: '长期行动型',
                    value: 'D4'
                }, {
                    label: '政策型',
                    value: 'D5'
                }]
            }
        }
    })
    @Column({
        default: ''
    })
    fangshi!: string;

    @Widget({
        title: '公告风格',
        hideInSearch: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                    label: '真诚亲民',
                    value: 'C1'
                }, {
                    label: '诙谐幽默',
                    value: 'C2'
                }, {
                    label: '客观真实',
                    value: 'C3'
                }, {
                    label: '古板僵化',
                    value: 'C4'
                }, {
                    label: '避重就轻或避硬就软',
                    value: 'C5'
                }, {
                    label: '措辞不当',
                    value: 'C6'
                }, {
                    label: '说教口吻或居高临下',
                    value: 'C7'
                }]
            }
        }
    })
    @Column({
        default: ''
    })
    fengge!: string;

    @Widget({
        title: '判断依据',
        hideInSearch: true,
        hideInTable: true,
        widget: {
            type: 'string'
        }
    })
    @Column({
        default: ''
    })
    reason!: string;
}
