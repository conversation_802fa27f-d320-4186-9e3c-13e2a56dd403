import { Component, Widget } from "@nger/rest";
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from "typeorm";


@Component({
    title: '关键字'
})
@Entity({
    name: 'spider_keyword'
})
export class Keyword { 
    @Widget({
        title: '序号',
        sortable: true,
        hideInAdd: true,
        hideInEdit: true,
        widget: {
            type: 'number'    
        }
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '关键字',
    })
    @Column()
    value!: string;

    @Widget({
        title: '关键字状态',
        widget: {
            type: 'radio',
            config: {
                options: [{
                    label: '禁用',
                    value: -1
                }, {
                    label: '启用',
                    value: 1
                }]
            }
        }
    })
    @Column({
        default: 0
    })
    status!: number;

    @Widget({
        title: '创建日期',
        hideInAdd: true,
        hideInEdit: true,
        hideInSearch: true
    })
    @CreateDateColumn({
        name: 'create_date'
    })
    create_date!: Date;

}
