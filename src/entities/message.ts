import { Column, CreateDateColumn, Entity, PrimaryColumn, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity({
    name: 'sys_message'
})
export class SysMessageEntity {

    @PrimaryColumn()
    sn!: string;

    @Column()
    from!: string;

    @Column({
        nullable: true
    })
    to!: string;

    @Column()
    type!: string;

    @Column({
        type: 'text'
    })
    data!: string;

    @Column({
        default: 0
    })
    status!: number;

    @Column()
    send_date!: Date;

    @CreateDateColumn()
    create_date!: Date;

    @UpdateDateColumn()
    update_date!: Date;
}