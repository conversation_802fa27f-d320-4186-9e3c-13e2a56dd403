import { Column, CreateDateColumn, <PERSON>tity, PrimaryGeneratedColumn, Unique, UpdateDateColumn } from "typeorm";


@Entity({
    name: 'sys_news'
})
export class News {
    /**
     * 编号
     */
    @PrimaryGeneratedColumn()
    id!: number;

    /**
     * 平台
     */
    @Column({
        width: 16
    })
    platform!: string;

    /**
     * 类型
     */
    @Column({
        width: 16
    })
    type!: string;

    /**
     * 内容
     */
    @Column({
        type: 'jsonb'
    })
    data: any;

    /**
     * 点赞
     */
    @Column({
        type: 'jsonb'
    })
    like: any;

    /**
     * 分享
     */
    @Column({
        type: 'jsonb'
    })
    share: any;

    /**
     * 评论
     */
    @Column({
        type: 'jsonb'
    })
    comment: any;

    /**
     * 用户
     */
    @Column({
        type: 'jsonb'
    })
    user: any;
    /**
     * 爬取方式
     */
    @Column({
        width: 320
    })
    from!: string;

    @Column({
        width: 16
    })
    aid!: string;

    @Column({
        width: 12
    })
    uid!: string;

    @Column()
    post_at!: Date;

    // nlp
    @Column()
    readablity!: number;

    @Column()
    word_num!: number;

    @Column({
        type: 'jsonb'
    })
    cut: any;
    // 关键字
    @Column({
        type: 'varchar',
        length: 120
    })
    keywords!: string;
    // 情感强度
    @Column()
    negative_probs!: number;

    @Column()
    positive_probs!: number;

    @Column({
        width: 12
    })
    sentiment_key!: string;

    @Column()
    sentiment_label!: number;

    @Column({
        type: 'jsonb',
        default: []
    })
    comment_cut: any;

    @Column({
        type: 'jsonb',
        default: []
    })
    comment_emotion: any;

    @Column()
    comment_readablity_avg!: number;

    @Column()
    comment_positive_avg!: number;

    @Column()
    comment_negative_avg!: number;

    @Column()
    comment_negative_count!: number;

    @Column()
    comment_word_num_avg!: number;

    @Column()
    comment_positive_count!: number;

    @Column({
        type: 'jsonb',
        default: []
    })
    share_cut: any;

    @Column({
        type: 'jsonb',
        default: []
    })
    share_emotion: any;

    @Column()
    share_readablity_avg!: number;

    @Column()
    share_positive_avg!: number;

    @Column()
    share_negative_avg!: number;

    @Column()
    share_negative_count!: number;

    @Column()
    share_positive_count!: number;

    @Column()
    share_word_num_avg!: number;

    @CreateDateColumn()
    create_date!: Date;

    @UpdateDateColumn()
    update_date!: Date;
}
