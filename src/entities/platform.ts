import { Widget, Component } from "@nger/rest";
import { Column, Entity, PrimaryGeneratedColumn, Unique } from "typeorm";

@Component({
    title: '平台'
})
@Entity({
    name: 'wb_platform'
})
@Unique(['name'])
export class WbPlatform {

    @Widget({
        title: '序号',
        sortable: true,
        hideInAdd: true,
        hideInEdit: true,
        widget: {
            type: 'number'    
        }
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '平台名称',
        hideInSearch: true
    })
    @Column()
    title!: string;

    @Widget({
        title: '代号'
    })
    @Column()
    name!: string;

    @Widget({
        title: '图标',
        hideInSearch: true
    })
    @Column()
    icon!: string;

    @Widget({
        title: '登录地址',
        hideInAdd: true,
        hideInDetail: true,
        hideInEdit: true,
        hideInSearch: true,
        hideInTable: true,
        required: false
    })
    @Column({
        default: ''
    })
    loginUrl!: string;

    @Widget({
        title: '绑定用户',
        required: false,
        hideInAdd: true,
        hideInSearch: true,
        hideInEdit: true
    })
    @Column({
        default: ''
    })
    uid!: string;

    @Widget({
        title: '用户昵称',
        required: false,
        hideInAdd: true,
        hideInSearch: true,
        hideInEdit: true
    })
    @Column({
        default: ''
    })
    nickname!: string;

    @Widget({
        title: '是否有效',
        required: false,
        hideInAdd: true,
        hideInSearch: true,
        hideInEdit: true
    })
    @Column({
        default: 0
    })
    status!: number;

    @Widget({
        title: '登录凭证',
        hideInTable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInDetail: true,
        hideInEdit: true
    })
    @Column('simple-json', {
        nullable: true
    })
    cookies!: any[];
}