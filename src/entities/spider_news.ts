import { Component, Widget } from "@nger/rest";
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Component({
    title: '新闻管理',
    hideAdd: true
})
@Entity({
    name: 'spider_news'
})
export class SpiderNews {
    @Widget({
        hideInAdd: true,
        hideInEdit: true,
        sortable: true,
        title: '序号'
    })
    @PrimaryGeneratedColumn()
    nid!: number;
    // 平台
    @Widget({
        hideInSearch: true
    })
    @Column()
    platform!: string;
    // 链接 唯一
    @Widget({
        hideInSearch: true
    })
    @Column({
        unique: true
    })
    url!: string;
    // 更新时间
    @Widget({
        hideInSearch: true
    })
    @Column()
    updated_time!: number;
    // 创建时间
    @Column()
    @Widget({
        hideInSearch: true
    })
    created_time!: number;
    // 分享
    @Column({
        default: 0
    })
    @Widget({
        hideInSearch: true
    })
    share_count!: number;
    @Column({
        type: 'jsonb'
    })
    @Widget({
        hideInSearch: true
    })
    share: any[] = [];
    // 赞同
    @Column({
        default: 0
    })
    @Widget({
        hideInSearch: true
    })
    voteup_count!: number;
    @Column({
        type: 'jsonb'
    })
    @Widget({
        hideInSearch: true
    })
    voteup: any[] = []
    // 评论
    @Column({
        default: 0
    })
    @Widget({
        hideInSearch: true
    })
    comment_count!: number;
    @Column({
        type: 'jsonb'
    })
    @Widget({
        hideInSearch: true
    })
    comment: any[] = [];
    // 文章id
    @Widget({
        hideInSearch: true
    })
    @Column()
    id!: string;
    // 标题
    @Widget({
        hideInSearch: true
    })
    @Column({
        nullable: true
    })
    title!: string;
    // 内容
    @Widget({
        hideInSearch: true
    })
    @Column({
        nullable: true
    })
    content!: string;
    // 简介
    @Widget({
        hideInSearch: true
    })
    @Column({
        nullable: true
    })
    excerpt!: string;
    // 图片
    @Widget({
        hideInSearch: true
    })
    @Column('simple-array')
    thumbnails!: string[];

    // 作者唯一标识
    @Widget({
        hideInSearch: true
    })
    @Column({
        nullable: true
    })
    author!: string;

    // 文章类型
    @Widget({
        hideInSearch: true
    })
    @Column()
    article_type: string = 'article'
    // 问题
    @Widget({
        hideInSearch: true
    })
    @Column({
        nullable: true
    })
    question_id?: string;

    @Widget({
        hideInSearch: true
    })
    @Column('simple-array')
    keywords!: string[];

    @Widget({
        hideInSearch: true
    })
    @Column('simple-json')
    extend: any;

    // 情感值
    @Widget({
        hideInSearch: true
    })
    @Column({
        default: 0
    })
    emotion!: number;

    // 可读性
    @Widget({
        hideInSearch: true
    })
    @Column({
        default: 0
    })
    readablity!: number;

    // 话题
    @Widget({
        hideInSearch: true
    })
    @Column({
        nullable: true
    })
    topic!: string;

    // 爬取时间
    @Widget({
        hideInSearch: true
    })
    @CreateDateColumn()
    spider_create_time!: Date;

    // 最后一次爬取更新时间
    @Widget({
        hideInSearch: true
    })
    @UpdateDateColumn()
    spider_update_time!: Date;
}
