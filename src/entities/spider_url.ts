import { Column, Entity, Index, PrimaryGeneratedColumn, Unique, CreateDateColumn } from "typeorm";

@Entity({
    name: 'spider_url'
})
@Unique(['url', 'platform'])
export class SpiderUrl {

    @PrimaryGeneratedColumn()
    id!: number;

    @Column()
    url!: string;

    @Column({
        nullable: true
    })
    account_url!: string;

    @Column()
    @Index('spider_url_platform')
    platform!: string;

    @Column()
    @Index('spider_url_type')
    type!: string;

    @Column({
        nullable: true
    })
    @Index('spider_url_keyword')
    keyword!: string

    @Column({
        default: 'waiting'
    })
    @Index('spider_url_status')
    status!: string

    @CreateDateColumn({
        name: 'create_date'
    })
    create_date!: Date

}
