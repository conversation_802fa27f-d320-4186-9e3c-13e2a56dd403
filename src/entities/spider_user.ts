import { Widget } from "@nger/rest";
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, Unique, UpdateDateColumn } from "typeorm";

@Entity({
    name: 'spider_user'
})
@Unique(['platform', 'username'])
export class SpiderUser {
    @Widget({
        title: '序号',
        sortable: true,
        hideInAdd: true,
        hideInEdit: true,
        widget: {
            type: 'number'
        }
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '平台'
    })
    @Column()
    platform!: string;

    @Widget({
        title: '用户名'
    })
    @Column({
        default: ''
    })
    username!: string;

    @Widget({
        title: '状态'
    })
    @Column({
        default: 1
    })
    status!: number;

    @Widget({
        title: '登录凭证',
        hideInEdit: true,
        hideInAdd: true,
        hideInSearch: true
    })
    @Column('simple-json', {
        nullable: true
    })
    cookies!: any;

    @Widget({
        title: '创建时间',
        hideInEdit: true,
        hideInAdd: true,
        hideInSearch: true
    })
    @CreateDateColumn({
        name: 'create_date'
    })
    create_date!: Date;

    @Widget({
        title: '创建时间',
        hideInEdit: true,
        hideInAdd: true,
        hideInSearch: true
    })
    @UpdateDateColumn({
        name: 'update_date'
    })
    update_date!: Date;
}