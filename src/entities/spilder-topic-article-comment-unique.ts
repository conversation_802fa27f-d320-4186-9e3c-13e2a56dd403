import { Component } from "@nger/rest";
import { Column, Entity, PrimaryGeneratedColumn, Unique } from "typeorm";

@Component({
    title: '评论仿重表'
})
@Entity({
    name: 'spilder_topic_article_comment_unique'
})
@Unique(['platform', 'cid'])
export class SpilderTopicArticleCommentUnique {
    @PrimaryGeneratedColumn()
    uid!: number;
    @Column()
    platform!: string;
    @Column({
        type: 'bigint'
    })
    cid!: number;
}
