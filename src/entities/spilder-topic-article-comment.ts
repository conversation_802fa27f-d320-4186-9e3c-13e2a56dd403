import { Component, Widget } from "@nger/rest";
import { Column, CreateDateColumn, Entity, JoinColumn, ManyToOne, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { WbEvent } from "./event";
import { WbPlatform } from "./platform";
import { SpilderTopic } from "./spilder-topic";
import { SpilderTopicArticle } from "./spilder-topic-article";


@Component({
    title: '评论'
})
@Entity({
    name: 'spilder_topic_article_comment'
})
export class SpilderTopicArticleComment {
    @Widget({
        title: '序号',
        hideInSearch: true,
        sortable: true
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '平台',
        path: 'platformEntity.title',
        widget: {
            type: 'selector',
            from: 'wb_platform',
            config: {
                label: 'title',
                value: 'name'
            }
        }
    })
    @Column({
        default: 'weibo',
    })
    platform!: string;

    @Widget({
        title: '平台',
        hideInTable: true
    })
    @ManyToOne(() => WbPlatform, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'platform',
        referencedColumnName: 'name'
    })
    platformEntity!: WbPlatform;

    @Widget({
        title: '事件',
        path: 'event.title',
        widget: {
            type: 'selector',
            from: 'wb_event',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    })
    @Column({
        default: 0
    })
    eid!: number;

    @Widget({
        title: '事件'
    })
    @ManyToOne(() => WbEvent, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'eid',
        referencedColumnName: 'id'
    })
    event!: WbEvent;

    @Widget({
        title: '话题',
        path: 'topic.title',
        widget: {
            type: 'selector',
            from: 'spilder_topic',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    })
    @Column({
        default: 0
    })
    tid!: number;

    @Widget({
        title: '话题'
    })
    @ManyToOne(() => SpilderTopic, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'tid',
        referencedColumnName: 'id'
    })
    topic!: SpilderTopic;

    @Widget({
        title: '帖子',
        path: 'article.text_raw',
        width: '220px',
        widget: {
            type: 'selector',
            from: 'spilder_topic_article',
            config: {
                label: 'text_raw',
                value: 'id'
            }
        }
    })
    @Column({
        default: 0
    })
    aid!: number;

    @Widget({
        title: '文章'
    })
    @ManyToOne(() => SpilderTopicArticle, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'aid',
        referencedColumnName: 'id'
    })
    article!: SpilderTopicArticle;

    @Widget({
        title: '评论时间',
        hideInSearch: true
    })
    @Column({
        nullable: true
    })
    created_at!: Date;

    @Widget({
        title: '评论ID',
        hideInSearch: true
    })
    @Column({
        type: 'bigint',
        default: 0
    })
    cid!: number;

    @Widget({
        title: '楼层',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: 0
    })
    floor_number!: number;

    @Widget({
        title: '评论文本',
        hideInSearch: true,
        widget: {
            type: 'textarea',
        }
    })
    @Column({
        default: ''
    })
    text!: string;

    @Widget({
        title: '来源',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    source!: string;

    @Widget({
        title: '用户ID',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    uid!: string;

    @Widget({
        title: '用户昵称',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    nickname!: string;

    @Widget({
        title: '点赞',
        hideInSearch: true
    })
    @Column({
        type: 'bigint',
        nullable: true
    })
    like_counts!: number;

    @Widget({
        title: '子评论',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        type: 'bigint',
        nullable:true
    })
    total_number!: number;

    @Widget({
        title: 'ID',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: 0
    })
    max_id!: number;

    @Widget({
        title: '是否被作者喜欢',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        name: 'is_liked_by_mblog_author',
        default: false
    })
    isLikedByMblogAuthor!: boolean;

    @Widget({
        title: '上级ID',
        hideInSearch: true
    })
    @Column({
        type: 'bigint',
        nullable: true
    })
    pid!: number;

    @Widget({
        title: '上级评论'
    })
    @ManyToOne(() => SpilderTopicArticleComment, it => it.pid, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'pid',
        referencedColumnName: 'id'
    })
    parent!: SpilderTopicArticleComment;

    @Widget({
        title: '子评论'
    })
    @OneToMany(() => SpilderTopicArticleComment, it => it.pid, {
        createForeignKeyConstraints: false
    })
    comments!: SpilderTopicArticleComment[];

    @Widget({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    })
    @CreateDateColumn({
        name: 'create_date'
    })
    createDate!: Date;

    @Widget({
        title: '更新时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    })
    @UpdateDateColumn({
        name: 'update_date'
    })
    updateDate!: Date;

}
