import { Component, Widget } from "@nger/rest";
import { Column, CreateDateColumn, Entity, PrimaryColumn, PrimaryGeneratedColumn } from "typeorm";

@Component({
    title: '帖子截面'
})
@Entity({
    name: 'spilder_topic_article_tend'
})
export class SpilderTopicArticleTend {

    @Widget({
        title: '序号'
    })
    @PrimaryGeneratedColumn()
    id!: number;


    @Widget({
        title: '文章'
    })
    @Column()
    aid!: number;

    @Widget({
        title: '转发数'
    })
    @Column({
        type: 'bigint',
        nullable: true
    })
    reposts_count!: number;

    @Widget({
        title: '评论数'
    })
    @Column({
        type: 'bigint',
        nullable: true
    })
    comments_count!: number;

    @Widget({
        title: '点赞数'
    })
    @Column({
        type: 'bigint',
        nullable: true
    })
    attitudes_count!: number;

    @Widget({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    })
    @CreateDateColumn({
        name: 'create_date'
    })
    createDate!: Date;
}
