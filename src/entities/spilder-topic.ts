import { Component, Widget } from "@nger/rest";
import { CreateDateAst } from "@nger/rest/dist/relations/ast";
import { Column, CreateDateColumn, Entity, JoinColumn, ManyToOne, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { WbEvent } from "./event";
import { WbPlatform } from "./platform";
import { SpilderAccount } from "./spilder-account";
import { SpilderTopicArticle } from "./spilder-topic-article";
import { SpilderTopicArticleComment } from "./spilder-topic-article-comment";

@Component({
    title: '话题/问答'
})
@Entity({
    name: 'spilder_topic'
})
export class SpilderTopic {
    @Widget({
        title: '序号',
        hideInSearch: true
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '平台',
        path: 'platformEntity.title',
        widget: {
            type: 'selector',
            from: 'wb_platform',
            config: {
                label: 'title',
                value: 'name'
            }
        }
    })
    @Column({
        default: 'weibo'
    })
    platform!: string;

    @Widget({
        title: '平台'
    })
    @ManyToOne(() => WbPlatform, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'platform',
        referencedColumnName: 'name'
    })
    platformEntity!: WbPlatform;

    @Widget({
        title: '话题名'
    })
    @Column({
        nullable: false,
        default: ''
    })
    title!: string;

    @Widget({
        title: '创建用户',
        path: 'user.nickname',
        hideInSearch: true
    })
    @Column({
        nullable: true
    })
    uid!: string;

    @Widget({
        title: '创建用户'
    })
    @ManyToOne(() => SpilderAccount, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'uid',
        referencedColumnName: 'uid'
    })
    user!: SpilderAccount;

    @Widget({
        title: '所属事件',
        path: 'event.title',
        hideInSearch: true
    })
    @Column({
        nullable: true
    })
    eid!: number;

    @Widget({
        title: '事件'
    })
    @ManyToOne(() => WbEvent, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'eid',
        referencedColumnName: 'id'
    })
    event!: WbEvent;

    @Widget({
        title: '分类ID',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    cate_id!: string;

    @Widget({
        title: '分类名',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    category_str!: string;

    @Widget({
        title: '发布位置',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    location!: string;

    @Widget({
        title: '相关话题',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    related_topic!: string;

    @Widget({
        title: '导语',
        widget: {
            type: 'textarea',
        },
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    summary!: string;

    @Widget({
        title: '话题连接',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    target_url!: string;

    @Widget({
        title: '主题标签',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    topic_tag!: string;

    @Widget({
        title: '创建时间',
        hideInSearch: true
    })
    @Column({
        default: 0,
        type: 'bigint',
    })
    create_at!: number;

    @Widget({
        title: '主持人id',
        // path: 'claim.nickname',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        nullable: true
    })
    claim_id!: string;

    @Widget({
        title: '主持人'
    })
    @ManyToOne(() => SpilderAccount, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'claim_id',
        referencedColumnName: 'uid'
    })
    claim!: SpilderAccount;

    @Widget({
        title: '主持人昵称',
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    claim_name!: string;

    @Widget({
        title: '阅读次数',
        hideInSearch: true,
        sortable: true
    })
    @Column({
        type: 'bigint',
        nullable: true
    })
    read_count!: number;

    @Widget({
        title: '讨论次数',
        hideInSearch: true,
        sortable: true
    })
    @Column({
        type: 'bigint',
        nullable: true
    })
    mention_count!: number;

    @Widget({
        title: '原创人数',
        hideInSearch: true,
        sortable: true
    })
    @Column({
        type: 'bigint',
        nullable: true
    })
    ori_uv_Count!: number;

    @Widget({
        title: '帖子',
        widget: {
            type: 'array',
            where: { tid: 'id' }
        }
    })
    @OneToMany(() => SpilderTopicArticle, it => it.eid, {
        createForeignKeyConstraints: false
    })
    articles!: SpilderTopicArticle[];

    @Widget({
        title: '评论',
        widget: {
            type: 'array',
            where: { tid: 'id' }
        }
    })
    @OneToMany(() => SpilderTopicArticleComment, it => it.eid, {
        createForeignKeyConstraints: false
    })
    comments!: SpilderTopicArticleComment[];

    @Widget({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    })
    @CreateDateColumn({
        name: 'create_date'
    })
    createDate!: Date;

    @Widget({
        title: '更新时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    })
    @UpdateDateColumn({
        name: 'update_date'
    })
    updateDate!: Date;
}
