import { Injectable, Injector } from "@nger/core";
import { MqOptions, MqService } from "./mq.service";

@Injectable()
export class MqFactory {
    constructor(private injector: Injector) { }
    async create(options: MqOptions) {
        const mq = new MqService(this.injector, options)
        await mq.init()
        return mq;
    }
    sleep(time: number) {
        return new Promise((resolve) => setTimeout(resolve, time * 1000));
    }
}
