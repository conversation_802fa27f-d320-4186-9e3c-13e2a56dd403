import { Injector } from "@nger/core";
import { RabbbitmqUtil } from "./rabbitmq.util";
import { Observable, Subscriber } from 'rxjs'
import { ConsumeMessage, Channel, ConfirmChannel } from 'amqplib'
export interface MqOptions {
    exchange: string;
    queue: string;
    routingKey: string;
    deadExchange: string;
    deadQueue: string;
    deadRoutingKey: string;
    prefetch?: number;
}
export interface Ack {
    (): void;
}
export interface NoAck {
    (allUpTo?: boolean, requee?: boolean): void;
}
export interface Reject {
    (requee?: boolean): void;
}
export interface Action<T = any> {
    ack: Ack;
    noAck: NoAck;
    reject: Reject;
    channel: Channel;
    message: ConsumeMessage | null;
    data: Message<T> | null;
    reason?: string | null;
    from?: string;
}
export interface Message<T = any> {
    type: string;
    payload: T;
}

export class MqService {
    get util() {
        return this.injector.get(RabbbitmqUtil)
    }
    constructor(
        private injector: Injector,
        public options: MqOptions
    ) { }
    onError(e: Error) {
        console.log(`mq server`, e)
    }
    async get() {
        const connection = await this.util.init()
        const channel = await this.util.createConfirmChannel(connection)
        const msg = await channel.get(this.options.queue, { noAck: false })
        return msg;
    }
    async count() {
        const connection = await this.util.init()
        const channel = await this.util.createConfirmChannel(connection)
        const count = await channel.purgeQueue(this.options.queue).then(q => q.messageCount)
        return count;
    }
    async init() { }
    async publish(msg: Message, ttl: number = 1000 * 60 * 30) {
        return this.send(JSON.stringify(msg), ttl).catch(e => this.onError(e))
    }
    publishs(list: Message[], ttl: number = 1000 * 60 * 30) {
        return this.sends(list.filter(it => {
            return !!it
        }).map(it => {
            return {
                msg: JSON.stringify(it),
                ttl
            }
        })).catch(e => this.onError(e))
    }
    async watch(isDead: boolean = false) {
        let sub: Observable<Action>;
        if (isDead) {
            sub = await this.dead()
        } else {
            sub = await this.consumer()
        }
        return sub;
    }
    async sends(list: { msg: string, ttl: number }[]) {
        const connection = await this.util.init()
        const channel = await this.util.createConfirmChannel(connection)
        return Promise.all(list.map(it => {
            return new Promise((resolve, reject) => {
                this._send(channel, it.msg, it.ttl, (err: Error) => reject(err), () => resolve(true))
            })
        })).finally(() => {
            channel.close().then(() => {
                return connection.close().catch(e => { })
            }).catch(() => { })
        })
    }
    private async _send(channel: ConfirmChannel, msg: string, ttl: number, onError: Function, onSuccess: Function) {
        channel.on('error', (err) => {
            onError(err)
        })
        // 默认交换机-队列-路由
        channel.assertExchange(this.options.exchange, 'direct', {
            durable: true
        }).then(() => {
            channel.publish(this.options.exchange, this.options.routingKey, Buffer.from(msg), {
                expiration: ttl,
                persistent: true,
                mandatory: true
            }, (err, ok) => {
                if (err) {
                    onError(err)
                } else {
                    onSuccess(true)
                }
            });
        });
    }
    async send(msg: string, ttl: number) {
        const connection = await this.util.init()
        const channel = await this.util.createConfirmChannel(connection)
        const isSend = await new Promise((resolve, reject) => {
            this._send(channel, msg, ttl, (err: Error) => reject(err), () => resolve(true))
        }).catch((e) => {
            console.error('catch an error', e.message)
        })
        await channel.close().catch(e => {
            console.log('channel close error', e.message)
        })
        await connection.close().catch(e => { })
        return isSend
    }
    async consumer() {
        const connection = await this.util.init()
        const channel = await this.util.createChannel(connection)
        return new Observable<Action>(sub => {
            channel.on('error', (erro) => {
                sub.error(erro)
            })
            this._createConsumer(channel, sub)
            return () => {
                channel.close().catch(e => {
                    console.log({ err: e })
                }).finally(() => {
                    connection.close().catch(e => { })
                })
            }
        })
    }
    private async _createConsumer(channel: Channel, sub: Subscriber<Action>) {
        channel.on('error', (err) => {
            sub.error(err)
        })
        channel.prefetch(this.options.prefetch || 1)
        // 死信交换机-路由
        await channel.assertExchange(this.options.exchange, 'direct', { durable: true });
        const queueEX = await channel.assertQueue(this.options.queue, {
            exclusive: false,
            deadLetterExchange: this.options.deadExchange,
            deadLetterRoutingKey: this.options.deadRoutingKey,
        });
        await channel.bindQueue(this.options.queue, this.options.exchange, this.options.routingKey);
        await channel.consume(queueEX.queue, (msg) => {
            const ack = () => channel.ack(msg!)
            const nack = (allUpTo: boolean = true, requee: boolean = true) => channel.nack(msg!, allUpTo, requee)
            const reject = (requee: boolean = false) => channel.reject(msg!, requee)
            sub.next({ message: msg, ack: ack, noAck: nack, channel, data: this.getMsg(msg?.content), reject })
        }, { noAck: false });
    }
    async dead() {
        const connection = await this.util.init()
        const channel = await this.util.createChannel(connection)
        return new Observable<Action>((sub) => {
            this._createDead(channel, sub)
            return () => {
                channel.close().catch(e => { })
            }
        })
    }
    private async _createDead(channel: Channel, sub: Subscriber<Action>) {
        channel.on('error', (err) => {
            sub.error(err)
        })
        await channel.prefetch(this.options.prefetch || 1)
        // 死信交换机-路由
        // 默认交换机
        await channel.assertExchange(this.options.deadExchange, 'direct', { durable: true });
        // 队列，超时发动到死信队列
        const queueDLX = await channel.assertQueue(this.options.deadQueue, {
            exclusive: false
        })
        await channel.bindQueue(this.options.deadQueue, this.options.deadExchange, this.options.deadRoutingKey)
        await channel.consume(queueDLX.queue, msg => {
            const ack = (allUpTo: boolean = true) => channel.ack(msg!, allUpTo)
            const nack = (allUpTo: boolean = true, requee: boolean = true) => channel.nack(msg!, allUpTo, requee)
            const reject = (requee: boolean = false) => channel.reject(msg!, requee)
            sub.next({ reject, message: msg, ack: ack, noAck: nack, channel, data: this.getMsg(msg?.content) })
        }, { noAck: false });
    }
    sleep(time: number) {
        return new Promise((resolve) => setTimeout(resolve, time * 1000));
    }
    private getMsg(buf?: Buffer | null) {
        try {
            if (buf) {
                const str = buf.toString('utf-8')
                return JSON.parse(str)
            }
        } catch (e) {
            console.log(e)
        }
        return null
    }
}
