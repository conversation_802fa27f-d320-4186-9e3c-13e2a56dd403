import { Controller, Get, Injector } from "@nger/core";
import { Body, Post } from "@nger/http";
import { MqFactory } from "./mq.factory";


@Controller('@nger/mq')
export class RabbitmqController {
    get mqFactory() {
        return this.injector.get(MqFactory)
    }
    constructor(private injector: Injector) { }
    @Post('publish')
    async publish(@Body() body: any) {
        const mq = await this.mqFactory.create({
            exchange: 'task.exchange',
            deadExchange: 'dead.task.exchange',
            routingKey: 'task.routingKey',
            deadRoutingKey: 'dead.task.routingKey',
            queue: 'task.queue',
            deadQueue: 'dead.task.queue'
        })
        await mq.publish(body)
        return body;
    }

    @Post('publishs')
    async publishs(@Body() body: any) {
        const mq = await this.mqFactory.create({
            exchange: 'task.exchange',
            deadExchange: 'dead.task.exchange',
            routingKey: 'task.routingKey',
            deadRouting<PERSON>ey: 'dead.task.routingKey',
            queue: 'task.queue',
            deadQueue: 'dead.task.queue'
        })
        const result = await mq.publishs(body)
        return result;
    }

    @Get('count')
    async count() {
        const mq = await this.mqFactory.create({
            exchange: 'task.exchange',
            deadExchange: 'dead.task.exchange',
            routingKey: 'task.routingKey',
            deadRoutingKey: 'dead.task.routingKey',
            queue: 'task.queue',
            deadQueue: 'dead.task.queue'
        })
        const count = await mq.count()
        return count;
    }
}
