import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>leWidthProvider } from "@nger/core";
import { MqFactory } from "./mq.factory";
import { RabbitmqController } from "./rabbitmq.controller";
import { RabbbitmqUtil } from "./rabbitmq.util";

@Module({
    providers: [],
    imports: [],
    controllers: [
        RabbitmqController
    ]
})
export class RabbitmqModule {
    static forRoot(): ModuleWidthProvider<RabbitmqModule> {
        return {
            providers: [
                RabbbitmqUtil,
                MqFactory
            ],
            module: RabbitmqModule
        }
    }
}
