import { Injectable } from "@nger/core";
import { connect, Options, Connection } from 'amqplib'

@Injectable()
export class RabbbitmqUtil {
    async init(): Promise<Connection> {
        const connect = await this.createConnect()
        return connect;
    }
    async destory(connect: Connection): Promise<void> {
        await connect.close().catch(e=>{
            console.log('destory error', e.message)
        })
    }
    createConnect() {
        const option: Options.Connect = {
            hostname: process.env['MQ_HOST'] || '*************',
            port: parseInt(process.env['MQ_PORT'] || '15672'),
            username: process.env['MQ_USER'] || 'imeepos',
            password: process.env['MQ_PASS'] || '123qwe',
            protocol: 'amqp'
        }
        return connect(option) as any
    }
    createChannel(connect: Connection) {
        return connect.createChannel()
    }
    createConfirmChannel(connect: Connection) {
        return connect.createConfirmChannel()
    }
    sleep(time: number) {
        return new Promise((resolve) => setTimeout(resolve, time * 1000));
    }
}
