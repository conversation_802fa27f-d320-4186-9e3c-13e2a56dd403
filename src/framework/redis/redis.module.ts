import { Module } from '@nger/core'
import { RedisFactory } from './redis.factory';
import { REDIS, RedisUtil } from './redis.util';
import { RedisClientType } from 'redis'

@Module({
    imports: [],
    providers: [
        RedisFactory,
        RedisUtil,
        {
            provide: REDIS, 
            useFactory: (factory: RedisFactory) => {
                return factory.create()
            }, 
            deps: [RedisFactory]
        }
    ]
})
export class RedisModule { }