import { Injectable, InjectionToken } from "@nger/core";
import { RedisClientOptions, RedisClientType } from "redis";

@Injectable()
export class RedisUtil{
    async getRedisConfig(): Promise<RedisClientOptions>{
        return {
            url: process.env['REDIS_URL'] || 'redis://192.168.2.225:6379',
            socket: {
                keepAlive: 1000 * 3
            },
            database: 0
        }
    }
}
export const REDIS = new InjectionToken<Promise<RedisClientType>>(`RedisClientType`)