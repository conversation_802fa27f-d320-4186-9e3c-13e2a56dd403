import { Page, HTTPResponse, HTTPRequest } from "@nger/puppeteer";
import { Observable, Subscriber } from "rxjs";
import { extname } from 'path'
import { URL } from 'url'
import { createHash } from 'crypto'
import { Injector } from "@nger/core";
import { PUPPETEER_POOL } from '../../tokens'
export interface PageHandle<T> {
    (page: Page | HTTPResponse | HTTPRequest, sub: Subscriber<T>): Promise<void>;
}
export class BrowserRequest {
    type: 'request' = 'request'
    headers!: object;
    url!: string;
    cookies!: any[];
    constructor(url: string, cookies: any[] = [], headers?: object) {
        this.url = url;
        if (headers) this.headers = headers
        this.cookies = cookies;
    }
    static is(val: any): val is Request {
        return val && val.type === 'request'
    }
}
export abstract class BrowserSpider {
    constructor(public injector: Injector) { }
    md5(text: string) {
        return createHash('md5').update(text).digest('base64')
    }
    isPage(val: any): val is Page {
        if (!val) return false;
        return !this.isHttpResponse(val) && !this.isHttpRequest(val)
    }
    isHttpResponse(val: any): val is HTTPResponse {
        if (!val) return false
        return Reflect.has(val, 'request')
    }
    isHttpRequest(val: any): val is HTTPRequest {
        if (!val) return false
        return Reflect.has(val, 'respond')
    }
    openInBrowser<T>(req: BrowserRequest, handle: PageHandle<T>, options: 'networkidle0' | 'domcontentloaded' = 'networkidle0', finish?: (sub: Subscriber<T>) => void, fail?: (page: Page, error: Error)=>void): Observable<T> {
        return new Observable((sub) => {
            this._createPage(req).then(page => {
                page.on('close', () => sub.complete())
                page.on('error', err => sub.error(err))
                this._openInBrowser<T>(page, req, handle, sub, options, finish, fail).catch(e => sub.error(e))
                return page;
            })
        })
    }
    get puppeteerPool(){
        return this.injector.get(PUPPETEER_POOL)
    }
    private async _createPage(req: BrowserRequest) {
        const browser = await this.puppeteerPool.acquire();
        this.puppeteerPool.release(browser);
        // const ctx = browser.defaultBrowserContext();
        // await ctx.overridePermissions(req.url, ['geolocation'])
        const page = await browser.newPage();
        await page.setCookie(...req.cookies);
        await page.setJavaScriptEnabled(true);
        await page.setRequestInterception(true);
        // await page.setUserAgent(
        //     "Mozilla/5.0 (Windows NT 6.0) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.36 Safari/536.5")
        page.removeAllListeners()
        await page.setViewport({
            width: 1325,
            height: 768
        });
        return page;
    }
    // 浏览器打开某链接
    private async _openInBrowser<T>(page: Page, req: BrowserRequest, handle: PageHandle<T>, sub: Subscriber<any>, options: any, finish: any, fail: any) {
        const ignoreExtnames: string[] = [
            '.ico', '.png', '.jpeg', '.jpg', '.css', '.svg', '.bmp', '.gif', '.ttf', '.eot', '.woff', '.woff2',
            '.mp3', '.mp4'
        ]
        const waitRun: any[] = []
        page.on('request', req => {
            const url = new URL(req.url())
            const path = url.pathname || '';
            const ext = extname(path).toLowerCase()
            if (ignoreExtnames.includes(ext)) {
                req.respond({
                    status: 200,
                    contentType: 'text/plain',
                    body: 'not found'
                })
            } else {
                req.continue()
            }
        })
        page.on('response', function (res) {
            const url = new URL(res.url())
            const path = url.pathname || '';
            const ext = extname(path).toLowerCase()
            if (ignoreExtnames.includes(ext)) {
                return;
            }
            if (ext === '.js') {
                return;
            }
            if (res.request().method().toUpperCase() === 'OPTIONS') {
                return;
            }
            waitRun.push(handle(res, sub))
        });
        await page.goto(req.url, {
            waitUntil: [options],
        }).catch(async e => {
            console.error(e.message)
            if(fail) await fail(page, e)
        })
        await Promise.all(waitRun);
        if (handle) {
            await handle(page, sub)
        } else {
            await this.delay(200)
        }
        if (finish) {
            await finish(sub)
        }
        await page.close().catch(e => { })
    }

    delay(ms: number) {
        return new Promise<void>((resolve, reject) => {
            setTimeout(() => {
                resolve()
            }, ms)
        })
    }
}