import { Injector } from "@nger/core";
import { Message } from "../rabbitmq";
import { <PERSON><PERSON>erSpider } from "./browser-spider";
import { load } from 'cheerio'
import { RedisFactory } from '../redis'
import { Db } from "@nger/typeorm";
import { Observable } from "rxjs";
import { RedisClientType } from "redis";
import { WEIBO_USER_POOL, ZHIHU_USER_POOL } from "../../tokens";
export interface Item<T> {
    platform: string;
    url: string;
    data: T;
    create_at: Date;
}
export abstract class CoreSpider<Payload = any> extends BrowserSpider {
    type!: string;
    platform!: string;
    bf!: string;
    v?: number;
    total?: number;
    get redisFactory() {
        return this.injector.get(RedisFactory)
    }
    _redis?: Promise<RedisClientType>;
    get redis(): Promise<RedisClientType> {
        if (this._redis) return this._redis;
        this._redis = this.redisFactory.create() as any;
        return this._redis!;
    }
    get db() {
        return this.injector.get(Db)
    }
    constructor(injector: Injector) {
        super(injector)
        this.init()
    }
    load(html: string) {
        return load(html)
    }
    get weiboUserPool() {
        return this.injector.get(WEIBO_USER_POOL)
    }
    get zhihuUserPool(){
        return this.injector.get(ZHIHU_USER_POOL)
    }
    async init(): Promise<void> {
        try {
            if (this.v && this.total && this.bf) {
                const client = await this.redis;
                await client.bf.reserve(this.bf, this.v, this.total);
            }
        } catch (e: any) {
            if (e.message.endsWith('item exists')) {
                // exists
            } else {
                console.log(e);
            }
        }
    }
    abstract run<T = any>(msg: Message<Payload>): Promise<Observable<T> | void>;
    abstract dead<T = any>(msg: Message<Payload>): Promise<Observable<T> | void>;
    abstract createKey(url: string): Promise<string> | string;
    async exists(key: string) {
        const client = await this.redis
        return await client.bf.exists(this.bf, key)
    }
    async save(key: string) {
        const client = await this.redis
        return await client.bf.add(this.bf, key)
    }
    createItem<T>(url: string, data: T, type: string): Message<Item<T>> {
        const item = {
            platform: this.platform,
            data,
            url,
            create_at: new Date()
        } as Item<T>
        return {
            type,
            payload: item
        } as Message<Item<T>>
    }
}

