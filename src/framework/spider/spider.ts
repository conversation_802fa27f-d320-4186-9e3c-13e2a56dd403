import { Detail, DetailLink } from "../tasks/types";
import { DetailTask } from "../tasks/detail.task";
import { Core<PERSON>pider } from "./core";
import { CommentTask } from "../tasks/comment.task";
import { LikeTask } from "../tasks/like.task";
import { RepostTimelineTask } from "../tasks/repostTimeline.task";
export abstract class Spider<Payload = any> extends CoreSpider<Payload> {
    getUser(){
        return this.zhihuUserPool.acquire()
    }
    retry(fn: Function, msg: string, times: number = 4, delay: number = Math.random() * 1000 * 30) {
        return new Promise(function(resolve, reject) {
            const tFn = function () {
                fn().then(resolve).catch((e: any) => {
                    if (times-- > 0) {
                        console.log(`${msg}--还有${times}次机会: ${e.message}`)
                        setTimeout(tFn, delay)
                    } else {
                        reject(e)
                    }
                })
            }
            return tFn()
        })
    }
    async publishDetailLinks(urls: DetailLink[]) {
        const detail = this.injector.get(DetailTask)
        const tasks = urls.map(payload => {
            return {
                type: this.platform,
                payload
            }
        })
        await detail.publishs(tasks)
        return tasks;
    }
    async publishDetail(url: Detail) {
        if (!Detail.isDetail(url)) {
            return;
        }
        const detail = this.injector.get(DetailTask)
        await detail.publish({
            type: this.platform,
            payload: {
                ...url
            }
        })
        return { url }
    }

    async publishGetComment(id: number, aid: string, platform: string){
        const commentTask = this.injector.get(CommentTask)
        await commentTask.publish({
            type: 'comment',
            payload: {
                id, aid, platform
            }
        })
    }

    async publishGetLike(id: number, aid: string, platform: string){
        const likeTask = this.injector.get(LikeTask)
        await likeTask.publish({
            type: 'like',
            payload: {
                id, aid, platform
            }
        })
    }

    async publishGetRepostTimeline(id: number, aid: string, platform: string){
        const likeTask = this.injector.get(RepostTimelineTask)
        await likeTask.publish({
            type: 'report-timeline',
            payload: {
                id, aid, platform
            }
        })
    }

}

