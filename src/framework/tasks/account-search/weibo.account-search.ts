import { Injectable } from "@nger/core";
import { load, text } from "cheerio";
import { Observable } from "rxjs";
import { Message } from "../../rabbitmq";
import { BrowserRequest, Spider } from "../../spider";

@Injectable()
export class <PERSON><PERSON>AccountSearch extends Spider {
    async dead<T = any>(msg: Message<any>): Promise<void | Observable<T>> {
       
    }
    platform: string = 'weibo';
    type: string = 'account';
    async run<T = any>(action: Message<any>): Promise<void | Observable<T>> {
        console.log(action)
        const { payload } = action;
        const keyword = encodeURIComponent(payload.keyword)
        const url = `https://s.weibo.com/user?q=${keyword}&Refer=weibo_user`;
        const user = await this.weiboUserPool.acquire()
        if (user) {
            const headers = {};
            const cookies: any[] = user.cookies;
            this.weiboUserPool.release(user);
            const req = new BrowserRequest(url, cookies, headers)
            return this.openInBrowser<T>(req, async (page, sub) => {
                if (this.isPage(page)) {
                    const content = await page.content();
                    const $ = this.load(content);
                    const accounts = $('#pl_user_feedList .card-user-b')
                    accounts.map((index, element)=>{
                        const $ = load(element)
                        const avatarElement = $('div.avator > a > img')
                        const avatar = avatarElement[0].attribs['src']
                        const nameElement = $('div.info > div > a')
                        const name = text(nameElement).trim()
                        const link = nameElement[0].attribs['href']
                        const descElement = $('div.info > p:nth-child(2)')
                        const desc = text(descElement).trim()
                        const fansElement = $('div.info > p:nth-child(3) > span')
                        const fans = text(fansElement).trim()
                        const data: any = {
                            avatar,
                            name,
                            link,
                            desc,
                            fans
                        }
                        sub.next(data);
                    })
                } else if (this.isHttpRequest(page)) {
                    page.continue()
                } else { }
            })
        }
        console.log(user)
    }
    createKey(url: string): string | Promise<string> {
        throw new Error("Method not implemented.");
    }
}
