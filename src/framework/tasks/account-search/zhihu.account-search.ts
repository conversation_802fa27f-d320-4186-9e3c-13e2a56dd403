import { Injectable } from "@nger/core";
import axios from "axios";
import { Observable } from "rxjs";
import { Message } from "../../rabbitmq";
import { BrowserRequest, Spider } from "../../spider";

@Injectable()
export class <PERSON><PERSON><PERSON>AccountSearch extends Spider {
    async dead<T = any>(msg: Message<any>): Promise<void | Observable<T>> {
    }
    platform: string = 'weibo';
    type: string = 'account';
    async run<T = any>(action: Message<any>): Promise<void | Observable<T>> {
        console.log(action)
        const { payload } = action;
        const keyword = encodeURIComponent(payload.keyword)
        const url = `https://www.zhihu.com/search?type=people&q=${keyword}`;
        const user = await this.zhihuUserPool.acquire();
        this.zhihuUserPool.release(user)
        if (user) {
            const headers = {};
            const cookies: any[] = user.cookies;
            const req = new BrowserRequest(url, cookies, headers)
            return this.openInBrowser(req, async (page, sub) => {
                if (this.isPage(page)) {
                    // await this.delay(1000 * 8)
                } else if (this.isHttpRequest(page)) {
                    const url = page.url()
                    if (url.startsWith('https://www.zhihu.com/api/v4/search_v3')) {
                        const headers = page.headers()
                        await axios.get(url, { headers }).then(res => {
                            const { data } = res.data;
                            data.map((item: any) => {
                                const { object } = item;
                                const avatar = object.avatar_url;
                                const name = (object.name as string).replaceAll('<em>', '').replaceAll('</em>', '');
                                const desc = object.headline
                                const link = object.url;
                                const fans = `${object.articles_count}文章,${object.answer_count}回答,${object.follower_count}关注者`
                                const user: any = {
                                    avatar,
                                    name,
                                    link,
                                    desc,
                                    fans
                                }
                                sub.next(user)
                            })
                            page.respond({
                                status: res.status,
                                headers: res.headers,
                                contentType: 'application/json',
                                body: JSON.stringify(res.data)
                            })
                        }).catch(e => {
                            console.log(e.message)
                        })
                    }
                    else {
                        page.continue()
                    }
                } else { }
            })
        }
    }
    createKey(url: string): string | Promise<string> {
        throw new Error("Method not implemented.");
    }
}
