import { Injector, StaticProvider } from "@nger/core";
import { <PERSON><PERSON><PERSON><PERSON> as <PERSON> } from "../../spider";
import { WeiboAccountSpider } from "./weibo.account";

export const accountProvicers: StaticProvider<any>[] = [
    {
        provide: Spider,
        useFactory: (injector: Injector) => {
            return new WeiboAccountSpider(injector)
        },
        deps: [Injector],
        multi: true
    }
]
