import axios from "axios";
import { Page } from "@nger/puppeteer";
import { Observable, Subscriber, bufferTime, mergeMap, of, from } from "rxjs";
import { Message } from "../../rabbitmq";
import { BrowserRequest, Spider } from "../../spider";
import { ARTICLE_DETAIL_KEY, ARTICLE_DETAIL_TOTAL, ARTICLE_DETAIL_V } from "../const";
import { DetailLink } from "../types";
import { AccountTask } from "../account.task";
import { SpiderUser } from "../../../entities/spider_user";

export class <PERSON><PERSON><PERSON><PERSON>unt<PERSON>pider extends Spider {
    async dead<T = any>(msg: Message<any>): Promise<void | Observable<any>> {
        const task = this.injector.get(AccountTask)
        return from(task.publish(msg))
    }
    bf: string = ARTICLE_DETAIL_KEY
    v: number = ARTICLE_DETAIL_V
    total: number = ARTICLE_DETAIL_TOTAL;
    platform: string = 'weibo';
    type: string = 'account';
    async scrollDown(page: Page) {
        await page.evaluate(() => {
            return new Promise<void>((resolve, reject) => {
                let totalHeight = 0;
                const distance: number = 100
                const timer = setInterval(() => {
                    const scrollHeight = document.body.scrollHeight;
                    window.scrollBy(0, distance)
                    totalHeight += distance;
                    console.log(totalHeight)
                    if (totalHeight >= scrollHeight) {
                        clearInterval(timer);
                        resolve()
                    }
                }, 50)
            })
        })
    }
    async getUser(): Promise<SpiderUser> {
        let user = await this.weiboUserPool.acquire().catch(e=>{
            return undefined;
        })
        while (!user) {
            await this.delay(10000 * 10 * Math.random())
            user = await this.weiboUserPool.acquire().catch(e=>{
                return undefined;
            });
        }
        this.weiboUserPool.release(user)
        return user;
    }
    async run(action: Message<any>): Promise<void | Observable<any>> {
        const { payload, type } = action;
        const keyword = payload.keyword
        let url = keyword;
        if (url.startsWith('/')) {
            url = `https:${url}`
        }
        let user = await this.getUser();
        if (user) {
            this.weiboUserPool.release(user)
            const headers = {};
            const cookies: any[] = user.cookies
            const req = new BrowserRequest(url, cookies, headers)
            return this.openInBrowser(req, async (page, sub) => {
                if (this.isPage(page)) {
                    const btn = await page.waitForSelector('#app div.woo-pop-wrap > span > button').catch(e => { })
                    if (btn) {
                        await btn.click()
                        await this.delay(200)
                    }
                } else if (this.isHttpRequest(page)) {
                    const url = page.url();
                    const headers = page.headers()
                    if (url.startsWith('https://weibo.com/ajax/statuses/mymblog')) {
                        await axios.get(url, { headers }).then(async res => {
                            const a = new URL(url)
                            const { data, ok } = res.data;
                            if (ok === 1 && data) {
                                const { list, since_id, total } = data;
                                const uid = a.searchParams.get('uid')!
                                const p = a.searchParams.get('page')!
                                const feature = a.searchParams.get('feature')!
                                list.map((item: any) => {
                                    const { mblogid } = item;
                                    const link = `https://weibo.com/${uid}/${mblogid}`
                                    const l = new DetailLink()
                                    l.href = link;
                                    l.platform = this.platform;
                                    sub.next(l)
                                });
                                await page.respond({
                                    status: res.status,
                                    headers: res.headers,
                                    contentType: 'application/json',
                                    body: JSON.stringify(res.data)
                                })
                                if (since_id) {
                                    await this.getMore(since_id, uid, parseInt(p) + 1, feature, headers, sub)
                                }
                            }
                        })
                        await this.delay(1000)
                    } else {
                        await page.continue()
                    }
                } else { }
            }).pipe(
                bufferTime(1000),
                mergeMap((list: any[]) => {
                    return from(this.publishDetailLinks(list).catch(e => console.log(e)))
                }),
            )
        }
    }
    async getMore(since_id: string, uid: string, p: number, feature: string, headers: any, sub: Subscriber<any>): Promise<void> {
        await this.delay(Math.random() * 500)
        const url = `https://weibo.com/ajax/statuses/mymblog?uid=${uid}&page=${p}&feature=${feature}&since_id=${since_id}`
        return axios.get(url, { headers }).then(res => {
            const { data, ok } = res.data;
            if (ok === 1) {
                const { list, since_id, total } = data;
                list.map((item: any) => {
                    const { mblogid } = item;
                    const link = `https://weibo.com/${uid}/${mblogid}`
                    const l = new DetailLink()
                    l.href = link;
                    l.platform = this.platform;
                    sub.next(l)
                })
                if (since_id && since_id.length > 0) {
                    return this.getMore(since_id, uid, p + 1, feature, headers, sub)
                }
                console.log({ data: res.data })
            } else {
                console.log('load error', res.data)
            }
        }).catch(e => {
            return this.getMore(since_id, uid, p, feature, headers, sub)
        })
    }
    createKey(url: string): string {
        return this.md5(url)
    }
}