import { Injector, St<PERSON>Provider } from "@nger/core";
import { <PERSON><PERSON><PERSON><PERSON> as <PERSON> } from "../../spider";
import { WeiboCommentSpider } from "./weibo.comment";

export const commentProvicers: StaticProvider<any>[] = [
    {
        provide: Spider,
        useFactory: (injector: Injector) => {
            return new WeiboCommentSpider(injector)
        },
        deps: [Injector],
        multi: true
    }
]
