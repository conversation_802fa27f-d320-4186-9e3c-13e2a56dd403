import { Db } from "@nger/typeorm";
import axios from "axios";
import { from, Observable } from "rxjs";
import { News } from "../../../entities/news";
import { SpiderUser } from "../../../entities/spider_user";
import { Message } from "../../rabbitmq";
import { Spider } from "../../spider";
import { CommentTask } from "../comment.task";
import { Error400, Error414, UserLogoutError } from "../error";
import { NlpService } from "../nlp.service";
import { makeRequestWithProxy } from "../httpRequest";

export class WeiboCommentSpider extends Spider {
    platform: string = 'weibo';
    type: string = 'comment';
    async getUser(): Promise<SpiderUser> {
        let user = await this.weiboUserPool.acquire().catch(e => {
            return undefined;
        })
        while (!user) {
            await this.delay(10000 * 10 * Math.random())
            user = await this.weiboUserPool.acquire().catch(e => {
                return undefined;
            });
        }
        this.weiboUserPool.release(user)
        return user;
    }
    async run<T = any>(msg: Message<any>): Promise<void | Observable<T>> {
        const { payload } = msg;
        const { id, aid, uid } = payload;
        let user = await this.getUser();
        if (user) {
            const db = this.injector.get(Db)
            const article: any = await db.manager.findOne(News, { select: ['uid', 'id', 'comment'], where: { id } })
            const psize = 200;
            if (article) {
                article.comment = article.comment || [];
                await this.getComponents(aid, article.uid, 0, psize, article);
                if (article.comment.length > 0) {
                    const comments = article.comment.map((it: any) => {
                        if (it.children && it.children.length > 0) {
                            const children = it.children.map((it: any) => it.text_raw);
                            return [it.text_raw, ...children]
                        }
                        return [it.text_raw];
                    }).flat().filter((it: string) => it && it.length > 0)
                    if (comments.length > 0) {
                        // const nlpService = this.injector.get(NlpService)
                        // const cuts = await nlpService.cut(comments);
                        // const sentas = await nlpService.senta(comments)
                        // if (cuts && cuts.length > 0) {
                        //     article.comment_cut = cuts;
                        //     let total_comment_word_num = 0
                        //     let total_comment_readablity = 0
                        //     cuts.map((cut: any) => {
                        //         const [cut_res, word_num, readablity] = cut;
                        //         total_comment_word_num += word_num
                        //         total_comment_readablity += readablity
                        //     });
                        //     article.comment_readablity_avg = Math.round(100 * total_comment_readablity / cuts.length);
                        //     article.comment_word_num_avg = Math.round(100 * total_comment_word_num / cuts.length);
                        // }

                        // if (sentas && sentas.length > 0) {
                        //     article.comment_emotion = sentas;
                        //     let comment_positive_total = 0;
                        //     let comment_negative_total = 0;
                        //     let comment_negative_count = 0;
                        //     let comment_positive_count = 0;
                        //     sentas.map((emotion: any) => {
                        //         const { negative_probs, positive_probs, sentiment_key, sentiment_label } = emotion;
                        //         comment_positive_total += positive_probs;
                        //         comment_negative_total += negative_probs
                        //         if (sentiment_label == 1) {
                        //             comment_positive_count += 1
                        //         } else {
                        //             comment_negative_count += 1
                        //         }
                        //     });
                        //     article.comment_positive_avg = Math.round(comment_positive_total * 100 / sentas.length)
                        //     article.comment_negative_avg = Math.round(comment_negative_total * 100 / sentas.length)
                        //     article.comment_negative_count = comment_negative_count
                        //     article.comment_positive_count = comment_positive_count
                        // }
                    }
                    await this.updateDb(article);
                }
            }
        }
    }
    async updateDb(article: News) {
        const db = this.injector.get(Db)
        const { aid, id, ...data } = article;
        return await db.manager.update(News, article.id, { ...data }).catch(e => {
            const msg = e.message as string;
            if (msg.includes('duplicate key value violates unique constraint')) {
                return;
            }
            throw e;
        });
    }
    async dead(msg: Message<any>): Promise<void | Observable<any>> {
        const task = this.injector.get(CommentTask)
        return from(task.publish(msg))
    }
    createKey(url: string): string | Promise<string> {
        throw new Error("Method not implemented.");
    }
    private async getHeaders() {
        const user = await this.getUser()
        if (user) {
            const cookies: any[] = user.cookies;
            const headers = {
                cookie: cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; '),
                ['user-agent']: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36'
            };
            return { headers, user };
        }
        throw new UserLogoutError()
    }
    private async getComponents(id: number, uid: string, max_id: number, psize: number, news: News): Promise<any> {
        const url = `https://weibo.com/ajax/statuses/buildComments`
        const query: any = {};
        query.is_reload = 1;
        query.id = id;
        query.count = psize;
        query.uid = uid;
        query.is_show_bulletin = 2;
        if (max_id) {
            query.flow = 0;
            query.is_mix = 0;
            query.max_id = max_id
        } else {
            query.is_mix = 0;
        }
        const _url = this.createUrl(url, query)
        const headers = await this.getHeaders();
        await makeRequestWithProxy({
            method: "get",
            url: _url,
            headers: headers.headers
        }).then(async (res: any) => {
            const { data, max_id, ok, trendsText } = res.data;
            if (trendsText) {
                console.log(trendsText)
            }
            if (ok == 1 && data.length > 0) {
                const components = await Promise.all(data.map(async (item: any) => {
                    if (item.comments.length > 0) {
                        await this.getChildComponents(item, uid, headers.headers, 0)
                    }
                    return item;
                }))
                news.comment.push(...components)
                if (max_id) {
                    await this.getComponents(id, uid, max_id, psize, news)
                }
            }
        }).catch(async e => {

            const message = e.message as string;
            const is414 = message.includes('Request failed with status code 414');
            if (is414) {
                throw new Error414()
            }
            const is400 = message.includes('Request failed with status code 400')
            if (is400) {
                return;
            }
            throw e;
        });
        this.weiboUserPool.release(headers.user)
    }
    private createUrl(url: string, query: any) {
        const str = Object.keys(query).map(key => {
            return `${key}=${query[key]}`
        }).join('&')
        return `${url}?${str}`
    }
    private getChildComponents(item: any, uid: string, headers: any, max_id: number = 0): Promise<any> {
        const url = `https://weibo.com/ajax/statuses/buildComments`
        const query: any = {}
        query.is_reload = 1;
        query.id = item.id
        query.is_show_bulletin = 2;
        query.is_mix = 1;
        query.fetch_level = 1;
        query.count = 100;
        query.uid = uid;
        if (max_id) {
            query.flow = 0;
            query.max_id = max_id;
        } else {
            query.max_id = 0
        }
        const _url = this.createUrl(url, query)

        return this.retry(async () => {
            return await makeRequestWithProxy<any>({
                method: "get",
                url: _url,
                headers: headers
            }).then(async res => {
                const { data, ok, max_id } = res.data;
                if (ok === 1 && data.length > 0) {
                    item.children = item.children || [];
                    item.children.push(...data);
                    if (max_id && data.length > 0) {
                        await this.delay(1000 * 10 * Math.random())
                        return this.getChildComponents(item, uid, headers, max_id)
                    }
                }
            }).catch((e) => {
                const message = e.message as string;
                const is414 = message.includes('Request failed with status code 414');
                if (is414) {
                    throw new Error414();
                }
                const is400 = message.includes('Request failed with status code 400')
                if (is400) {
                    return;
                }
                throw e;
            })
        }, '获取子评论-getChildComponents')
    }
}