import { Injector, StaticProvider } from "@nger/core";
import { <PERSON><PERSON><PERSON><PERSON> as <PERSON> } from "../../spider";
import { WeiboDetailSpider } from "./weibo.detail";

export const detailProvicers: StaticProvider<any>[] = [
    {
        provide: Spider,
        useFactory: (injector: Injector) => {
            return new WeiboDetailSpider(injector)
        },
        deps: [Injector],
        multi: true
    }
]
