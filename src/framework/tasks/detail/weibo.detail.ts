import { APP_ROOT } from "@nger/core";
import axios, { AxiosError } from "axios";
import { text } from "cheerio";
import { createWriteStream } from "fs";
import { join } from "path";
import { Page } from "@nger/puppeteer";
import { from, Observable, Subscriber } from "rxjs";
import { News } from "../../../entities/news";
import { Message } from "../../rabbitmq";
import { Spider } from "../../spider";
import { ensureDirSync } from 'fs-extra'
import { DetailTask } from "../detail.task";
import { Db } from "@nger/typeorm";
import { Error400, Error414, UserLogoutError } from "../error";
// import { NlpService } from "../nlp.service";
import { SpiderUser } from "../../../entities/spider_user";
import { SystemMessage } from "../../../v2/message";
import { makeRequestWithProxy } from "../httpRequest";
export class WeiboDetailSpider extends Spider {
    platform: string = 'weibo';
    type: string = 'detail';
    async scrollDown(page: Page) {
        await page.evaluate(() => {
            return new Promise<void>((resolve, reject) => {
                let totalHeight = 0;
                const distance: number = 100
                const timer = setInterval(() => {
                    const scrollHeight = document.body.scrollHeight;
                    window.scrollBy(0, distance)
                    totalHeight += distance;
                    if (totalHeight >= scrollHeight) {
                        clearInterval(timer);
                        resolve()
                    }
                }, 0)
            })
        })
    }
    async getComment(page: Page) {
        let isEnd = false;
        if (!isEnd) {
            const content = await page.content();
            const $ = this.load(content);
            const more = $('.Bottom_text_1kFLe')
            const moreTip = text(more).trim()
            if (moreTip.includes('没有更多内容了')) {
                isEnd = true;
            }
            await this.scrollDown(page);
        }
    }
    async getArticleDetail() {

    }
    async dead(msg: Message<any>): Promise<void | Observable<any>> {
        const task = this.injector.get(DetailTask)
        return from(task.publish(msg))
    }
    async getUser(): Promise<SpiderUser> {
        let user = await this.weiboUserPool.acquire().catch(e => {
            return undefined;
        })
        while (!user) {
            await this.delay(10000 * 10 * Math.random())
            user = await this.weiboUserPool.acquire().catch(e => {
                return undefined;
            });
        }
        this.weiboUserPool.release(user)
        return user;
    }
    private async getHeaders() {
        let user = await this.getUser();
        if (user) {
            const cookies: any[] = user.cookies;
            const headers = {
                cookie: cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; '),
                ['user-agent']: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36'
            };
            return { headers, user };
        }
        throw new UserLogoutError()
    }
    get systemMessage() {
        return this.injector.get(SystemMessage)
    }
    async run(msg: Message<any>): Promise<void | Observable<any>> {
        const { payload } = msg;
        if(!payload.keyword){
            console.error(`关键字缺失，任务异常`)
        }
        console.log(`执行抓取详情任务: ${payload.keyword}`)
        const link = payload.href;
        console.log(`detail url: ${link}`)
        let user = await this.getHeaders();
        if (user) {
            const news = new News();
            news.type = 'article';
            news.comment = [];
            news.like = [];
            news.share = [];
            if (!link) {
                return;
            }
            const _url = new URL(link);
            const [, uid, mid] = _url.pathname.split('/');
            if (!mid) {
                throw new Error('文章ID无效: ' + link)
            }
            // const redis = await this.redis;
            // const isExisit = await redis.exists(`${this.platform}.${mid}`)
            // if (isExisit) {
            //     const dataStr = JSON.stringify(payload);
            //     this.systemMessage.sendText(`detail`, {
            //         title: '文章已存在',
            //         content: dataStr
            //     }).subscribe();
            //     return;
            // }
            return new Observable<any>((sub: Subscriber<any>) => {
                const url = `https://weibo.com/ajax/statuses/show?id=${mid}&locale=zh-CN&isGetLongText=true`;
                console.log(`axios代理请求：${url}`)
                makeRequestWithProxy<any>({
                    method: 'get',
                    url: url,
                    headers: user.headers
                }).then(async res => {
                    console.log(`请求完成：${url}`)
                    if (res.status === 400) {
                        sub.error(new Error400());
                        return;
                    }
                    if (res.status === 414) {
                        sub.error(new Error414())
                        return;
                    }
                    const { user: account, ok, error_code, message, msg, ...content } = res.data;
                    if (ok == 1) {
                        news.platform = this.platform;
                        news.from = url;
                        content.id = `${content.id}`
                        news.data = content;
                        news.user = account;
                        news.aid = content.id
                        news.uid = account.id;
                        news.post_at = new Date(content.created_at);
                        news.readablity = 0;
                        news.word_num = 0;
                        news.cut = {};
                        news.keywords = payload.keyword || '';
                        news.negative_probs = 0;
                        news.positive_probs = 1;
                        news.sentiment_key = 'positive';
                        news.sentiment_label = 1;
                        // 分享情感
                        news.share_cut = [];
                        news.share_emotion = [];
                        news.share_readablity_avg = 0
                        news.share_word_num_avg = 0

                        news.share_positive_avg = 0
                        news.share_negative_avg = 0
                        news.share_negative_count = 0;
                        news.share_positive_count = 0;

                        news.comment_cut = [];
                        news.comment_emotion = [];
                        news.comment_readablity_avg = 0
                        news.comment_positive_avg = 0
                        news.comment_negative_avg = 0
                        news.comment_negative_count = 0;
                        news.comment_positive_count = 0;
                        news.comment_word_num_avg = 0;
                        if (content.text_raw) {
                            // 暂时去掉 nlp
                            await new Promise((resolve)=>setTimeout(resolve, 1000 * Math.random()))
                            // const nlpService = this.injector.get(NlpService)
                            // const cuts = await nlpService.cut([content.text_raw]);
                            // const sentas = await nlpService.senta([content.text_raw])
                            // if (cuts && cuts.length > 0) {
                            //     const cut = cuts[0]
                            //     const [cut_res, word_num, readablity] = cut;
                            //     news.readablity = Math.round(100 * readablity);
                            //     news.word_num = word_num;
                            //     news.cut = cut_res;
                            // }
                            // if (sentas && sentas.length > 0) {
                            //     const emotion = sentas[0]
                            //     const { negative_probs, positive_probs, sentiment_key, sentiment_label } = emotion;
                            //     news.negative_probs = Math.round(100 * negative_probs);
                            //     news.positive_probs = Math.round(100 * positive_probs);
                            //     news.sentiment_key = sentiment_key;
                            //     news.sentiment_label = sentiment_label;
                            // }
                        }
                        const saveNews = await this.saveDb(news) as News;
                        // await redis.set(`${this.platform}.${mid}`, 1)
                        // const dataStr = JSON.stringify(payload);
                        // this.systemMessage.sendText(`detail`, {
                        //     title: '爬取成功',
                        //     content: dataStr
                        // }).subscribe();
                        if (saveNews) {
                            console.log(`${link}保存成功`)
                            await this.publishGetLike(saveNews.id, saveNews.aid, this.platform);
                            await this.publishGetRepostTimeline(saveNews.id, saveNews.aid, this.platform);
                            await this.publishGetComment(saveNews.id, saveNews.aid, this.platform);
                        } else {
                            console.log(`platform: ${news.platform}, aid : ${news.aid} , ${link}保存失败`)
                        }
                        this.weiboUserPool.release(user.user)
                        sub.complete()
                    } else {
                        if (msg.includes(`频次过高`)) {
                            sub.error(new UserLogoutError())
                            return;
                        }
                        // 暂时移除 systemMessage
                        // this.systemMessage.sendText('detail.error', {
                        //     title: message,
                        //     content: message
                        // }).subscribe();
                        console.log(`请求风控`, res)
                        sub.complete()
                        return;
                    }
                }).catch(async e => {
                    console.error(e)
                    console.error(`代理请求异常：${(e as Error).message}`)
                    if (e instanceof AxiosError) {
                        const response = e.response!;
                        const code = e.code;
                        if (code === 'ETIMEDOUT') {
                            console.log(`414 错误`)
                            sub.error(new Error414())
                            return;
                        }
                        if (!response) {
                            console.log(`位置 错误 ${e.message}`)
                            sub.error(e)
                            return;
                        }
                        const data: string = response.data;
                        const status: number = response.status;
                        if (status == 400) {
                            console.log(`400 错误`)
                            sub.error(new Error400())
                            return;
                        }
                        if (data.includes('Request failed with status code 500')) {
                            console.log(`500 错误`)
                            sub.error(e)
                            return;
                        }
                        const is400 = data.includes('Request failed with status code 400')
                        if (is400) {
                            console.log(`帖子已失效`)
                            sub.error(new Error400())
                            return;
                        }
                        if (data.includes('only be seen by login user')) {
                            console.log(`需要重新登录`)
                            await this.weiboUserPool.destroy(user.user!)
                            sub.error(new UserLogoutError())
                            return;
                        }
                        if (data.includes('Request failed with status code 414')) {
                            console.log(`414 错误`)
                            sub.error(new Error414())
                            return;
                        }
                    }
                    const error = e as AxiosError;
                    if (error?.response?.status === 504) {
                        console.log(`微博抓取详情失败 ${e.message}`)
                        sub.error(e)
                        return;
                    }
                    console.log(`微博抓取详情失败 ${e.message}`)
                    sub.error(e)
                })
            })
        } else {
            /**
            * 没有有效登陆用户时，停止运行，直到有登陆触发时，重新开始
            * */
            throw new UserLogoutError()
        }
    }
    private ids: number[] = []
    async saveDb(article: News) {
        console.log(this.ids.join(','))
        const db = this.injector.get(Db)
        const old = await db.manager.findOne(News, {
            select: ['id', 'aid', 'keywords'],
            where: { aid: article.aid },
        });
        if (old) {
            article.id = old.id;
            article.update_date = new Date()
            this.ids.push(old.id)
            console.log(`帖子已存在，更新文章/帖子 ${old.id} ${article.aid} === ${old.aid} ${article.keywords} === ${old.keywords}`)
            return await db.manager.update(News, old.id, article).catch(e => {
                console.error(e)
                const msg = e.message as string;
                if (msg.includes('duplicate key value violates unique constraint')) {
                    console.error("udpate " + article.aid + " " + msg)
                    return;
                }
                throw e;
            })
        } else {
            console.log(`帖子不存在，新增文章/帖子`)
            return await db.manager.save(News, article).catch(e => {
                console.error(e)
                const msg = e.message as string;
                if (msg.includes('duplicate key value violates unique constraint')) {
                    console.error("add " + article.aid + " " + msg)
                    return;
                }
                throw e;
            })
        }
    }
    async saveLocal(article: News) {
        const data = article.data;
        const id = data.id;
        if (id) {
            const root = this.injector.get(APP_ROOT)
            const created_at = new Date(data.created_at)
            const month = created_at.getMonth() + 1
            const day = created_at.getDate()
            const hour = created_at.getHours()
            const filePath = join(root, 'data/weibo', `${month}`, `${day}`, `${hour}`);
            ensureDirSync(filePath)
            const stream = createWriteStream(join(filePath, `${id}.json`));
            if (data.ok == 0) {
                return;
            } else {
                return new Promise<void>((resolve, reject) => {
                    stream.write(JSON.stringify(article, null, 2), (err) => {
                        if (err) reject(err)
                        stream.close();
                        resolve()
                    })
                })
            }
        }
    }
    async createKey(url: string): Promise<string> {
        return this.md5(url)
    }
    strToInt(str: string) {
        return parseInt(str)
    }
}
