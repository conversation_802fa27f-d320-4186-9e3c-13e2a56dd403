export class Author {
    avatar!: string;
    nickname!: string;
    create_at!: Date;
}
export class Topic {
    title!: string;
}
export class Article {
    title!: string;
    platform!: string;
    author!: string;
    content!: string;
    images!: string[];
    videos!: string[];
    comment_count!: number;
    good_count!: number;
    share_count!: number;
    post_at!: Date;
    create_at!: Date;
    shares!: ArticleShare[];
    comments!: ArticleComment[];
    goods!: ArticleGood[];
    topics!: ArticleTopic[];
}
export class ArticleComment {
    content!: string;
    author!: string;
    good_count!: number;
    children!: ArticleComment[]
}
export class ArticleShare {
    content!: string;
    author!: string;
    post_at!: Date;
}
export class ArticleGood {
    author!: string;
}
export class ArticleTopic {
    title!: string;
    link!: string;
}