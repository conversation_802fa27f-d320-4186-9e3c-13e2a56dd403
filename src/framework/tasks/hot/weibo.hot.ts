import { Observable } from "rxjs";
import { Message } from "../../rabbitmq";
import { <PERSON> } from "../../spider";

export class WeiboHotSpider extends Spider {
    dead<T = any>(msg: Message<any>): Promise<void | Observable<T>> {
        throw new Error("Method not implemented.");
    }
    run<T = any>(msg: Message<any>): Promise<void | Observable<T>> {
        throw new Error("Method not implemented.");
    }
    createKey(url: string): string | Promise<string> {
        throw new Error("Method not implemented.");
    }
}