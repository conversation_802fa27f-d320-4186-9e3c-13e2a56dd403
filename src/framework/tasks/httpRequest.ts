import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Agent } from 'https'

let request = require('superagent')
require('superagent-proxy')(request)
const agent = new Agent({
    rejectUnauthorized: false, // 忽略证书验证
});
interface ProxyData {
    ip: string;
    port: number;
    expire: number;
}

let _currentProxy: Promise<ProxyData> | null = null;
let isFetchingProxy = false;

function isProxyExpired(expire: number): boolean {
    return Date.now() >= expire;
}

async function fetchNewProxy(): Promise<void> {
    if (isFetchingProxy) {
        // 等待直到获取完成
        await new Promise<void>(resolve => setTimeout(() => {
            resolve()
        }, 1000 * 10));
        return;
    }
    isFetchingProxy = true;
    try {
        // const url = `http://api.tianqiip.com/getip?secret=fdmf7bkcooa79fxh&num=1&type=json&port=3&time=3&mr=1&sign=b971f42ffa00066c1ca44293713b6085`
        const url = `http://api.tianqiip.com/getip?secret=fdmf7bkcooa79fxh&num=1&type=json&port=2&time=3&mr=1&sign=b971f42ffa00066c1ca44293713b6085`
        _currentProxy = axios.get<{ code: number, data: Array<{ ip: string, port: number, expire: string }> }>(url).then(response => {
            if (response.data.code === 1000 && response.data.data.length > 0) {
                const proxy = response.data.data[0];
                console.log(`获取代理IP成功`, proxy)
                return {
                    ip: proxy.ip,
                    port: proxy.port,
                    expire: Date.now() + 1000 * 60 * 3
                };
            } else {
                console.log(`加载代理IP失败，请检查是否欠费`)
                throw new Error('Failed to fetch proxy');
            }
        });
    } finally {
        isFetchingProxy = false;
    }
}

export async function makeRequestWithProxy<T>(config: AxiosRequestConfig, retries = 1): Promise<AxiosResponse<T>> {
    // 检查代理是否存在或过期
    if (!_currentProxy) {
        console.log(`代理地址为空-----`)
        await fetchNewProxy();
        // 再次检查是否获取成功
        if (!_currentProxy) {
            throw new Error('Proxy is not available');
        }
    }
    const currentProxy = await _currentProxy;
    if (isProxyExpired(currentProxy.expire)) {
        console.log(`代理IP已过期---重新获取`)
        await fetchNewProxy();
        // 再次检查是否获取成功
        if (!currentProxy) {
            throw new Error('Proxy is not available');
        }
    }
    try {
        // let proxyUrl = `socks5://${currentProxy.ip}:${currentProxy.port}`
        let httpsProxyUrl = `http://${currentProxy.ip}:${currentProxy.port}`
        // const proxyConfig: AxiosRequestConfig = {
        //     ...config,
        //     proxy: {
        //         host: currentProxy.ip,
        //         port: currentProxy.port,
        //         protocol: 'https'
        //     },
        //     httpsAgent: agent
        // }
        // const agent = new SocksProxyAgent(proxyUrl);
        return request.get(config.url)
            .set(config.headers || {})
            .proxy(httpsProxyUrl)
            .agent(agent)
            .then((res: any) => {
                try {
                    return {
                        data: JSON.parse(res.text),
                        status: res.status,
                        headers: res.headers
                    }
                } catch (e) {
                    return {
                        data: res.text,
                        status: res.status,
                        headers: res.headers
                    }
                }
            })
            .catch((e: any) => {
                const { status, response } = e;
                console.error(`makeRequestWithProxy`, { status, response, config })
                throw e;
            })
        // return axios.request<T>(proxyConfig);
    } catch (e) {
        if (retries > 0) {
            // 可能是代理失效，强制刷新代理
            return makeRequestWithProxy(config, retries - 1);
        }
        console.error("makeRequestWithProxy", e)
        throw e;
    }
}
