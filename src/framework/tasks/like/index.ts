import { Injector, StaticProvider } from "@nger/core";
import { <PERSON><PERSON>pid<PERSON> as Spider } from "../../spider";
import { WeiboLikeSpider } from "./weibo.like";

export const likeProvicers: StaticProvider<any>[] = [
    {
        provide: Spider,
        useFactory: (injector: Injector) => {
            return new WeiboLikeSpider(injector)
        },
        deps: [Injector],
        multi: true
    }
]
