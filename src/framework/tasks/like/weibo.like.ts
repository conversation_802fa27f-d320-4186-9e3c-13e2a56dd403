import { Db } from "@nger/typeorm";
import axios from "axios";
import { from, Observable } from "rxjs";
import { News } from "../../../entities/news";
import { SpiderUser } from "../../../entities/spider_user";
import { Message } from "../../rabbitmq";
import { Spider } from "../../spider";
import { Error414, UserLogoutError } from "../error";
import { LikeTask } from "../like.task";
import { makeRequestWithProxy } from "../httpRequest";

export class WeiboLikeSpider extends Spider {
    platform: string = 'weibo';
    type: string = 'like';
    async getUser(): Promise<SpiderUser> {
        let user = await this.weiboUserPool.acquire().catch(e => {
            return undefined;
        })
        while (!user) {
            await this.delay(10000 * 10 * Math.random())
            user = await this.weiboUserPool.acquire().catch(e => {
                return undefined;
            });
        }
        return user;
    }
    async run<T = any>(msg: Message<any>): Promise<void | Observable<T>> {
        const { payload } = msg;
        const { id, aid } = payload;
        let user = await this.getUser();
        if (user) {
            const p = 0;
            const psize = 200;
            const news = new News();
            news.id = id;
            news.aid = aid;
            news.like = news.like || [];
            const res = await this.getLikeShow(aid, p + 1, psize, news);
            if (res instanceof Error) {
                throw res;
            }
            await this.updateDb(news);
            this.weiboUserPool.release(user)
        }
    }

    async updateDb(article: News) {
        const db = this.injector.get(Db)
        const { aid, id, like } = article;
        if (like && like.length > 0) {
            return await db.manager.update(News, article.id, { like }).catch(e => {
                const msg = e.message as string;
                if (msg.includes('duplicate key value violates unique constraint')) {
                    return;
                }
                throw e;
            });
        }
    }
    private async getHeaders() {
        const user = await this.getUser()
        if (user) {
            const cookies: any[] = user.cookies;
            const headers = {
                cookie: cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; '),
                ['user-agent']: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36'
            };
            return { headers, user };
        }
        return;
    }
    private async getLikeShow(id: number, page: number, psize: number, news: News): Promise<any> {
        if (!id) return;
        const url = `https://weibo.com/ajax/statuses/likeShow?id=${id}&attitude_type=0&attitude_enable=1&page=${page}&count=${psize}`
        const headers = await this.getHeaders();
        return this.retry(async () => {
            if (headers) {
                return makeRequestWithProxy<any>({
                    method: 'get',
                    url: url,
                    headers: headers.headers
                }).then(async res => {
                    const { data, ok, total_number } = res.data;
                    if (ok === -100) {
                        await this.weiboUserPool.destroy(headers.user);
                        return new UserLogoutError()
                    }
                    if (ok == 1 && data.length > 0) {
                        news.like.push(...data)
                        const current = (page - 1) * psize
                        if (total_number > current) {
                            return this.getLikeShow(id, page + 1, psize, news)
                        }
                    }
                }).catch(e => {
                    const message = e.message as string;
                    const is414 = message.includes('Request failed with status code 414');
                    if (is414) {
                        throw new Error414();
                    }
                    const is400 = message.includes('Request failed with status code 400')
                    if (is400) {
                        return;
                    }
                    throw e;
                });
            }
        }, '获取点赞-getLikeShow');
    }

    async dead(msg: Message<any>): Promise<void | Observable<any>> {
        const task = this.injector.get(LikeTask)
        return from(task.publish(msg))
    }
    createKey(url: string): string | Promise<string> {
        throw new Error("Method not implemented.");
    }
}