import { Injector, St<PERSON>Provider } from "@nger/core";
import { <PERSON><PERSON><PERSON><PERSON> as <PERSON> } from "../../spider";
import { WeiboListSpider } from "./weibo.list";

export const listProvicers: StaticProvider<any>[] = [
    {
        provide: Spider,
        useFactory: (injector: Injector) => {
            return new WeiboListSpider(injector)
        },
        deps: [Injector],
        multi: true
    },
]
