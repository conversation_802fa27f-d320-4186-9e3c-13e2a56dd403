import { load } from "cheer<PERSON>";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPR<PERSON>ponse, Page } from "@nger/puppeteer";
import { from, Observable, Subscriber } from "rxjs";
import { SpiderUser } from "../../../entities/spider_user";
import { Message } from "../../rabbitmq";
import { BrowserRe<PERSON>, Spider } from "../../spider";
import { ListTask } from "../list.task";
import { DetailLink } from "../types";
import { ARTICLE_DETAIL_KEY, ARTICLE_DETAIL_TOTAL, ARTICLE_DETAIL_V } from "../const";
import { DetailTask } from "../detail.task";
import { Error400, Error414, UserLogoutError } from "../error";
import { PageTask } from "../page.task";
import { SystemMessage } from "../../../v2/message";
import { writeFile, writeFileSync } from "fs";
export interface WeiboSearchPayload {

}
export class <PERSON><PERSON><PERSON>ist<PERSON><PERSON><PERSON> extends Spider {
    async dead<T = any>(msg: Message<any>): Promise<void | Observable<any>> {
        const task = this.injector.get(ListTask)
        return from(task.publish(msg))
    }
    bf: string = ARTICLE_DETAIL_KEY
    v: number = ARTICLE_DETAIL_V
    total: number = ARTICLE_DETAIL_TOTAL;
    platform: string = 'weibo';
    type: string = 'list';
    get list() {
        return this.injector.get(ListTask)
    }
    private formatDate(startDate: Date) {
        return this.format(startDate, 'yyyy-MM-dd-hh')
    }
    format(date: Date, fmt: string) {  // author: meizz 
        const o: any = {
            "M+": date.getMonth() + 1,  // 月份 
            "d+": date.getDate(),  // 日 
            "h+": date.getHours(),  // 小时 
            "m+": date.getMinutes(),  // 分 
            "s+": date.getSeconds(),  // 秒 
            "q+": Math.floor((date.getMonth() + 3) / 3),  // 季度 
            "S": date.getMilliseconds()  // 毫秒 
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (let k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    async getUser(): Promise<SpiderUser> {
        let user = await this.weiboUserPool.acquire().catch(e => {
            return undefined;
        })
        while (!user) {
            await this.delay(10000 * 10 * Math.random())
            user = await this.weiboUserPool.acquire().catch(e => {
                return undefined;
            });
        }
        this.weiboUserPool.release(user)
        return user;
    }
    async run(action: Message<any>): Promise<Observable<any> | void> {
        const { payload, type } = action;
        const url = payload.url.replace('&suball=1', '');
        console.log(`list url: ${url}`)
        let user = await this.getUser();
        if (user) {
            this.weiboUserPool.release(user)
            const cookies = user.cookies;
            const headers = {};
            const req = new BrowserRequest(url, cookies, headers);
            return this.openInBrowser(req, async (page, sub) => {
                return this.handler(page, sub, user!, payload);
            }, 'domcontentloaded', () => { }, (p, e) => {
                console.log(e)
            })
        }
    }
    get systemMessage() {
        return this.injector.get(SystemMessage)
    }
    async handler(page: Page | HTTPResponse | HTTPRequest, sub: Subscriber<any>, user: SpiderUser, payload: any) {
        if (this.isPage(page)) {
            const url = page.url()
            if (url.startsWith('search_need_login')) {
                this.systemMessage.sendText('logout', { title: 'login.fail', content: `reason: search_need_login, url: ${page.url()},${user.username} logout` }).subscribe();
                await this.weiboUserPool.destroy(user)
                sub.error(new UserLogoutError())
                return;
            }
            if (url.includes('passport.weibo.com')) {
                this.systemMessage.sendText('logout', { title: 'login.fail', content: `reason: search_need_login, url: ${page.url()},${user.username} logout` }).subscribe();
                await this.weiboUserPool.destroy(user)
                sub.error(new UserLogoutError())
                return;
            }
            const content = await page.content()
            if (content.includes('Sina Visitor System')) {
                console.log(`${url} 登陆失效`)
                this.systemMessage.sendText('logout', { title: 'login.fail', content: `reason: Sina Visitor System, url: ${page.url()},${user.username} logout` }).subscribe();
                await this.weiboUserPool.destroy(user)
                sub.error(new UserLogoutError())
                return;
            }
            if (content.includes('404 Page not found')) {
                console.log(`${url} 404 Page not found`)
                this.systemMessage.sendText('404', { title: '抱歉，未找到', content: `${url}` }).subscribe();
                sub.error(new Error400())
                return;
            }
            if (content.includes('抱歉，未找到')) {
                console.log(`${url} 抱歉，未找到`)
                this.systemMessage.sendText('404', { title: '抱歉，未找到', content: `${url}` }).subscribe();
                sub.error(new Error400())
                return;
            }
            if (content.includes('登录') && content.includes('注册')) {
                console.log(`登陆失效`);
                this.systemMessage.sendText('logout', { title: 'login.fail', content: `reason: loginBtn, url: ${page.url()},${user.username} logout` }).subscribe();
                await this.weiboUserPool.destroy(user);
                sub.error(new UserLogoutError());
                return;
            }
            const $ = this.load(content);
            const element = $('.LoginBtn_btn_10QRY');
            if (element.length > 0) {
                console.log(`登陆失效`);
                this.systemMessage.sendText('logout', { title: 'login.fail', content: `reason: loginBtn, url: ${page.url()},${user.username} logout` }).subscribe();
                await this.weiboUserPool.destroy(user);
                sub.error(new UserLogoutError());
                return;
            }
            // #pl_feedlist_index > div.card-wrap > div > p
            // #pl_feedlist_index > div:nth-child(2) > div:nth-child(1) > div.card > div.card-feed > div.content > div.from > a:nth-child(1)
            // #pl_feedlist_index > div:nth-child(2) > div:nth-child(1) > div > div.card-feed > div.content > div.from > a:nth-child(1)
            const links = $('#pl_feedlist_index .card-feed')
            console.log(`links length is ${links.length}`)
            if (links.length === 0) {
                const url2 = await page.url()
                writeFileSync(`${url2}.html`, content)
            }
            const detailSend: DetailLink[] = [];
            // #pl_feedlist_index > div:nth-child(1) > div:nth-child(9) > div > div.card-feed > div.content > div.from > a:nth-child(1)
            links.map((index, item) => {
                try {
                    const $ = load(item);
                    // #pl_feedlist_index > div:nth-child(2) > div:nth-child(1) > div.card > div.card-feed > div.content > div.from > a:nth-child(1)
                    const linkElement = $('.content > .from > a:nth-child(1)');
                    const link = linkElement[0]?.attribs['href'];
                    console.log(`find a link ${link}`)
                    const d = new DetailLink();
                    const url = this.parseUrl(link);
                    if (url) {
                        d.platform = this.platform;
                        d.href = url;
                        d.keyword = payload.keyword;
                        detailSend.push(d);
                    }
                } catch (e) {
                    console.error(e)
                }
            });
            if (detailSend.length > 0) {
                const detailTask = this.injector.get(DetailTask)
                console.log(`send detail ${detailSend.length}`)
                await detailTask.publishs(detailSend.map(payload => {
                    return {
                        type: 'detail',
                        payload
                    }
                }))
                this.systemMessage.sendText('list', { title: 'list.detail', content: `send detail task ${detailSend.length}` }).subscribe();
            } else {
                console.log(`一个帖子都没有哦！`, url)
            }
            const pageSend: string[] = [];
            // feed_list_page_morelist s-scroll
            const moreList = $('ul[node-type="feed_list_page_morelist"] > li')
            let activateIndex: number = 0;
            console.log(`-----------------------------------------`)
            console.log(`一共有${moreList.length}个列表页面`)

            moreList.map((index, el) => {
                const cur = el.attribs['class'] && el.attribs['class'].includes('cur')
                if (!cur) {
                    const $ = load(el)
                    const a = $('a')[0]
                    const href = a.attribs['href'];
                    const url = this.parseUrl(`https://s.weibo.com${href}`)!
                    pageSend.push(url)
                } else {
                    console.log(`${index}时当前页面`)
                    activateIndex = index;
                }
            });
            if (pageSend.length > 0) {
                const pageTask = this.injector.get(PageTask)
                await pageTask.publishs(pageSend.map(p => {
                    return {
                        type: 'page',
                        payload: {
                            url: p,
                            platform: this.platform,
                            keyword: payload.keyword
                        }
                    }
                }))
                this.systemMessage.sendText('list', { title: 'list.page', content: `send page task ${pageSend.length}` }).subscribe();
                await this.delay(1000 * Math.random() * 30)
            }
            console.log(`${pageSend.length}-----------------------------------------\n\n`)
            await this.delay(1000 * Math.random() * 30)
            sub.complete();
        } else if (this.isHttpResponse(page)) {
        }
        else {
            page.continue()
        }
    }
    parseUrl(url: string) {
        if (!url) {
            return;
        }
        if (url.startsWith('https')) {
            return url
        } else {
            return `https:${url}`
        }
    }
    createKey(href: string): string {
        const url = new URL(href)
        const key = url.hostname + url.pathname;
        return this.md5(key)
    }
}
