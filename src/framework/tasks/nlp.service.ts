import { Injectable } from "@nger/core";
import axios from "axios";


@Injectable()
export class NlpService {

    get url() {
        return process.env['NLP_URL'] || 'http://192.168.2.111:8082'
    }

    get cutUrl() {
        return this.url + '/cut'
    }

    get sentaUrl() {
        return this.url + '/senta'
    }

    retry(fn: Function, data: any, times: number = 10, delay: number = Math.random() * 1000 * 10) {
        return new Promise(function (resolve, reject) {
            const tFn = function () {
                fn().then(resolve).catch((e: any) => {
                    if (times-- > 0) {
                        console.log(`还有${times}次机会`, data)
                        setTimeout(tFn, delay)
                    } else {
                        reject(e)
                    }
                })
            }
            return tFn()
        })
    }

    cut(texts: string[]): Promise<any> {
        return this.retry(() => {
            return axios.post(this.cutUrl, { texts }, {
                headers: {
                    'content-type': 'application/json'
                },
                timeout: 0
            }).then(res => res.data)
        }, texts)
    }

    senta(texts: string[]): Promise<any> {
        return this.retry(() => {
            return axios.post(this.sentaUrl, {
                texts: texts
            }, {
                headers: {
                    'content-type': 'application/json'
                },
                timeout: 0
            }).then(res => res.data)
        }, texts)
    }

}