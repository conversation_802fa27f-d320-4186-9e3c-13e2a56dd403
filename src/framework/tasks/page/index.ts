import { Injector, St<PERSON>Provider } from "@nger/core";
import { <PERSON><PERSON>pid<PERSON> as <PERSON> } from "../../spider";
import { WeiboPageSpider } from "./weibo.page";

export const pageProvicers: StaticProvider<any>[] = [
    {
        provide: Spider,
        useFactory: (injector: Injector) => {
            return new WeiboPageSpider(injector)
        },
        deps: [Injector],
        multi: true
    },
]
