import { load, text } from "cheerio";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPR<PERSON>ponse, Page, User } from "@nger/puppeteer";
import { bufferTime, catchError, concat, concatAll, concatMap, filter, from, merge, mergeMap, Observable, of, Subscriber, takeLast, tap, zip } from "rxjs";
import { SpiderUser } from "../../../entities/spider_user";
import { Message } from "../../rabbitmq";
import { BrowserRequest, Spider } from "../../spider";
import { ListTask } from "../list.task";
import { Detail, DetailLink, Link } from "../types";
import { ARTICLE_DETAIL_KEY, ARTICLE_DETAIL_TOTAL, ARTICLE_DETAIL_V } from "../const";
import { DetailTask } from "../detail.task";
import { Error400, UserLogoutError } from "../error";
import { PageTask } from "../page.task";
export interface WeiboSearchPayload {

}
export class WeiboPageS<PERSON><PERSON> extends Spider {
    async dead<T = any>(msg: Message<any>): Promise<void | Observable<any>> {
        const task = this.injector.get(PageTask)
        return from(task.publish(msg))
    }
    bf: string = ARTICLE_DETAIL_KEY
    v: number = ARTICLE_DETAIL_V
    total: number = ARTICLE_DETAIL_TOTAL;
    platform: string = 'weibo';
    type: string = 'page';
    get list() {
        return this.injector.get(ListTask)
    }
    private formatDate(startDate: Date) {
        return this.format(startDate, 'yyyy-MM-dd-hh')
    }
    format(date: Date, fmt: string) {  // author: meizz 
        const o: any = {
            "M+": date.getMonth() + 1,  // 月份 
            "d+": date.getDate(),  // 日 
            "h+": date.getHours(),  // 小时 
            "m+": date.getMinutes(),  // 分 
            "s+": date.getSeconds(),  // 秒 
            "q+": Math.floor((date.getMonth() + 3) / 3),  // 季度 
            "S": date.getMilliseconds()  // 毫秒 
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (let k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    async getUser(): Promise<SpiderUser> {
        let user = await this.weiboUserPool.acquire().catch(e => {
            return undefined;
        })
        while (!user) {
            await this.delay(10000 * 10 * Math.random())
            user = await this.weiboUserPool.acquire().catch(e => {
                return undefined;
            });
        }
        this.weiboUserPool.release(user)
        return user;
    }
    async run(action: Message<any>): Promise<Observable<any> | void> {
        const { payload, type } = action;
        const url = payload.url;
        let user = await this.getUser();
        if (user && url) {
            this.weiboUserPool.release(user)
            const cookies = user.cookies;
            const headers = {};
            const req = new BrowserRequest(url, cookies, headers);
            return this.openInBrowser(req, async (page, sub) => {
                return this.handler(page, sub, user!, payload);
            }, 'domcontentloaded', () => { }, (p, e) => {
                console.log(e)
            })
        }
    }
    async handler(page: Page | HTTPResponse | HTTPRequest, sub: Subscriber<any>, user: SpiderUser, payload: any) {
        if (this.isPage(page)) {
            const url = page.url()
            console.log(`page url ${url}`)
            if (url.startsWith('search_need_login')) {
                console.log(`登陆失效`)
                await this.weiboUserPool.destroy(user)
                sub.error(new UserLogoutError())
                return;
            }
            const content = await page.content()
            if (content.includes('404 Page not found')) {
                console.log(content)
                sub.complete();
                return;
            }
            const $ = this.load(content);
            const element = $('.loginBtn');
            if (element.length > 0) {
                await this.weiboUserPool.destroy(user);
                sub.error(new UserLogoutError());
                return;
            }
            // #pl_feedlist_index > div:nth-child(2) > div:nth-child(1) > div > div.card-feed > div.content > div.from > a:nth-child(1)
            // #pl_feedlist_index > div:nth-child(2) > div:nth-child(1) > div > div.card-feed > div.content > div.from > a:nth-child(1)
            const links = $('#pl_feedlist_index .card-feed')
            console.log(`共有 ${links.length}个链接`)
            const waitSend: DetailLink[] = [];
            links.map((index, item) => {
                try {
                    const $ = load(item);
                    const linkElement = $('div.content > .from > a');
                    const link = linkElement[0]?.attribs['href'];
                    const d = new DetailLink();
                    const url = this.parseUrl(link);
                    if (url) {
                        d.platform = this.platform;
                        d.href = url;
                        d.keyword = payload.keyword;
                        waitSend.push(d);
                    } else {
                        console.error("链接解析失败", link)
                    }
                } catch (e) {
                    console.error(`解析链接出错`, e)
                }
            });
            if (waitSend.length > 0) {
                const detailTask = await this.injector.get(DetailTask)
                await detailTask.publishs(waitSend.map(payload => ({ type: 'detail', payload })))
                console.log(`send detail task ${waitSend.length}`)
            } else {
                console.log(`not found detail task`)
            }
            await this.delay(1000 * Math.random() * 30)
            sub.complete();
        } else if (this.isHttpResponse(page)) {
        }
        else {
            page.continue()
        }
    }
    parseUrl(url: string) {
        if (!url) {
            return;
        }
        if (url.startsWith('https')) {
            return url
        } else {
            return `https:${url}`
        }
    }
    createKey(href: string): string {
        const url = new URL(href)
        const key = url.hostname + url.pathname;
        return this.md5(key)
    }
}