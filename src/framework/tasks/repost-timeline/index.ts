import { Injector, St<PERSON>Provider } from "@nger/core";
import { <PERSON><PERSON>pid<PERSON> as Spider } from "../../spider";
import { WeiRepostTimelineSpider } from "./weibo.repost-timeline";

export const repostTimelineProvicers: StaticProvider<any>[] = [
    {
        provide: Spider,
        useFactory: (injector: Injector) => {
            return new WeiRepostTimelineSpider(injector)
        },
        deps: [Injector],
        multi: true
    }
]
