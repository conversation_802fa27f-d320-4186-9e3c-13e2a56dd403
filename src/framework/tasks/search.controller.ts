import { Controller, Get, Injector } from "@nger/core";
import { Body, Post } from "@nger/http";
import { Observable } from "rxjs";
import { WeiboAccountSearch } from "./account-search/weibo.account-search";
import { ZhihuAccountSearch } from "./account-search/zhihu.account-search";
import { AccountTask } from "./account.task";
import { SearchTask } from "./search.task";


@Controller('@nger/task')
export class SearchController {
    get weiboAccount() {
        return this.injector.get(WeiboAccountSearch)
    }
    get zhihuAccount() {
        return this.injector.get(ZhihuAccountSearch)
    }
    get searchTask() {
        return this.injector.get(SearchTask)
    }
    get accountTask() {
        return this.injector.get(AccountTask)
    }
    constructor(private injector: Injector) { }
    @Post('account')
    account(@Body() body: any) {
        const { keyword, platform } = body;
        switch (platform) {
            case 'weibo':
                return this.weiboAccount.run({ type: 'search-account', payload: { keyword } }).then(res => {
                    if (res) return this.getObservableList(res)
                    return [];
                })
            case 'zhihu':
                return this.zhihuAccount.run({ type: 'search-account', payload: { keyword } }).then(res => {
                    if (res) return this.getObservableList(res)
                    return [];
                })
        }
        return body;
    }
    private getObservableList(obs: Observable<any>) {
        return new Promise<any[]>((resolve, reject) => {
            const list: any[] = []
            obs.subscribe({
                next: (res) => {
                    list.push(res)
                },
                complete: () => {
                    resolve(list)
                },
                error: (e) => reject(e)
            })
        })
    }
    @Get('index')
    index() {
        return 'hello world!'
    }
    @Post('send')
    async publish(@Body() body: any) {
        console.log(body)
        const { type, payload } = body;
        if (type === 'search') {
            await this.searchTask.publish(body)
        }
        if (type === 'account') {
            await this.accountTask.publish(body)
        }
        return { ok: 1 };
    }
    @Post('publishs')
    publishs(@Body() body: any) {
        const { type, list } = body;
        if (type === 'search') {
            return this.searchTask.publishs(list)
        }
        if (type === 'account') {
            return this.accountTask.publishs(list)
        }
    }
}