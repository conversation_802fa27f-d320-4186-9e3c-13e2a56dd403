import { from, Observable, of, Subscriber, filter, tap, concatMap, mergeMap } from "rxjs";
import { Message } from "../../rabbitmq";
import { BrowserRequest, <PERSON> } from "../../spider";
import { load, text } from 'cheerio'
import { HTTPRequest, HTTPResponse, Page } from "@nger/puppeteer";
import { Link, Detail } from '../types'
export interface WeiboSearchPayload {
    keyword: string;
}
export class BaiduSearchSpider extends Spider {
    platform: string = 'baidu';
    type: string = 'search';
    async dead<T = any>(msg: Message<any>): Promise<void | Observable<T>> {
    }
    async run(msg: Message<WeiboSearchPayload>): Promise<Observable<any> | void> {
        const { payload, type } = msg;
        const keyword = encodeURIComponent(payload.keyword)
        const url = `https://www.baidu.com/s?rtt=1&bsst=1&cl=2&tn=news&rsv_dl=ns_pc&word=${keyword}`
        const headers = {};
        const cookies: any[] = [];
        const req = new BrowserRequest(url, cookies, headers)
        return this.openInBrowser(req, this.handler.bind(this)).pipe(
            mergeMap(url => {
                if (Link.isLink(url)) {
                    const req = new BrowserRequest(url.href, cookies, headers)
                    return this.openInBrowser(req, async (page, sub) => {
                        // get page list
                        if (this.isPage(page)) {
                            const content = await page.content()
                            const $ = this.load(content);
                            const links = $('#content_left .result-op')
                            links.map((index, result) => {
                                try {
                                    const $ = load(result)
                                    const link = $('h3>a')[0]
                                    const href = link.attribs['href']
                                    const title = text([link]).trim()
                                    const span = $('.news-source_Xj4Dv span.c-color-gray')
                                    const author = text(span).trim()
                                    const img = $('.news-source_Xj4Dv .c-img > img')[0]
                                    const avatar = img?.attribs['src']
                                    sub.next(new Detail(href, title, author, avatar))
                                } catch (e) {
                                    console.error(e)
                                }
                            })
                            await this.delay(Math.random() * 1000)
                        }
                    })
                }
                return of(url)
            }),
            mergeMap((res: any) => {
                return from(this.publishDetail(res).catch(e => console.log(e)))
            })
        )
    }
    async handler(page: Page | HTTPResponse | HTTPRequest, sub: Subscriber<any>) {
        if (this.isPage(page)) {
            const title = await page.title()
            if (title.includes('百度安全验证')) {
                // 百度安全验证
                await page.waitForNavigation({ timeout: 0 });
            }
            const content = await page.content()
            const $ = this.load(content)
            const links = $('#content_left .result-op');
            links.map((index, result) => {
                try {
                    const $ = load(result);
                    const link = $('h3>a')[0];
                    const href = link.attribs['href'];
                    const title = text([link]).trim();
                    const span = $('.news-source_Xj4Dv span.c-color-gray');
                    const author = text(span).trim();
                    const img = $('.news-source_Xj4Dv .c-img > img')[0];
                    const avatar = img?.attribs['src'];
                    sub.next(new Detail(href, title, author, avatar));
                } catch (e) {
                    console.error(e);
                }
            });
            const children = $('#page>div').children();
            const endIndex = children.length - 1;
            let isEnd = false;
            let lastUrl: string = ``;
            let current: number = 0;
            children.map((index, child) => {
                if (child.tagName !== 'a') {
                    current = index;
                }
                if (index === endIndex) {
                    const t = text([child])
                    if (t.includes('下一页')) {
                        isEnd = false;
                    } else {
                        isEnd = true
                    }
                }
            })
            if (!isEnd) {
                children.map((index, child) => {
                    if (child.tagName === 'a') {
                        const href = child.attribs['href'];
                        if (index !== endIndex && current < index) {
                            sub.next(new Link(`https://www.baidu.com${decodeURI(href)}`))
                        }
                        if (index === endIndex - 1) {
                            // 最后一个链接
                            lastUrl = `https://www.baidu.com${decodeURI(href)}`
                        }
                    }
                });
                if (!isEnd && lastUrl) {
                    await page.goto(lastUrl);
                    await this.handler(page, sub);
                }
            }
        } else if (this.isHttpResponse(page)) { 
            // 
        }
        else {
            page.continue()
        }
    }
    total: number = 0;
    async createKey(href: string): Promise<string> {
        const url = new URL(href)
        const entries = url.searchParams.entries()
        const params = [];
        for (let [key, val] of entries) {
            params.push(`${key}=${val}`)
        }
        const key = url.hostname + url.pathname + '?' + params.sort().join(';');
        return this.md5(key)
    }
}
