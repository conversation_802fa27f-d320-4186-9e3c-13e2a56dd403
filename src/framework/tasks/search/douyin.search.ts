import { Observable } from "rxjs";
import { Message } from "../../rabbitmq";
import { <PERSON> } from "../../spider";
export interface WeiboSearchPayload{

}
export class DouyinSearchSpider extends Spider {
    async dead<T = any>(msg: Message<any>): Promise<void | Observable<T>> {
    }
    platform: string = 'douyin';
    type: string = 'search';
    async run(msg: Message<any>): Promise<Observable<any> | void> { }
    createKey(url: string): Promise<string> {
        throw new Error("Method not implemented.");
    }
}
