import { Message } from "../../rabbitmq";
import { Spider, BrowserRequest } from "../../spider";
import { filter, from, map, Observable, of, switchMap } from "rxjs";
interface HeimaoPayload {
    keyword: string;
    open_schedule: boolean;
    schedule: string | null;
    platform: 'heimao';
}
/**
 * 
main{
    sn: '17361259723',
    title: '从来没有在九<span class="code-red">江</span><span class="code-red">银</span>行<span class="code-red">股份</span>有限公司借过，却在征信出现？？',
    cotitle: '九<span class="code-red">江</span><span class="code-red">银</span>行<span class="code-red">股份</span>有限公司',
    appeal: '解释',
    comment_id: 'tousu_complaint_14052983',
    timestamp: '1660699078',
    status: 7,
    upvote_amount: '0',
    share_amount: 0,
    summary: '今天查个人征信发现这个记录：\n' +
    '\n' +
    '2021年11月03日九江银行股份有限公司发放的44,360元（人民币）其他个人消费贷款，长期有效。截至2022年07月，余额20,266。\n' +
    '\n' +
    '问题是我从来没有结过这笔钱？这个从何而来呢？麻烦九江银行股份有限公司给个合理解释以及处理办法，我不要它出现在我的征信里。',
    url: '//tousu.sina.com.cn/complaint/view/17361259723/',
    evaluate_u: null,
    ext_src: '0',
    field: '48',
    cost: '44360',
    tpl: '0',
    comment_amount: 0
}
author {
    title: '匿名',
    avatar: '//n.sinaimg.cn/tech/hmapp/user_default.png',
    wb_profile: '//tousu.sina.cn'
}

author{
    title: '用户5745781660',
    avatar: '//tvax1.sinaimg.cn/default/images/default_avatar_male_180.gif?KID=imgbed,tva&Expires=1665543353&ssig=hKCIyvcg2O',
    intro: '',
    enterprise_status: 0,
    personal_status: 0,
    wb_profile: '//weibo.com/u/5745781660',
    enterprise: null,
    type: null,
    gender: 'm',
    location: '其他',
    wb_exist: true
}

 */
export interface EmptyAuthor {
    title: string;
    avatar: string;
    wb_profile: string;
}
export interface Author {
    title: string,
    avatar: string,
    intro: string,
    enterprise_status: number,
    personal_status: number,
    wb_profile: string,
    enterprise: string,
    type: string,
    gender: string,
    location: string,
    wb_exist: boolean
}
export interface HeimaoItem {
    sn: string,
    title: string,
    appeal: string,
    comment_id: string,
    timestamp: string,
    status: number,
    upvote_amount: string,
    share_amount: number,
    summary: string,
    url: string,
    evaluate_u: null,
    ext_src: string,
    field: string,
    cost: string,
    tpl: string,
    comment_amount: number;
    author: EmptyAuthor | Author;
}
export class HeimaoSearchSpider extends Spider<HeimaoPayload> {
    async dead<T = any>(msg: Message<HeimaoPayload>): Promise<void | Observable<T>> {
    }
    platform: string = 'heimao';
    type: string = 'search';
    async run(msg: Message<HeimaoPayload>): Promise<Observable<any> | void> {
        const { payload, type } = msg;
        const keyword = encodeURIComponent(payload.keyword)
        const url = `https://tousu.sina.com.cn/index/search/?keywords=${keyword}&t=0`;
        const headers = {};
        const cookies: any[] = [];
        const req = new BrowserRequest(url, cookies, headers)
        return this.openInBrowser<{ data: any, url: string }>(req, async (page, sub) => {
            if (this.isPage(page)) {
                await page.waitForSelector('#u_search_total')
            } else if (this.isHttpResponse(page)) {
                const url = page.url();
                if (url.includes('https://tousu.sina.com.cn/api/index/s')) {
                    await page.text().then(text => {
                        try {
                            const res = text.match(/try\{.*?\((.*)\)\;\}/i)
                            if (res && res.length > 0) {
                                const data = JSON.parse(res![1])
                                return sub.next({
                                    data,
                                    url
                                })
                            } else {
                                throw new Error('text parse error')
                            }
                        } catch (e) {
                            console.error(e)
                            throw e;
                        }
                    })
                }
            } else {
                page.continue()
            }
        }).pipe(
            switchMap(source => {
                const data = source.data
                return of(data).pipe(
                    map((res: any) => res.result),
                    map((res: any) => {
                        const { status, timestamp, data } = res;
                        const { code, msg } = status;
                        if (code === 0) {
                            const lists: any[] = data.lists;
                            const result = lists.map((item: any) => {
                                const { main, author } = item;
                                const data = {
                                    ...main,
                                    author
                                } as HeimaoItem
                                return this.createItem(source.url, data, 'nlp')
                            })
                            return result
                        }
                        throw new Error(msg)
                    }),
                    // switchMap(list => from(this.nlp.publishs(list)))
                )
            }),
        )
    }

    createKey(url: string): Promise<string> {
        throw new Error("Method not implemented.");
    }
}
