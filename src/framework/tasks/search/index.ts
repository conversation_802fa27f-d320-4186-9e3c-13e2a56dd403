import { Injector, <PERSON><PERSON><PERSON><PERSON>ider } from "@nger/core";
import { <PERSON><PERSON><PERSON><PERSON> as <PERSON> } from "../../spider";
import { BaiduSearchSpider } from "./baidu.search";
import { DouyinSearchSpider } from "./douyin.search";
import { HeimaoSearchSpider } from "./heimao.search";
import { QqNewSearchSpider } from "./qq-new.search";
import { ToutiaoSearchSpider } from "./toutiao.search";
import { WechatSearchSpider } from "./wechat.search";
import { WeiboSearchSpider } from "./weibo.search";
import { ZhihuSearchSpider } from "./zhihu.search";

export const searchProvicers: StaticProvider<any>[] = [
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new HeimaoSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // },
    {
        provide: Spider,
        useFactory: (injector: Injector) => {
            return new WeiboSearchSpider(injector)
        },
        deps: [Injector],
        multi: true
    },
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new BaiduSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // },
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new DouyinSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // },
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new QqNewSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // },
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new ToutiaoSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // },
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new WechatSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // },
    // {
    //     provide: Spider,
    //     useFactory: (injector: Injector) => {
    //         return new ZhihuSearchSpider(injector)
    //     },
    //     deps: [Injector],
    //     multi: true
    // }
]
