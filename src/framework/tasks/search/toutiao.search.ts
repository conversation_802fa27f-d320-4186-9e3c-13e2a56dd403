import { load, text } from "cheerio";
import { HTTPRequest, HTTPResponse, Page } from "@nger/puppeteer";
import { concatMap, filter, from, map, mergeMap, Observable, of, Subscriber, tap } from "rxjs";
import { Message } from "../../rabbitmq";
import { BrowserRequest, Spider } from "../../spider";
import { Detail, Link } from "../types";

export interface WeiboSearchPayload {

}
export class ToutiaoSearchSpider extends Spider {
    async dead<T = any>(msg: Message<any>): Promise<void | Observable<T>> {
    }
    platform: string = 'toutiao';
    type: string = 'search';
    async run(action: Message<any>): Promise<Observable<any> | void> {
        const { payload, type } = action;
        const keyword = encodeURIComponent(payload.keyword)
        const url = `https://so.toutiao.com/search?dvpf=pc&source=search_subtab_switch&keyword=${keyword}&pd=information&action_type=search_subtab_switch&from=news&cur_tab_title=news&page_num=0&search_id=`;
        const headers = {};
        const cookies: any[] = [];
        const req = new BrowserRequest(url, cookies, headers)
        return this.openInBrowser(req, this.handler.bind(this)).pipe(
            mergeMap((link) => {
                if (Link.isLink(link)) {
                    const req = new BrowserRequest(link.href, cookies, headers)
                    return this.openInBrowser(req, async (page, sub) => {
                        if (this.isPage(page)) {
                            const content = await page.content()
                            const $ = this.load(content);
                            const links = $('.s-result-list .result-content')
                            const end = links.length - 1;
                            links.map((index, item) => {
                                try {
                                    if (index === end) {
                                        return;
                                    }
                                    const $ = load(item)
                                    const linkElement = $('.cs-header>div>a')
                                    const link = linkElement[0]?.attribs['href'];
                                    if (link) {
                                        const title = text(linkElement).trim()
                                        const userElement = $('.cs-source-content .text-ellipsis')
                                        const avatar = ``
                                        const author = text(userElement).trim()
                                        const detail = new Detail(`https://so.toutiao.com${link}`, title, author, avatar)
                                        sub.next(detail)
                                    }
                                } catch (e) {
                                    console.error(e)
                                }
                            })
                        }
                        await this.delay(Math.random() * 1000)
                    })
                }
                return of(link)
            }),
            mergeMap((link: any) => {
                this.total += 1;
                return from(this.publishDetail(link).catch(e => console.error(e)))
            })
        )
    }
    total: number = 0;
    async handler(page: Page | HTTPResponse | HTTPRequest, sub: Subscriber<any>) {
        if (this.isPage(page)) {
            await page.waitForSelector('.s-result-list').catch(e => sub.error(e))
            const content = await page.content()
            const $ = this.load(content);
            const links = $('.s-result-list .result-content')
            const end = links.length - 1;
            links.map((index, item) => {
                try {
                    if (index === end) {
                        return;
                    }
                    const $ = load(item)
                    // #s-dom-8660d6d0 > div > div > div > div.cs-view.pad-bottom-3.cs-view-block.cs-text.align-items-center.cs-header > div > a
                    const linkElement = $('.cs-header>div>a')
                    const link = linkElement[0]?.attribs['href'];
                    if (link) {
                        const title = text(linkElement).trim()
                        const userElement = $('.cs-source-content .text-ellipsis')
                        const avatar = ``
                        const author = text(userElement).trim()
                        const detail = new Detail(`https://so.toutiao.com${link}`, title, author, avatar)
                        sub.next(detail)
                    }
                } catch (e) {
                    console.error(e)
                }
            })
            const children = $('div.cs-pagination').children()
            const endIndex = children.length - 1;
            let isEnd = false;
            let lastUrl: string = ``;
            let current: number = 0;
            children.map((index, child) => {
                if (child.attribs['class'].includes('text-darker-inverse')) {
                    current = index;
                }
                if (index === endIndex) {
                    const t = text([child])
                    if (t.includes('下一页')) {
                        isEnd = false;
                    } else {
                        isEnd = true
                    }
                }
            })
            if (!isEnd) {
                children.map((index, child) => {
                    if (child.tagName === 'a') {
                        const href = child.attribs['href'];
                        if (index !== endIndex && current < index) {
                            sub.next(new Link(decodeURI(href)))
                        }
                        if (index === endIndex - 1) {
                            // 最后 一个 链接
                            lastUrl = decodeURI(href)
                        }
                    }
                })
                if (!isEnd && lastUrl) {
                    await page.goto(lastUrl)
                    await this.handler(page, sub)
                }
            }
        } else if (this.isHttpResponse(page)) { }
        else {
            page.continue()
        }
        await this.delay(Math.random() * 1000)
    }
    async createKey(href: string): Promise<string> {
        const url = new URL(href)
        const jumpUrl = url.searchParams.get('url')
        if (jumpUrl) {
            const jumpKey = this.getJumpUrlKey(jumpUrl)
            const key = this.md5(jumpKey)
            return key;
        }
        return ''
    }

    getJumpUrlKey(href: string): string {
        const url = new URL(href)
        const key = url.hostname + url.pathname;
        return this.md5(key)
    }
}