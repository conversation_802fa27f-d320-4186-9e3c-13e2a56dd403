import { text } from "cheer<PERSON>";
import { H<PERSON><PERSON>equest, HTTPResponse, Page } from "@nger/puppeteer";
import { mergeMap, Observable, of, Subscriber } from "rxjs";
import { Message } from "../../rabbitmq";
import { BrowserRequest, <PERSON> } from "../../spider";
import { Link } from "../types";
export interface WeiboSearchPayload {

}
export class WechatSearchSpider extends Spider {
    async dead<T = any>(msg: Message<any>): Promise<void | Observable<T>> {
    }
    platform: string = 'wechat';
    type: string = 'search';
    async run(action: Message<any>): Promise<Observable<any> | void> {
        const { payload, type } = action;
        const keyword = encodeURIComponent(payload.keyword)
        const url = `https://weixin.sogou.com/weixin?p=01030402&query=${keyword}&type=2&ie=utf8`
        const user = await this.getUser()
        if (user) {
            const cookies = user.cookies
            const headers = {};
            const req = new BrowserRequest(url, cookies, headers)
            return this.openInBrowser(req, this.handler.bind(this)).pipe(
                mergeMap(link => {
                    if (Link.isLink(link)) {
                        const req = new BrowserRequest(link.href, cookies, headers)
                        return this.openInBrowser(req, async (page, sub) => {
                            if (this.isPage(page)) {
                                await this.delay(1000 * 60 * 3)
                            }
                        })
                    }
                    return of(link)
                }, 1)
            )
        }else{
            return 
        }
    }
    async handler(page: Page | HTTPResponse | HTTPRequest, sub: Subscriber<any>): Promise<void> {
        if (this.isPage(page)) {
            const content = await page.content()
            const $ = this.load(content);
            const children = $('#pagebar_container').children()
            const endIndex = children.length - 1;
            let isEnd = false;
            let lastUrl: string = ``;
            let current: number = 0;
            children.map((index, child) => {
                if (child.tagName === 'span') {
                    current = index;
                }
                if (index === endIndex) {
                    const t = text([child])
                    if (t.includes('下一页')) {
                        isEnd = false;
                    } else {
                        isEnd = true
                    }
                }
            })
            if (!isEnd) {
                children.map((index, child) => {
                    if (child.tagName === 'a') {
                        const href = child.attribs['href'];
                        if (index !== endIndex && current < index) {
                            sub.next(new Link(`https://weixin.sogou.com/weixin${decodeURI(href)}`))
                        }
                        if (index === endIndex - 1) {
                            // 最后 一个 链接
                            lastUrl = decodeURI(href)
                        }
                    }
                })
                if (!isEnd && lastUrl) {
                    await page.goto(lastUrl)
                    await this.handler(page, sub)
                }
            }
        } else if (this.isHttpResponse(page)) { }
        else {
            page.continue()
        }
        await this.delay(Math.random() * 1000)
    }
    async createKey(url: string): Promise<string> {
        return ``
    }
}