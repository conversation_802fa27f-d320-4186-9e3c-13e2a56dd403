import { load, text } from "cheerio";
import { H<PERSON><PERSON><PERSON><PERSON>, HTTPResponse, Page, User } from "@nger/puppeteer";
import { bufferTime, catchError, concat, concatAll, concatMap, filter, from, merge, mergeMap, Observable, of, Subscriber, takeLast, tap, zip } from "rxjs";
import { SpiderUser } from "../../../entities/spider_user";
import { Message } from "../../rabbitmq";
import { BrowserRequest, Spider } from "../../spider";
import { ListTask } from "../list.task";
import { Detail, DetailLink, Link } from "../types";
import { ARTICLE_DETAIL_KEY, ARTICLE_DETAIL_TOTAL, ARTICLE_DETAIL_V } from "../const";
import { DetailTask } from "../detail.task";
import { Error400, UserLogoutError } from "../error";
import { writeFile } from "fs/promises";
import { join } from "path";
import { APP_ROOT } from "@nger/core";
import { ensureDirSync } from "fs-extra";
import { SearchTask } from "../search.task";
import { SystemMessage } from "../../../v2/message";
export interface WeiboSearchPayload {

}
export class WeiboSearchSpider extends Spider {
    async dead<T = any>(msg: Message<any>): Promise<void | Observable<any>> {
        const task = this.injector.get(SearchTask)
        return from(task.publish(msg))
    }
    bf: string = ARTICLE_DETAIL_KEY
    v: number = ARTICLE_DETAIL_V
    total: number = ARTICLE_DETAIL_TOTAL;
    platform: string = 'weibo';
    type: string = 'search';
    get list() {
        return this.injector.get(ListTask)
    }
    private formatDate(startDate: Date) {
        return this.format(startDate, 'yyyy-MM-dd-hh')
    }
    format(date: Date, fmt: string) {  // author: meizz 
        const o: any = {
            "M+": date.getMonth() + 1,  // 月份 
            "d+": date.getDate(),  // 日 
            "h+": date.getHours(),  // 小时 
            "m+": date.getMinutes(),  // 分 
            "s+": date.getSeconds(),  // 秒 
            "q+": Math.floor((date.getMonth() + 3) / 3),  // 季度 
            "S": date.getMilliseconds()  // 毫秒 
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (let k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    createUrl(keyword: string, startDate?: string, endDate?: string) {
        let url = `https://s.weibo.com/weibo?q=${encodeURIComponent(keyword)}&suball=1&Refer=g&typeall=1`
        if (startDate) {
            let timescope = `custom:${startDate}`
            if (endDate) {
                timescope += `:${endDate}`
            }
            url += `&timescope=${encodeURIComponent(timescope)}`
        } else {
            if (endDate) {
                let timescope = `custom:`
                timescope += `:${endDate}`
                url += `&timescope=${encodeURIComponent(timescope)}`
            }
        }
        return url;
    }
    get systemMessage() {
        return this.injector.get(SystemMessage)
    }
    async run(action: Message<any>): Promise<Observable<any> | void> {
        const { payload, type } = action;
        if (!payload.keyword) {
            return;
        }
        if (payload.keyword.length === 0) {
            return;
        }
        const keyword = payload.keyword
        const urls: string[] = [];
        const step = payload.step || 12;
        if (payload.time) {
            let [start, end] = payload.time;
            let startDate = new Date(start);
            const maxEndDate = new Date(end);
            if (step > 0) {
                let endDate = new Date(startDate.getTime() + step * 60 * 60 * 1000);
                while (endDate < maxEndDate) {
                    urls.push(this.createUrl(keyword, this.formatDate(startDate), this.formatDate(endDate)))
                    startDate = endDate;
                    endDate = new Date(startDate.getTime() + step * 60 * 60 * 1000);
                }
                urls.push(this.createUrl(keyword, this.formatDate(startDate), this.formatDate(endDate)))
            } else {
                urls.push(this.createUrl(keyword, this.formatDate(startDate), this.formatDate(maxEndDate)))
            }
        } else {
            urls.push(this.createUrl(keyword))
        }
        this.systemMessage.send({
            type: 'search',
            data: { urls, payload }
        }).subscribe();
        const listTask = this.injector.get(ListTask)
        await listTask.publishs(urls.map(url => {
            return { type: 'list', payload: { url, platform: this.platform, keyword: payload.keyword } }
        }));
        return;
    }
    async handler(page: Page | HTTPResponse | HTTPRequest, sub: Subscriber<any>, user: SpiderUser, hasPage: boolean = false, payload: any) {
        if (this.isPage(page)) {
            const url = page.url()
            if (url.startsWith('search_need_login')) {
                await this.weiboUserPool.destroy(user)
                throw new UserLogoutError()
            }
            const content = await page.content()
            if (content.includes('404 Page not found')) {
                throw new Error400();
            }
            const hash = this.md5(url);
            const root = this.injector.get(APP_ROOT);
            ensureDirSync(join(root, 'data', this.platform))
            await writeFile(join(root, 'data', this.platform, `${hash}`), content);
            const $ = this.load(content);
            const element = $('.loginBtn');
            if (element.length > 0) {
                await this.weiboUserPool.destroy(user);
                throw new UserLogoutError();
            }
            // #pl_feedlist_index > div:nth-child(2) > div:nth-child(1) > div > div.card-feed > div.content > div.from > a:nth-child(1)
            const links = $('#pl_feedlist_index .card-feed')
            const waitSend: DetailLink[] = [];
            links.map((index, item) => {
                try {
                    const $ = load(item);
                    const linkElement = $('div.content > .from > a');
                    const link = linkElement[0]?.attribs['href'];
                    const d = new DetailLink();
                    const url = this.parseUrl(link);
                    if (url) {
                        d.platform = this.platform;
                        d.href = url;
                        d.keyword = payload.keyword;
                        waitSend.push(d);
                    }
                } catch (e) {
                    // console.error(e)
                }
            });
            sub.next(waitSend);
            if (hasPage) {
                const moreList = $('ul[node-type="feed_list_page_morelist"] > li')
                moreList.map((index, el) => {
                    const cur = el.attribs['class'] && el.attribs['class'].includes('cur')
                    if (!cur) {
                        const $ = load(el)
                        const a = $('a')[0]
                        const href = a.attribs['href'];
                        const url = this.parseUrl(`https://s.weibo.com${href}`)!
                        sub.next(new Link(url))
                    }
                })
            }
            await this.delay(1000 * Math.random() * 30)
        } else if (this.isHttpResponse(page)) {
        }
        else {
            page.continue()
        }
    }
    parseUrl(url: string) {
        if (!url) {
            return;
        }
        if (url.startsWith('https')) {
            return url
        } else {
            return `https:${url}`
        }
    }
    createKey(href: string): string {
        const url = new URL(href)
        const key = url.hostname + url.pathname;
        return this.md5(key)
    }
}