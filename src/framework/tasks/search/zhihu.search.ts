import axios from "axios";
import { text } from "cheerio";
import { Page } from "@nger/puppeteer";
import { from, mergeMap, Observable, Subscriber } from "rxjs";
import { Message } from "../../rabbitmq";
import { BrowserRequest, Spider } from "../../spider";
import { Article } from "../types";
export interface WeiboSearchPayload {

}
export class ZhihuSearchSpider extends Spider {
    async dead<T = any>(msg: Message<any>): Promise<void | Observable<T>> {}
    platform: string = 'zhihu';
    type: string = 'search';
    async scrollDown(page: Page) {
        await page.evaluate(() => {
            return new Promise<void>((resolve, reject) => {
                let totalHeight = 0;
                const distance: number = 100;
                const timer = setInterval(() => {
                    const scrollHeight = document.body.scrollHeight;
                    window.scrollBy(0, distance)
                    totalHeight += distance;
                    if (totalHeight >= scrollHeight) {
                        clearInterval(timer);
                        resolve();
                    }
                }, 50);
            })
        })
    }
    async run(action: Message<any>): Promise<Observable<any> | void> {
        const { payload } = action;
        const keyword = encodeURIComponent(payload.keyword)
        const url = `https://www.zhihu.com/search?q=${keyword}&type=content`;
        const user = await this.zhihuUserPool.acquire();
        if (user) {
            this.zhihuUserPool.release(user)
            const headers = {};
            const cookies: any[] = user.cookies;
            const req = new BrowserRequest(url, cookies, headers)
            return this.openInBrowser(req, async (page, sub) => {
                if (this.isHttpResponse(page)) { 

                } else if (this.isPage(page)) {
                    let isEnd = false;
                    while (!isEnd) {
                        await this.scrollDown(page);
                        const content = await page.content();
                        const $ = this.load(content);
                        const moreTip = $('#SearchMain > div > div > div > div > div.css-10r4b1g');
                        const moreTipText = text(moreTip);
                        if (moreTipText.includes('没有更多了')) {
                            isEnd = true;
                        }
                    }
                }
                else {
                    if (page.url().startsWith('https://www.zhihu.com/api/v4/search_v3')) {
                        const url = page.url();
                        const headers = page.headers();
                        return axios.get(url, {
                            headers
                        }).then(async res => {
                            const { data } = res.data;
                            Promise.all(data.map((item: any) => this.handleData(item, sub)))
                            page.respond({
                                status: res.status,
                                headers: res.headers,
                                contentType: 'application/json',
                                body: JSON.stringify(res.data)
                            })
                        })
                    } else {
                        page.continue()
                    }
                }
                await this.delay(1000 * 60 * 3)
            })
        }
    }
    async handleAnswer(data: any, sub: Subscriber<any>) {
        const { author, thumbnail_info, question } = data;
        const thumbnails = thumbnail_info.thumbnails.map((thumb: any) => thumb.url)
        const article = new Article()
        article.url = data.url;
        article.updated_time = data.updated_time;
        article.voteup_count = data.voteup_count;
        article.title = data.title;
        article.content = data.content;
        article.excerpt = data.excerpt;
        article.created_time = data.created_time;
        article.comment_count = data.comment_count;
        article.id = data.id;
        article.platform = this.platform;
        article.thumbnails = thumbnails;
        article.author = author.name;
        article.author_avatar = author.avatar_url;
        article.author_follower_count = author.follower_count;
        article.author_gender = author.gender;
        article.author_id = author.id;
        article.author_url = author.url;
        article.author_user_type = author.user_type;
        article.author_voteup_count = author.voteup_count;
        article.article_type = 'answer';
        article.question_name = question.name;
        article.question_url = question.url;
        article.question_id = question.id;
        sub.next(article)
    }
    handleArticle(data: any, sub: Subscriber<any>) {
        const { author, thumbnail_info } = data;
        const thumbnails = thumbnail_info.thumbnails.map((thumb: any) => thumb.url);
        const article = new Article();
        article.url = data.url;
        article.updated_time = data.updated_time;
        article.voteup_count = data.voteup_count;
        article.title = data.title;
        article.content = data.content;
        article.excerpt = data.excerpt;
        article.created_time = data.created_time;
        article.comment_count = data.comment_count;
        article.id = data.id;
        article.platform = this.platform;
        article.thumbnails = thumbnails;
        article.author = author.name;
        article.author_avatar = author.avatar_url;
        article.author_follower_count = author.follower_count;
        article.author_gender = author.gender;
        article.author_id = author.id;
        article.author_url = author.url;
        article.author_user_type = author.user_type;
        article.author_voteup_count = author.voteup_count;
        sub.next(article)
    }
    handleHotTiming(data: any, sub: Subscriber<any>) {
        const { content_items } = data;
        content_items.map((item: any) => {
            const type = item.type;
            switch (type) {
                case 'article':
                    this.handleArticle(data.object, sub)
                    break;
                case 'answer':
                    this.handleAnswer(data, sub)
                    break;
                default:
                    console.log(data)
                    break;
            }
        });
    }
    handleSearchResult(data: any, sub: Subscriber<any>) {
        const { type } = data;
        switch (type) {
            case 'article':
                this.handleArticle(data, sub);
                break;
            case 'answer':
                this.handleAnswer(data, sub);
                break;
            default:
                console.log(data);
                break;
        }
    }

    async handleData(data: any, sub: Subscriber<any>) {
        const { type } = data;
        switch (type) {
            case 'search_result':
                this.handleSearchResult(data.object, sub);
                break;
            case 'hot_timing':
                this.handleHotTiming(data.object, sub);
                break;
            case 'answer':
                this.handleAnswer(data, sub);
                break;
            default:
                console.log(data);
                break;
        }
        await this.delay(1000 * 60)
    }
    async requestSearch(url: string, headers: any, sub: Subscriber<any>): Promise<any> {
        return axios.get(url, {
            headers
        }).then(async res => {
            const { paging, data } = res.data;
            Promise.all(data.map((item: any) => this.handleData(item, sub)));
            return {
                status: res.status,
                headers: res.headers,
                contentType: 'application/json',
                body: JSON.stringify(res.data)
            }
        })
    }
    createKey(url: string): string {
        return this.md5(url);
    }
}
