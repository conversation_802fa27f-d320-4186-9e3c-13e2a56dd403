import { Injector } from "@nger/core";
import { createMq } from "./util";
import { Action, Message, MqService } from '../rabbitmq'
import { CoreSpider } from "../spider/core";
import { Error400, Error414, UserLogoutError } from "./error";
import { SystemMessage } from '../../v2/message';

export abstract class Task {
    mq!: MqService;
    prefetch: number = 1;
    get systemMessage() {
        return this.injector.get(SystemMessage)
    }
    constructor(public injector: Injector, public name: string) { }
    async onInit() {
        this.mq = await createMq(this.injector, this.name, this.prefetch)
    }
    async consumer(): Promise<any> {
        await this.onInit()
        const consumer = await this.mq.consumer()
        consumer.pipe().subscribe((res) => {
            this.handler(res, 'consumer')
        })
    }
    async dead(): Promise<any> {
        await this.onInit()
        const dead = await this.mq.dead()
        dead.pipe().subscribe((res) => {
            this.getDead(res, 'dead')
        })
    }
    async publish(msg: Message, ttl: number = 1000 * 60 * 30) {
        const sender = await createMq(this.injector, this.name)
        await sender.publish(msg, ttl)
    }
    async publishs(msg: Message[], ttl: number = 1000 * 60 * 30) {
        const sender = await createMq(this.injector, this.name)
        return sender.publishs(msg, ttl)
    }
    async getDead(action: Action<any>, type: string) {
        const spiders = this.injector.getMulti(CoreSpider)
        action.from = type;
        if (action.data) {
            const { payload } = action.data;
            const platform = payload.platform;
            const spider = spiders.find(s => s.type === this.name && s.platform === platform)
            if (spider) {
                await spider.dead(action.data).then(res => {
                    if (res) {
                        res.subscribe({
                            error: (e) => {
                                action.ack()
                            },
                            complete: () => action.ack()
                        })
                        return;
                    } else {
                        action.ack()
                    }
                })
                return;
            }
        }
        action.reject()
    }
    handler(action: Action<any>, type: string) {
        const spiders = this.injector.getMulti(CoreSpider)
        action.from = type;
        const data = action.data;
        if (data) {
            const { payload } = data
            const { open_schedule } = payload;
            const platform = payload.platform
            const spider = spiders.find(s => s.type === this.name && s.platform === platform)
            if (spider) {
                spider.run(data).then(res => {
                    if (res) {
                        res.pipe().subscribe({
                            error: (e) => {
                                if (e instanceof UserLogoutError) {
                                    // action.reject();
                                    setTimeout(()=>{
                                        action.noAck(true, true)
                                    },1000 * Math.random() * 30)
                                }
                                else if (e instanceof Error414) {
                                    this.systemMessage.sendText('414', {
                                        title: '414',
                                        content: '系统繁忙，采集频率过高'
                                    }).subscribe();
                                    setTimeout(()=>{
                                        action.noAck(true, true)
                                    },1000 * Math.random() * 30)
                                } else if (e instanceof Error400) {
                                    setTimeout(()=>{
                                        action.ack();
                                    },1000 * Math.random() * 3)
                                } else {
                                    const dataStr = JSON.stringify(data);
                                    this.systemMessage.sendText(`error`, {
                                        title: e.message,
                                        content: dataStr,
                                    }).subscribe();
                                    setTimeout(()=>{
                                        action.noAck(true, true)
                                    },1000 * Math.random() * 30)
                                }
                            },
                            complete: () => {
                                action.ack()
                            }
                        })
                    } else {
                        action.ack()
                    }
                }).catch(e => {
                    if (e instanceof UserLogoutError) {
                        setTimeout(()=>{
                            action.noAck(true, true)
                        },1000 * Math.random() * 30)
                    }
                    else if (e instanceof Error414) {
                        this.systemMessage.sendText(`414`, {
                            title: '414',
                            content: '系统繁忙，采集频率过高'
                        }).subscribe();
                        setTimeout(()=>{
                            action.noAck(true, true)
                        },1000 * Math.random() * 30)
                    } else if (e instanceof Error400) {
                        this.systemMessage.sendText(`400`, {
                            title: '400',
                            content: '资源已删除或不存在'
                        }).subscribe();
                        action.ack();
                    } else {
                        console.log(e);
                        const dataStr = JSON.stringify(data);
                        this.systemMessage.sendText(`error`, {
                            title: e.message,
                            content: dataStr,
                        }).subscribe();
                        setTimeout(()=>{
                            action.noAck(true, true)
                        },1000 * Math.random() * 30)
                    }
                })
                return;
            }
        }
        if (action.data) {
            const { payload } = action.data
            const platform = payload.platform
            console.error(`reject data error or not found spider ${platform} ${this.name}`)
            action.ack()
            return;
        }
        action.ack()
    }
}
