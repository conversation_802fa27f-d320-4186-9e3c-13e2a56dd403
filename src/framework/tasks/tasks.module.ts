import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Inject<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>idthProvider } from "@nger/core";
import { SearchController } from "./search.controller";
import { SearchTask } from "./search.task";
import { searchProvicers } from "./search";
import { Task } from "./task";
import { DetailTask } from "./detail.task";
import { ListTask } from "./list.task";
import { AccountTask } from "./account.task";
import { CommentTask } from "./comment.task";
import { accountProvicers } from "./account";
import { WeiboAccountSearch } from "./account-search/weibo.account-search";
import { ZhihuAccountSearch } from "./account-search/zhihu.account-search";
import { TopicTask } from "./topic.task";
import { detailProvicers } from "./detail";
import { HotTask } from "./hot.task";
import { UserService } from "./user.service";
import { NlpService } from "./nlp.service";
import { LikeTask } from "./like.task";
import { RepostTimelineTask } from "./repostTimeline.task";
import { commentProvicers } from "./comment";
import { likeProvicers } from "./like";
import { repostTimelineProvicers } from "./repost-timeline";
import { listProvicers } from "./list";
import { pageProvicers } from "./page";
import { PageTask } from "./page.task";

@Module({
    providers: [
        ...searchProvicers,
        ...accountProvicers,
        ...detailProvicers,
        ...commentProvicers,
        ...likeProvicers,
        ...repostTimelineProvicers,
        ...listProvicers,
        ...pageProvicers,
        NlpService,
        SearchTask,
        DetailTask,
        ListTask,
        AccountTask,
        CommentTask,
        PageTask,
        LikeTask,
        RepostTimelineTask,
        TopicTask,
        HotTask,
        UserService,
        {
            provide: WeiboAccountSearch,
            useFactory: (injector: Injector) => new WeiboAccountSearch(injector),
            deps: [Injector]
        },
        {
            provide: ZhihuAccountSearch,
            useFactory: (injector: Injector) => new ZhihuAccountSearch(injector),
            deps: [Injector]
        },
    ],
    imports: [],
    controllers: [
        SearchController
    ]
})
export class TasksModule {
    static forRoot(): ModuleWidthProvider<TasksModule> {
        return {
            module: TasksModule,
            providers: [
                {
                    provide: Task,
                    useExisting: SearchTask,
                    multi: true
                },
                {
                    provide: Task,
                    useExisting: DetailTask,
                    multi: true
                },
                {
                    provide: Task,
                    useExisting: HotTask,
                    multi: true
                },
                {
                    provide: Task,
                    useExisting: ListTask,
                    multi: true
                },
                {
                    provide: Task,
                    useExisting: PageTask,
                    multi: true
                },
                {
                    provide: Task,
                    useExisting: AccountTask,
                    multi: true
                },
                {
                    provide: Task,
                    useExisting: CommentTask,
                    multi: true
                },
                {
                    provide: Task,
                    useExisting: TopicTask,
                    multi: true
                },
                {
                    provide: Task,
                    useExisting: LikeTask,
                    multi: true
                },
                {
                    provide: Task,
                    useExisting: RepostTimelineTask,
                    multi: true
                }
            ]
        }
    }
}
