export class Link {
    href: string;
    type: string = 'link'
    constructor(href: string) {
        this.href = href;
    }
    static isLink(val: any): val is Link {
        return val && val.type === 'link'
    }
}
export class DetailLink {
    type: string = 'detail'
    href!: string;
    platform!: string;
    keyword!: string;
    static is(val: any): val is DetailLink {
        return val && val.type === 'detail'
    }
}
export class Detail {
    href: string;
    avatar: string;
    type: string = 'detail'
    title: string;
    author: string;
    constructor(href: string, title: string, author: string, avatar: string) {
        this.href = href;
        this.title = title;
        this.author = author;
        this.avatar = avatar;
    }
    static isDetail(val: any): val is Detail {
        return val && val.type === 'detail'
    }
}

export class Article {
    type: 'article' = 'article'

    // 平台
    platform!: string;
    // 链接 唯一
    url!: string;
    // 更新时间
    updated_time!: number;
    // 创建时间
    created_time!: number;
    // 分享
    share_count!: number;
    share: any[] = [];
    // 赞同
    voteup_count!: number;
    voteup: any[] = []
    // 评论
    comment_count!: number;
    comment: any[] = [];
    // 文章id
    id!: string;
    // 标题
    title!: string;
    // 内容
    content!: string;
    // 简介
    excerpt!: string;
    // 图片
    thumbnails!: string[];
    // 作者
    author!: string;
    // 作者头像
    author_avatar!: string;
    // 作者性别
    author_gender!: number;
    // 作者id
    author_id!: string;
    // 作者粉丝
    author_follower_count!: number;
    // 作者链接
    author_url!: string;
    // 点赞
    author_voteup_count!: number;
    // 作者类型
    author_type!: string;
    // 作者性质
    author_user_type!: string;

    // 文章类型
    article_type: string = 'article'
    // 问题
    question_name?: string;
    // 问题链接
    question_url?: string;
    // 问题id
    question_id?: string;

    is_plus!: boolean;
    is_plus_content!: boolean;

    static is(val: any): val is Article {
        return val && val.type === 'article'
    }

}