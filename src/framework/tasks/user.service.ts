import { Injectable, Injector } from "@nger/core";
import { Db } from "@nger/typeorm";
import { SpiderUser } from "../../entities/spider_user";


@Injectable()
export class UserService {
    get db() {
        return this.injector.get(Db)
    }
    constructor(private injector: Injector) { }
    users: any[] = [];
    user: any;
    async getRandomUser(platform: string): Promise<SpiderUser | undefined> {
        const users = await this.db.manager.find(SpiderUser, {
            where: { platform: platform, status: 2 }
        })
        if (users.length === 0) {
            return undefined;
        }
        const userIndex = Math.floor(Math.random() * users.length)
        const user = users[userIndex]
        return user;
    }
    async getUser(platform: string): Promise<SpiderUser | undefined> {
        if (this.user) return this.user;
        if (this.users.length <= 0) {
            this.users = await this.db.manager.find(SpiderUser, {
                where: { platform: platform, status: 2 }
            })
            if (this.users.length === 0) {
                return undefined;
            }
        }
        const userIndex = Math.floor(Math.random() * this.users.length)
        this.user = this.users[userIndex]
        return this.user;
    }
    async logout(user: SpiderUser) {
        await this.db.manager.update(SpiderUser, user.id, { status: 1 }).catch(e => console.log(e.message))
        this.user = undefined;
        this.users = [];
        await this.getUser(user.platform)
    }
}