import { Injector } from "@nger/core"
import { MqFactory } from "../rabbitmq"


export function createMq(injector: Injector, name: string, prefetch: number = 1) {
    const mqFactory = injector.get(MqFactory)
    return mqFactory.create({
        exchange: `${name}.exchange`,
        deadExchange: `dead.${name}.exchange`,
        routingKey: `${name}.routingKey`,
        deadRoutingKey: `dead.${name}.routingKey`,
        queue: `${name}.queue`,
        deadQueue: `dead.${name}.queue`,
        prefetch
    })
}
