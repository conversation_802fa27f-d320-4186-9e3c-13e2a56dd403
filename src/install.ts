import https from 'https';
import createHttpsProxyAgent from 'https-proxy-agent';
import ProgressBar from 'progress';
import { getProxyForUrl } from 'proxy-from-env';
import { PuppeteerNode } from 'puppeteer-core';
import URL from 'url';
import { getConfiguration } from './getConfiguration';
const PUPPETEER_REVISIONS = Object.freeze({
    chromium: '1108766',
    firefox: 'latest',
});
/**
 * @internal
 */
const supportedProducts: any = {
    chrome: 'Chromium',
    firefox: 'Firefox Nightly',
};
/**
 * @internal
 */
export async function downloadBrowser() {
    const configuration = getConfiguration();
    const PuppeteerNodeNew: any = PuppeteerNode
    const puppeteer = new PuppeteerNodeNew({ configuration, isPuppeteerCore: false });
    const product = configuration.defaultProduct;
    const browserFetcher = puppeteer.createBrowserFetcher();
    let revision = configuration.browserRevision;
    if (!revision) {
        switch (product) {
            case 'chrome':
                revision = PUPPETEER_REVISIONS.chromium;
                break;
            case 'firefox':
                revision = PUPPETEER_REVISIONS.firefox;
                revision = await getFirefoxNightlyVersion();
                break;
        }
    }
    console.log(`fetch binary: ${revision}`)
    await fetchBinary(revision);
    function fetchBinary(revision: any) {
        const revisionInfo = browserFetcher.revisionInfo(revision);
        // Do nothing if the revision is already downloaded.
        if (revisionInfo.local) {
            logPolitely(`${supportedProducts[product]} is already in ${revisionInfo.folderPath}; skipping download.`);
            return;
        }
        // Override current environment proxy settings with npm configuration, if any.
        const NPM_HTTPS_PROXY = process.env['npm_config_https_proxy'] || process.env['npm_config_proxy'];
        const NPM_HTTP_PROXY = process.env['npm_config_http_proxy'] || process.env['npm_config_proxy'];
        const NPM_NO_PROXY = process.env['npm_config_no_proxy'];
        if (NPM_HTTPS_PROXY) {
            process.env['HTTPS_PROXY'] = NPM_HTTPS_PROXY;
        }
        if (NPM_HTTP_PROXY) {
            process.env['HTTP_PROXY'] = NPM_HTTP_PROXY;
        }
        if (NPM_NO_PROXY) {
            process.env['NO_PROXY'] = NPM_NO_PROXY;
        }
        function onSuccess(localRevisions: any) {
            logPolitely(`${supportedProducts[product]} (${revisionInfo.revision}) downloaded to ${revisionInfo.folderPath}`);
            localRevisions = localRevisions.filter((revision: any) => {
                return revision !== revisionInfo.revision;
            });
            const cleanupOldVersions = localRevisions.map((revision: any) => {
                return browserFetcher.remove(revision);
            });
            Promise.all([...cleanupOldVersions]);
        }
        function onError(error: any) {
            console.error(`ERROR: Failed to set up ${supportedProducts[product]} r${revision}! Set "PUPPETEER_SKIP_DOWNLOAD" env variable to skip download.`);
            console.error(error);
            process.exit(1);
        }
        let progressBar: any = null;
        let lastDownloadedBytes = 0;
        function onProgress(downloadedBytes: any, totalBytes: any) {
            if (!progressBar) {
                progressBar = new ProgressBar(`Downloading ${supportedProducts[product]} r${revision} - ${toMegabytes(totalBytes)} [:bar] :percent :etas `, {
                    complete: '=',
                    incomplete: ' ',
                    width: 20,
                    total: totalBytes,
                });
            }
            const delta = downloadedBytes - lastDownloadedBytes;
            lastDownloadedBytes = downloadedBytes;
            progressBar.tick(delta);
        }
        return browserFetcher
            .download(revisionInfo.revision, onProgress)
            .then(() => {
                return browserFetcher.localRevisions();
            })
            .then(onSuccess)
            .catch(onError);
    }
    function toMegabytes(bytes: any) {
        const mb = bytes / 1024 / 1024;
        return `${Math.round(mb * 10) / 10} Mb`;
    }
    async function getFirefoxNightlyVersion() {
        const firefoxVersionsUrl = 'https://product-details.mozilla.org/1.0/firefox_versions.json';
        const proxyURL = getProxyForUrl(firefoxVersionsUrl);
        const requestOptions: any = {};
        if (proxyURL) {
            const parsedProxyURL = URL.parse(proxyURL);
            const proxyOptions = {
                ...parsedProxyURL,
                secureProxy: parsedProxyURL.protocol === 'https:',
            };
            requestOptions.agent = createHttpsProxyAgent(proxyOptions);
            requestOptions.rejectUnauthorized = false;
        }
        const promise = new Promise((resolve, reject) => {
            let data = '';
            logPolitely(`Requesting latest Firefox Nightly version from ${firefoxVersionsUrl}`);
            https
                .get(firefoxVersionsUrl, requestOptions, r => {
                    if (r.statusCode && r.statusCode >= 400) {
                        return reject(new Error(`Got status code ${r.statusCode}`));
                    }
                    r.on('data', chunk => {
                        data += chunk;
                    });
                    r.on('end', () => {
                        try {
                            const versions = JSON.parse(data);
                            return resolve(versions.FIREFOX_NIGHTLY);
                        }
                        catch {
                            return reject(new Error('Firefox version not found'));
                        }
                    });
                })
                .on('error', reject);
        });
        return promise;
    }
}
/**
 * @internal
 */
function logPolitely(toBeLogged: any) {
    const logLevel = process.env['npm_config_loglevel'] || '';
    const logLevelDisplay = ['silent', 'error', 'warn'].indexOf(logLevel) > -1;
    // eslint-disable-next-line no-console
    if (!logLevelDisplay) {
        console.log(toBeLogged);
    }
}

downloadBrowser()