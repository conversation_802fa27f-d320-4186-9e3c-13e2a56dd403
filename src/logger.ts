import { InjectionToken, Injector, StaticProvider } from "@nger/core";

export const LOGGER_URL = new InjectionToken<string>(`LOGGER URL`)
export const LOGGER_LEVEL = new InjectionToken<string>(`LOGGER LEVEL`)
export const LOGGER_TAGS = new InjectionToken<string[]>(`LOGGER TAGS`)

export abstract class Logger {
    abstract log(level: string, message: string, fields: unknown): void;
    abstract debug(message: string, fields: unknown): void;
    abstract info(message: string, fields: unknown): void;
    abstract warn(message: string, fields: unknown): void;
    abstract error(message: string, fields: unknown): void;
    abstract fatal(message: string, fields: unknown): void;
}
/**
 * logstash logger
 */
export const loggerProviders: StaticProvider<any>[] = [{
    provide: LOGGER_URL,
    useValue: process.env.LOGSTASH_URL
}, {
    provide: LOGGER_TAGS,
    useValue: ['production', 'api']
}, {
    provide: LOGGER_LEVEL,
    useValue: 'info'
}, {
    provide: Logger,
    useFactory: (injector: Injector) => {
        const tags = injector.get(LOGGER_TAGS)
        const url = injector.get(LOGGER_URL)
        const level = injector.get(LOGGER_URL)
        require('logstash')(url, tags, level)
    },
    deps: [
        Injector
    ]
}]


