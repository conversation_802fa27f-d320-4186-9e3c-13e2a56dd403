#!/usr/bin/env node
import cluster from 'cluster';
import os from 'os'
import "reflect-metadata";
import { Module, platformCore, APP_ROOT } from '@nger/core';
import { ZookeeperModule } from '@nger/zookeeper';
import { TypeormModule } from '@nger/typeorm';
import { UtilsModule } from '@nger/utils';
import { RabbitMqModule, RabbitmqStarter } from '@nger/rabbitmq';
import { WeiboNodeModule } from "./weibo-node.module";

@Module({
    imports: [
        TypeormModule.forRoot(),
        ZookeeperModule.forRoot(),
        RabbitMqModule.forRoot(),
        UtilsModule,
        WeiboNodeModule,
    ]
})
export class AppNodeModule { }
if (cluster.isPrimary) {
    const cpus = os.cpus().length
    for (let i = 0; i < cpus; i++) {
        cluster.fork()
    }
    cluster.on("exit", (worker, code, signal) => {
        console.log("工作进程" + worker.process.pid + "已退出")
    });
    console.log(`主节点启动成功，共计${cpus}个爬虫节点`)
} else {
    platformCore([{
        provide: APP_ROOT,
        useValue: process.cwd()
    }]).bootstrap(AppNodeModule).then(async injector => {
        try {
            const rabbitmqStarter = injector.get(RabbitmqStarter)
            await rabbitmqStarter.start(true);
            console.log(`爬虫节点${cluster.worker?.process.pid}启动成功`)
        } catch (e: any) {
            console.log(e.message)
        }
    });
}
