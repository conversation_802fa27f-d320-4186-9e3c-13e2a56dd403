#!/usr/bin/env node
const dotenv = require('dotenv')
dotenv.config()
import "reflect-metadata";
process.env.TZ = 'Asia/Shanghai';
import { Module, platformCore, APP_ROOT } from '@nger/core';
import { HttpModule } from '@nger/http';
import { ZookeeperModule } from '@nger/zookeeper';
import { InstallModule } from '@nger/install';
import { TypeormModule } from '@nger/typeorm';
import { UtilsModule } from '@nger/utils';
import { WeiboModule } from './weibo.module';
import { RestModule } from '@nger/rest';
import { RabbitmqModule } from './framework/rabbitmq';
import { SirvModule } from '@nger/sirv';
import { Oauth2ServerModule } from '@nger/oauth2';
import { join } from "path";
import { RedisModule, TasksModule } from "./framework";
import { coreProviders } from "./tokens";
import EventEmitter from "events";
const root = join(__dirname, '..');
EventEmitter.setMaxListeners(1000)

@Module({
    imports: [
        TypeormModule.forRoot(),
        RabbitmqModule.forRoot(),
        ZookeeperModule.forRoot(),
        Oauth2ServerModule,
        InstallModule.forRoot(),
        SirvModule.forChild(root, '@nger/weibo', [
            'big', 'home', 'loginAdmin',
            'rest', 'gonggao', 'search',
            'search/weibo', 'search/zhihu',
            'search/toutiao', 'search/heimao',
            'search/wechat', 'search/qq-new',
            'nlp', 'bind-user', 'search/all', 
            'account/search', 'task'
        ]),
        UtilsModule,
        WeiboModule,
        HttpModule,
        RestModule,
        TasksModule,
        RedisModule
    ],
    providers: [
        ...coreProviders
    ]
})
export class AppModule { }

platformCore([{
    provide: APP_ROOT,
    useValue: process.cwd()
}]).bootstrap(AppModule)
    .then(injector => {
        console.info('恭喜您主节点启动成功')
    });
