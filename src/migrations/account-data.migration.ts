

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { join } from 'path';
import { parse } from 'node-xlsx'
import { SpilderAccount } from '../entities/spilder-account';
@Injectable()
export class Account************* extends Migration {
    constructor(injector: Injector) {
        super(`*************`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        const dir = join(__dirname, '../../static/account.xlsx');
        const list = parse(dir);
        if (list.length === 1) {
            const obj = list[0];
            const data = obj.data;
            await Promise.all(data.map(async (it: any, index) => {
                if (index === 0) {
                    return;
                }
                else {
                    const title = it[3];
                    const old = await this.db.manager.findOne(SpilderAccount, { where: { nickname: title } });
                    if (old) {
                        old.type = it[4];
                        old.verified_reason = it[6];
                        old.from = it[7];
                        old.url = it[8];
                        await this.db.manager.save(SpilderAccount, old);
                    }
                }
            }));
        }
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
    }
}
