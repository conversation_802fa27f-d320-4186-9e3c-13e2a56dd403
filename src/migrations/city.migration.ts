

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { WbCity } from '../entities/city';

@Injectable()
export class city1657731735130 extends Migration {
    constructor(injector: Injector) {
        super(`1657731735130`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(WbCity))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(WbCity))
    }
}
