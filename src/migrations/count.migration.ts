

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { CountArticle, CountArticleComment } from '../entities/count';

@Injectable()
export class count1662172062070 extends Migration {
    constructor(injector: Injector) {
        super(`1662172062070`);
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(CountArticle))
        await queryRunner.createTable(this.createTable(CountArticleComment))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.createTable(this.createTable(WbCity))
    }
}
