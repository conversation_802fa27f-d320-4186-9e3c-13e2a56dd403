

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { CrawlingUrl } from '../entities/crawling_url';

@Injectable()
export class CrawlingUrl1675300939131 extends Migration {
    constructor(injector: Injector) {
        super(`1675300939131`);
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(CrawlingUrl))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.createTable(this.createTable(WbCity))
    }
}
