

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { WbEventCategory } from '../entities/event-category';
@Injectable()
export class WbEventCategoryData1658900661284 extends Migration {
    constructor(injector: Injector) {
        super(`1658900661284`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        const list: WbEventCategory[] = [{
            title: '农、林、牧、渔业',
            code: '1'
        }, {
            title: '采矿业',
            code: '2'
        }, {
            title: '制造业',
            code: '3'
        }, {
            title: '电力、热力、燃气及水生产和供应业',
            code: '4'
        }, {
            title: '建筑业',
            code: '5'
        }, {
            title: '批发和零售业',
            code: '6'
        }, {
            title: '交通运输、仓储和邮政业',
            code: '7'
        }, {
            title: '住宿和餐饮业',
            code: '8'
        }, {
            title: '信息传输、软件和信息技术服务业',
            code: '9'
        }, {
            title:'金融业',
            code:'10'
        },{
            title: '房地产业',
            code: '11'
        }, {
            title: '租赁和商务服务业',
            code: '12'
        }, {
            title: '科学研究和技术服务业',
            code: '13'
        }, {
            title: '水利、环境和公共设施管理业',
            code: '14'
        }, {
            title: '居民服务、修理和其他服务业',
            code: '15'
        }, {
            title: '教育',
            code: '16'
        }, {
            title: '卫生和社会工作',
            code: '17'
        }, {
            title: '文化、体育和娱乐业',
            code: '18'
        }, {
            title: '公共管理、社会保障和社会组织',
            code: '19'
        }, {
            title: '国际组织',
            code: '20'
        }];
        await this.db.manager.save(WbEventCategory, list)
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
    }
}
