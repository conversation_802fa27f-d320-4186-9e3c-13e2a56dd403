

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { WbEventCategory } from '../entities/event-category';

@Injectable()
export class WbEventCategory1658900342571 extends Migration {
    constructor(injector: Injector) {
        super(`1658900342571`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(WbEventCategory))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(WbEventCategory))
    }
}
