

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { WbEventType } from '../entities/event-type';

@Injectable()
export class WbEventTypeData1658900374793 extends Migration {
    constructor(injector: Injector) {
        super(`1658900374793`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        const list: WbEventType[] = [{
            title: '社会民生类-自然灾害',
            code: 'A1'
        }, {
            title: '社会民生类-事故灾难',
            code: 'A2'
        }, {
            title: '社会民生类-公共卫生事件',
            code: 'A3'
        }, {
            title: '社会民生类-社会安全事件',
            code: 'A4'
        }, {
            title: '社会民生类-公共安全',
            code: 'A5'
        }, {
            title: '社会民生类-社会道德',
            code: 'A6'
        }, {
            title: '社会民生类-医患纠纷',
            code: 'A7'
        }, {
            title: '社会民生类-苦难生活',
            code: 'A8'
        }, {
            title: '社会民生类-慈善救助',
            code: 'A9'
        }, {
            title: '社会民生类-物价监管',
            code: 'A10'
        }, {
            title: '政府行为处置后果类事件',
            code: 'B'
        }, {
            title: '公职人员自身行为类事件',
            code: 'C'
        }];
        await this.db.manager.save(WbEventType, list)
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
    }
}
