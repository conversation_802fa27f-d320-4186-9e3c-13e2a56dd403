

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { WbEventType } from '../entities/event-type';

@Injectable()
export class WbEventType1658900303914 extends Migration {
    constructor(injector: Injector) {
        super(`1658900303914`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(WbEventType))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(WbEventType))
    }
}
