

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { WbEvent } from '../entities/event';

@Injectable()
export class event1657763465436 extends Migration {
    constructor(injector: Injector) {
        super(`1657763465436`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(WbEvent))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(WbEvent))
    }
}
