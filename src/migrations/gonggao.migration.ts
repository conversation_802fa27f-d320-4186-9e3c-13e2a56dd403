

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { Gonggao } from '../entities/gonggao';

@Injectable()
export class Gonggao1665026397032 extends Migration {
    constructor(injector: Injector) {
        super(`1665026397032`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(Gonggao))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(Gonggao))
    }
}
