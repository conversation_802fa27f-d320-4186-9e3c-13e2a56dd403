

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { Keyword } from '../entities/keyword';

@Injectable()
export class Keyword1664352047120 extends Migration {
    constructor(injector: Injector) {
        super(`1664352047120`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(Keyword))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(Keyword))
    }
}
