

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { WbTask } from '../entities/task';
import { SysMessageEntity } from '../entities/message';

@Injectable()
export class SysMessage1676478574476 extends Migration {
    constructor(injector: Injector) {
        super(`1676478574476`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SysMessageEntity))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SysMessageEntity))
    }
}
