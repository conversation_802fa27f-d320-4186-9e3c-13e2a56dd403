

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { WbPlatform } from '../entities/platform';

/**
 * 平台及平台用户
 */
@Injectable()
export class platform1658554213740 extends Migration {
    constructor(injector: Injector) {
        super(`1658554213740`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        const weibo = new WbPlatform();
        weibo.icon = 'icon-xinlangweibo';
        weibo.name = 'weibo';
        weibo.title = '微博';
        weibo.loginUrl = `https://weibo.com/`
        await this.db.manager.save(WbPlatform, weibo);
        const zhihu = new WbPlatform();
        zhihu.icon = 'icon-shejiaotubiao-46';
        zhihu.name = 'zhihu';
        zhihu.title = '知乎';
        zhihu.loginUrl = `https://www.zhihu.com/`
        await this.db.manager.save(WbPlatform, zhihu);
        const toutiao = new WbPlatform();
        toutiao.icon = 'icon-toutiaoyangshi';
        toutiao.name = 'toutiao';
        toutiao.title = '头条';
        toutiao.loginUrl = `https://www.toutiao.com/`
        await this.db.manager.save(WbPlatform, toutiao);
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
    }
}
