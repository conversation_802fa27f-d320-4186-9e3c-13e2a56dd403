

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { WbPlatform } from '../entities/platform';

@Injectable()
export class platform1658554174060 extends Migration {
    constructor(injector: Injector) {
        super(`1658554174060`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(WbPlatform))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(WbPlatform))
    }
}
