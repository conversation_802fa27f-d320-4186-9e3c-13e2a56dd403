

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { Setting } from '../entities/setting';

@Injectable()
export class Setting1665820822756 extends Migration {
    constructor(injector: Injector) {
        super(`1665820822756`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(Setting))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(Setting))
    }
}
