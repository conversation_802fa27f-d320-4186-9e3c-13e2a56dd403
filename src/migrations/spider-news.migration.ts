

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpiderNews } from '../entities/spider_news';

@Injectable()
export class SpiderNews1665754851939 extends Migration {
    constructor(injector: Injector) {
        super(`1665754851939`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpiderNews))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpiderNews))
    }
}
