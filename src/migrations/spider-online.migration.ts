

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpiderOnline } from '../entities/spider-online';

@Injectable()
export class SpiderOnline1663750157077 extends Migration {
    constructor(injector: Injector) {
        super(`1663750157077`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpiderOnline))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SpiderOnline))
    }
}
