

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpiderUrl } from '../entities/spider_url';

@Injectable()
export class SpiderUrl1663773389836 extends Migration {
    constructor(injector: Injector) {
        super(`1663773389836`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpiderUrl))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SpiderUrl))
    }
}
