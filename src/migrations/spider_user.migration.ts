

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpiderUser } from '../entities/spider_user';

@Injectable()
export class SpiderUser1664358244796 extends Migration {
    constructor(injector: Injector) {
        super(`1664358244796`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpiderUser))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SpiderUser))
    }
}
