

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpilderAccount } from '../entities/spilder-account';

@Injectable()
export class SpilderAccount************* extends Migration {
    constructor(injector: Injector) {
        super(`*************`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpilderAccount))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SpilderAccount))
    }
}
