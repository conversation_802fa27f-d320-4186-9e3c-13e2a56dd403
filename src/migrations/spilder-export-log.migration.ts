

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpilderExportLog } from '../entities/export-log';

@Injectable()
export class SpilderExportLog1659426673921 extends Migration {
    constructor(injector: Injector) {
        super(`1659426673921`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpilderExportLog))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SpilderExportLog))
    }
}
