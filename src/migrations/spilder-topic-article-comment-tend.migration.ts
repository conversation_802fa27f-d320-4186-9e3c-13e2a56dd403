

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpilderTopicArticleCommentTend } from '../entities/spilder-topic-article-comment-tend';

@Injectable()
export class SpilderTopicArticleCommentTend1659433214868 extends Migration {
    constructor(injector: Injector) {
        super(`1659433214868`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpilderTopicArticleCommentTend))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SpilderTopicArticleCommentTend))
    }
}
