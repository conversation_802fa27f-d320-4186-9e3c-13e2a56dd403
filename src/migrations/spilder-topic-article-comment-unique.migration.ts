

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpilderTopicArticleCommentUnique } from '../entities/spilder-topic-article-comment-unique';

@Injectable()
export class spilderTopicArticleCommentUnique1661934452670 extends Migration {
    constructor(injector: Injector) {
        super(`1661934452670`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpilderTopicArticleCommentUnique))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SpilderTopicArticleCommentUnique))
    }
}
