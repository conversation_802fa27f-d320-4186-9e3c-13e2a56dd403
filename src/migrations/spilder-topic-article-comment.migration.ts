

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpilderTopicArticleComment } from '../entities/spilder-topic-article-comment';

@Injectable()
export class spilderTopicArticleComment1658671163802 extends Migration {
    constructor(injector: Injector) {
        super(`1658671163802`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpilderTopicArticleComment))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SpilderTopicArticleComment))
    }
}
