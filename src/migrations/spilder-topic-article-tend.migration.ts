

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpilderTopicArticleTend } from '../entities/spilder-topic-article-tend';

@Injectable()
export class SpilderTopicArticleTend1659433177432 extends Migration {
    constructor(injector: Injector) {
        super(`1659433177432`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpilderTopicArticleTend))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SpilderTopicArticleTend))
    }
}
