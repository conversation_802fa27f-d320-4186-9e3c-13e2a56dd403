

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpilderTopicArticle } from '../entities/spilder-topic-article';

@Injectable()
export class spilderTopicArticle1658666059979 extends Migration {
    constructor(injector: Injector) {
        super(`1658666059979`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpilderTopicArticle))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SpilderTopicArticle))
    }
}
