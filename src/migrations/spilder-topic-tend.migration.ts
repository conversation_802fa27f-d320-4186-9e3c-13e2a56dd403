

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpilderTopicTend } from '../entities/spilder-topic-tend';

@Injectable()
export class SpilderTopicTend1659433128281 extends Migration {
    constructor(injector: Injector) {
        super(`1659433128281`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpilderTopicTend))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SpilderTopicTend))
    }
}
