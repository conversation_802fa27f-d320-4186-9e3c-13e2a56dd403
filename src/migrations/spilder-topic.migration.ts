

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { SpilderTopic } from '../entities/spilder-topic';

@Injectable()
export class spilderTopic1658645005229 extends Migration {
    constructor(injector: Injector) {
        super(`1658645005229`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(SpilderTopic))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(SpilderTopic))
    }
}
