

import { Migration } from '@nger/typeorm';
import { Injector, Injectable } from "@nger/core"
import { QueryRunner } from "typeorm"
import { WbTask } from '../entities/task';

@Injectable()
export class WbTask1659433453188 extends Migration {
    constructor(injector: Injector) {
        super(`1659433453188`)
        this.injector = injector;
    }
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(this.createTable(WbTask))
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable(this.createTable(WbTask))
    }
}
