#!/usr/bin/env node
import "reflect-metadata";
process.env.TZ = 'Asia/Shanghai';
import { Module, platformCore, APP_ROOT } from '@nger/core';
import { ZookeeperModule } from '@nger/zookeeper';
import { TypeormModule } from '@nger/typeorm';
import { UtilsModule } from '@nger/utils';
import { RabbitmqModule } from './framework/index';
import { WeiboNodeModule } from "./weibo-node.module";
import { coreProviders } from "./tokens";

@Module({
    imports: [
        TypeormModule.forRoot(),
        ZookeeperModule.forRoot(),
        RabbitmqModule.forRoot(),
        UtilsModule,
        WeiboNodeModule,
    ],
    providers: [
        ...coreProviders
    ]
})
export class AppModule { }

platformCore([{
    provide: APP_ROOT,
    useValue: process.cwd()
}]).bootstrap(AppModule).then(async injector => {
    try {
        
    } catch (e: any) {
        console.log(e.message)
    }
});

process.on('uncaughtException', (error: Error, origin: any) => {
    console.error(error.message)
})

process.on('unhandledRejection', (reason: unknown, promise: Promise<unknown>) => {
    console.error(reason)
    promise.catch(e => console.error(e.message))
})
