#!/usr/bin/env node
const dotenv = require('dotenv')
dotenv.config({
    path: '../config/.env'
})
import "reflect-metadata";
process.env.TZ = 'Asia/Shanghai';
import { Module, platformCore, APP_ROOT } from '@nger/core';
import { RabbitmqModule, RedisModule, TasksModule } from './framework';
import { Db, TypeormModule } from "@nger/typeorm";
import { SpiderUser } from "./entities/spider_user";
import { EventEmitter } from "stream";
import { coreProviders } from "./tokens";

EventEmitter.setMaxListeners(1000)

@Module({
    imports: [
        RabbitmqModule.forRoot(),
        TasksModule.forRoot(),
        TypeormModule.forEnv(),
        RedisModule,
        TypeormModule.forFeature([
            SpiderUser
        ])
    ],
    providers: [
        ...coreProviders
    ]
})
export class AppModule { }

platformCore([{
    provide: APP_ROOT,
    useValue: process.cwd()
}]).bootstrap(AppModule)
    .then(async injector => {
        const db = injector.get(Db);
        console.info('恭喜您主old启动成功');
    });
