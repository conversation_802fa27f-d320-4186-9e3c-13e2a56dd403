import { Injectable, Injector } from "@nger/core";
import { Db } from "@nger/typeorm";
import { DataSource, EntityManager } from 'typeorm'
import { SpilderTopicArticleComment } from "../entities/spilder-topic-article-comment";
import { SpilderTopicArticleCommentTend } from "../entities/spilder-topic-article-comment-tend";
import { SpilderTopicArticleCommentUnique } from "../entities/spilder-topic-article-comment-unique";

@Injectable()
export class CommentService {
    private get db() {
        return this.injector.get(Db)
    }
    private get dataSource() {
        return this.injector.get(DataSource)
    }
    constructor(private injector: Injector) { }
    private createQueryRunner() {
        return this.db.createQueryRunner()
    }
    upgrades(comments: SpilderTopicArticleComment[]) {
        return [
            ...new Set(comments.map(c => `${c.platform}$-$${c.cid}`))
        ].map(a => a.split('$-$'))
    }
    async upgrade(comment: SpilderTopicArticleComment, needDeletes: number[] = []) {
        const runner = this.createQueryRunner();
        await runner.connect();
        await runner.startTransaction();
        const entity = new EntityManager(this.dataSource, runner)
        try {
            try {
                await entity.insert(SpilderTopicArticleCommentUnique, { platform: comment.platform, cid: comment.cid })
            } catch (e) {
                // remove 
            }
            await runner.commitTransaction();
        }
        catch (e) {
            // 插入截面数据
            needDeletes.push(comment.id);
            await runner.rollbackTransaction();
        }
        finally {
            await runner.release();
        }
    }
    async insertOrUpdateComment(comment: SpilderTopicArticleComment) {
        const runner = this.createQueryRunner();
        await runner.connect();
        await runner.startTransaction();
        const entity = new EntityManager(this.dataSource, runner)
        try {
            await entity.insert(SpilderTopicArticleCommentUnique, { platform: comment.platform, cid: comment.cid })
            await entity.insert(SpilderTopicArticleComment, comment)
            await runner.commitTransaction();
        }
        catch (e) {
            // 插入截面数据
            const tend = new SpilderTopicArticleCommentTend();
            tend.cid = comment.id;
            tend.like_counts = comment.like_counts;
            await entity.save(SpilderTopicArticleCommentTend, tend)
        }
        finally {
            await runner.release();
        }
    }
}