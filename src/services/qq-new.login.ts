import { Injectable, Injector, defer } from "@nger/core";
import { Subject } from "rxjs";
import { CryptoService } from '@nger/utils'
import { PUPPETEER_POOL } from "../tokens";
@Injectable()
export class QqNewLoginService {
    get crypto() {
        return this.injector.get(CryptoService)
    }
    constructor(private injector: Injector) { }
    private delay(time: number) {
        const d = defer();
        setTimeout(() => {
            d.resolve();
        }, time)
        return d;
    }
    get puppeteerPool(){
        return this.injector.get(PUPPETEER_POOL)
    }
    async login(login_state: Subject<any>) {
        const browser = await this.puppeteerPool.acquire()
        const url = 'https://news.qq.com/'
        const ctx = browser.defaultBrowserContext();
        await ctx.overridePermissions(url, ['geolocation'])
        const page = await browser.newPage();
        page.setJavaScriptEnabled(true);
        page.setViewport({
            width: 1325,
            height: 768
        });
        await page.goto(url, {
            waitUntil: ['networkidle2']
        });
        const href = page.url();
        login_state.next({
            action: 'open',
            data: href
        })
        try {
            const loginBtn = await page.waitForSelector('#login');
            login_state.next({
                action: 'get_login_btn',
                data: href
            })
            if (loginBtn) {
                await loginBtn.hover();
                const sel = await page.waitForSelector('.agreementlogin .sel')
                if (sel) {
                    await sel.click()
                }
                await loginBtn.click();
                login_state.next({
                    action: 'click_login_btn',
                    data: href
                })
            }
            await page.waitForNavigation()
            let iframUrl = await page.$eval('iframe', (img) => (img as HTMLIFrameElement).src);
            login_state.next({
                action: 'get_login_iframe_url',
                data: iframUrl
            })
            console.log(iframUrl)
            await page.goto(iframUrl)
            // let img = await page.waitForSelector('#qrlogin_img').catch(e => {
            //     return null;
            // });
            // while(!img){
            //     img = await page.waitForSelector('#qrlogin_img').catch(e => {
            //         return null;
            //     });
            // }
            // let loginImage = await page.$eval('img#qrlogin_img', (img) => (img as HTMLImageElement).src);
            // login_state.next({
            //     action: 'get_login_qrcode',
            //     data: loginImage
            // });
            let tip = await page.waitForSelector('div.step2_outer>div.qr_h3').catch(e => {
                return null;
            })
            while (!tip) {
                tip = await page.waitForSelector('div.step2_outer>div.qr_h3').catch(e => {
                    return null;
                })
            }
            const txt = await page.$eval('div.step2_outer>div.qr_h3', (div) => {
                return (div as HTMLDivElement).innerText;
            });
            login_state.next({
                action: 'get_scan_qrcode',
                data: txt
            })
            // 扫描成功
            await page.waitForNavigation()
            // await page.setRequestInterception(false);
            const cookies = await page.cookies('https://news.qq.com/')
            login_state.next({
                action: 'login_success',
                data: url,
                cookies
            });
            let userDiv = await page.waitForSelector('div.logined').catch(e => {
                return null;
            })
            while (!userDiv) {
                userDiv = await page.waitForSelector('div.logined').catch(e => {
                    return null;
                })
            }
            const avatar = await page.$eval('div.user-icon > a > img', a => {
                const href = (a as HTMLImageElement).src;
                return href;
            })
            login_state.next({
                action: 'get_userinfo',
                data: {
                    cookies,
                    avatar
                }
            });
        } catch (e: any) {
            login_state.next({
                action: 'get_login_fail',
                data: e.message
            })
            login_state.error(e)
        }
        finally {
            await page.close()
            this.puppeteerPool.release(browser)
        }
    }
}