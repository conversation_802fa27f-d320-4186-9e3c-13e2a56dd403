import { Injectable, Injector, defer } from "@nger/core";
import { Subject } from "rxjs";
import { CryptoService } from '@nger/utils'
import { PUPPETEER_POOL } from "../tokens";
@Injectable()
export class ToutiaoLoginService {
    get crypto() {
        return this.injector.get(CryptoService)
    }
    constructor(private injector: Injector) { }
    private delay(time: number) {
        const d = defer();
        setTimeout(() => {
            d.resolve();
        }, time)
        return d;
    }
    get puppeteerPool(){
        return this.injector.get(PUPPETEER_POOL)
    }
    async login(login_state: Subject<any>) {
        const browser = await this.puppeteerPool.acquire()
        const url = 'https://www.toutiao.com/'
        const ctx = browser.defaultBrowserContext();
        await ctx.overridePermissions(url, ['geolocation'])
        const page = await browser.newPage();
        page.setJavaScriptEnabled(true);
        page.setViewport({
            width: 1325,
            height: 768
        });
        await page.goto(url, {
            waitUntil: ['networkidle2']
        });
        const href = page.url();
        login_state.next({
            action: 'open',
            data: href
        })
        try {
            const loginBtn = await page.waitForSelector('.login-button');
            login_state.next({
                action: 'get_login_btn',
                data: href
            })
            if (loginBtn) {
                await loginBtn.click();
                login_state.next({
                    action: 'click_login_btn',
                    data: href
                })
            }
            let img = await page.waitForSelector('img.web-login-scan-code__content__qrcode-wrapper__qrcode').catch(e => {
                return null;
            });
            while (!img) {
                if (loginBtn) {
                    await loginBtn.click();
                    login_state.next({
                        action: 'click_login_btn',
                        data: href
                    })
                    img = await page.waitForSelector('img.web-login-scan-code__content__qrcode-wrapper__qrcode').catch(e => {
                        return null;
                    });
                }
            }
            let loginImage = await page.$eval('img.web-login-scan-code__content__qrcode-wrapper__qrcode', (img) => (img as HTMLImageElement).src);
            login_state.next({
                action: 'get_login_qrcode',
                data: loginImage
            })
            let tip = await page.waitForSelector('p.web-login-scan-code__content__qrcode-wrapper__mask__toast__text').catch(e => {
                return null;
            })
            while (!tip) {
                tip = await page.waitForSelector('p.web-login-scan-code__content__qrcode-wrapper__mask__toast__text').catch(e => {
                    return null;
                })
            }
            const txt = await page.$eval('p.web-login-scan-code__content__qrcode-wrapper__mask__toast__text', (div) => {
                return (div as HTMLDivElement).innerText;
            });
            login_state.next({
                action: 'get_scan_qrcode',
                data: txt
            })
            // 扫描成功
            await page.waitForNavigation()
            // await page.setRequestInterception(false);
            const cookies = await page.cookies('https://toutiao.com/')
            login_state.next({
                action: 'login_success',
                data: url,
                cookies
            });
            let userDiv = await page.waitForSelector('div.user-icon').catch(e => {
                return null;
            })
            while (!userDiv) {
                userDiv = await page.waitForSelector('div.user-icon').catch(e => {
                    return null;
                })
            }
            const link = await page.$eval('div.user-icon > a', a => {
                const href = (a as HTMLAnchorElement).href;
                return href;
            })
            const name = await page.$eval('div.user-icon > a > span', a => {
                const href = (a as HTMLSpanElement).innerText;
                return href;
            })
            const avatar = await page.$eval('div.user-icon > a > img', a => {
                const href = (a as HTMLImageElement).src;
                return href;
            })
            // https://www.toutiao.com/c/user/token/MS4wLjABAAAAAQoIjXdnz07Qc--kUzH-vG8zwuxpC4zveo0dHEtl-7npUZOUJTs8uPFMPXRg8U-V/?source=feed
            const match = /^https:.*?\/c\/user\/token\/(.*)\//.exec(link)
            let uid = ''
            if (match && match.length > 0) {
                uid = match[1]
            }
            login_state.next({
                action: 'get_userinfo',
                data: {
                    cookies,
                    uid,
                    name,
                    avatar
                }
            });
        } catch (e: any) {
            login_state.next({
                action: 'get_login_fail',
                data: e.message
            })
            login_state.error(e)
        }
        finally {
            await page.close()
            this.puppeteerPool.release(browser)
        }
    }
}