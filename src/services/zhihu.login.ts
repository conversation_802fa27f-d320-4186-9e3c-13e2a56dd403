import { Injectable, Injector, defer } from "@nger/core";
import { Subject } from "rxjs";
import { CryptoService } from '@nger/utils'
import { PUPPETEER_POOL } from "../tokens";
@Injectable()
export class ZhihuLoginService {
    get crypto() {
        return this.injector.get(CryptoService)
    }
    constructor(private injector: Injector) { }
    private delay(time: number) {
        const d = defer();
        setTimeout(() => {
            d.resolve();
        }, time)
        return d;
    }
    get puppeteerPool(){
        return this.injector.get(PUPPETEER_POOL)
    }
    async login(login_state: Subject<any>) {
        const browser = await this.puppeteerPool.acquire()
        const url = 'https://www.zhihu.com/signin?next=%2F'
        const ctx = browser.defaultBrowserContext();
        await ctx.overridePermissions(url, ['geolocation'])
        const page = await browser.newPage();
        page.setJavaScriptEnabled(true);
        page.setViewport({
            width: 1920,
            height: 768
        });
        await page.goto(url, {
            waitUntil: ['networkidle2']
        });
        this.delay(20)
        const href = page.url();
        login_state.next({
            action: 'open',
            data: href
        })
        try {
            let loginImage = await page.$eval('div.Qrcode-img > img', (img) => (img as HTMLImageElement).src);
            login_state.next({
                action: 'get_login_qrcode',
                data: loginImage
            })
            const tip = await page.waitForSelector('p.Qrcode-scanResultTips', { timeout: 1000 * 60 })
            if (tip) {
                const txt = await page.$eval('p.Qrcode-scanResultTips', (div) => {
                    return (div as HTMLDivElement).innerText;
                });
                login_state.next({
                    action: 'get_scan_qrcode',
                    data: txt
                })
                // 扫描成功
                await page.waitForNavigation()
                // await page.setRequestInterception(false);
                const cookies = await page.cookies('https://zhihu.com/')
                const initialData = await page.$eval('script#js-initialData', script => {
                    const json = script.innerHTML
                    return JSON.parse(json);
                })
                login_state.next({
                    action: 'login_success',
                    data: url,
                    cookies,
                    initialData
                });
                const response = await page.goto('https://www.zhihu.com/api/v4/me?include=is_active')
                if(response){
                    const user = await response.json()
                    const uid = user.id;
                    const name = user.name;
                    const avatar = user.avatar_url;
                    login_state.next({
                        action: 'get_userinfo',
                        data: {
                            uid,
                            cookies,
                            name,
                            avatar
                        }
                    });
                }
            }
        } catch (e: any) {
            login_state.next({
                action: 'get_login_fail',
                data: e.message
            })
            login_state.error(e)
        }
        finally {
            await page.close()
            this.puppeteerPool.release(browser)
        }
    }
}