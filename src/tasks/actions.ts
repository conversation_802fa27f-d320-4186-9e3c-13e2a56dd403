import { StringToken } from "@nger/core";

/**
 * 执行文章抓取任务
 */
export interface IArticleTask {
    link: string;
    eid: number;
    tid: number;
    page: number;
    uid: number;
}
export const ARTICLE_TASK: StringToken<IArticleTask> = `@nger/weibo/article-task`

export interface IArticleDetailTask {
    link: string;
    uid: number;
    tid: number;
    eid: number;
}
export const ARTICLE_DETAIL_TASK: StringToken<IArticleDetailTask> = `@nger/weibo/article-detail-task`


/**
 * 创建文章抓取任务
 */
export interface ICreateArticleTask {
    link: string;
    eid: number;
    tid: number;
    uid: number;
}
export const CREATE_ARTICLE_TASK: StringToken<ICreateArticleTask> = `@nger/weibo/create-article-task`;

export interface ITopicTask {
    keyword: string;
    uid: number;
    eid: number;
    page: number;
}
export const TOPIC_TASK: StringToken<ITopicTask> = `@nger/weibo/topic-task`;

export interface ICreateTopicTask {
    keyword: string;
    uid: number;
    eid: number;
}
export const CREATE_TOPIC_TASK: StringToken<ICreateTopicTask> = `@nger/weibo/create-topic-task`;


export interface ICreateCommentTask {
    aid: string;
    tid: number;
    eid: number;
}
export const CREATE_COMMENT_TASK: StringToken<ICreateCommentTask> = `@nger/weibo/create-comment-task`


export interface ICommentTask {
    code: string;
    uid: number;
    aid: number;
    eid: number;
    tid: number;
    accountId: string;
}
export const COMMENT_TASK: StringToken<ICommentTask> = `@nger/weibo/comment-task`;
