import { Injector, Next } from "@nger/core";
import { COMPLETE, DATA, FAIL, Task } from "@nger/rabbitmq";
import { Db } from "@nger/typeorm";
import { CountArticle, CountArticleComment } from "../entities/count";

export interface FenciData {
    id: number;
    type: string;
    results: any;
    sentiment_results: any;
    sentiment_word: any;
    readability: number;
}

export class FenciTask extends Task {
    constructor() {
        super(`@nger/weibo/fenci-result`)
        this.count = 1;
    }
    async handle(injector: Injector, next?: Next | undefined) {
        const complete = injector.get(COMPLETE);
        const fail = injector.get(FAIL);
        const data = injector.get<FenciData>(DATA);
        const db = injector.get(Db);
        try {
            const { id, type } = data;
            switch (type) {
                case 'article':
                    const article = await this.handleArticle(data);
                    await db.manager.save(article);
                    await complete();
                    break;
                case 'comment':
                    const comment = await this.handleComment(data);
                    await db.manager.save(comment);
                    await complete();
                    break;
                default:
                    break;
            }
        } catch (e) {
            console.log((e as Error).message)
            fail();
        }
        next && next();
    }
    async handleArticle(data: any) {
        const count = new CountArticle();
        count.eid = data.eid;
        count.postAt = new Date(data.postAt);
        count.aid = data.id;
        const [negative, neutral, positive, negativeWords, neutralWords, positiveWords] = this.getSentiment(data.sentiment_word)
        count.negative = negative;
        count.neutral = neutral;
        count.positive = positive;
        count.negativeWords = negativeWords;
        count.neutralWords = neutralWords;
        count.positiveWords = positiveWords;
        count.readability = Number(data.readability);
        const [acount, awords] = this.getA(data.results)
        count.a = acount;
        count.aWords = awords;
        const [vcount, vwords] = this.getV(data.results);
        count.v = vcount;
        count.vWords = vwords;
        const [dcount, dwords] = this.getD(data.results);
        count.d = dcount;
        count.dWords = dwords;
        const [ncount, nwords] = this.getN(data.results);
        count.n = ncount;
        count.nWords = nwords;
        count.l = data.text.length;
        count.emotion = Number(data.sentiment_conf);
        count.platform = data.platform;
        // 词云
        return count;
    }
    getWords(start: string, results: string[][][]): [number, string[]] {
        const words: string[] = []
        const list = results[0][1].filter((it, index) => {
            if (it.startsWith(start)) {
                words.push(results[0][0][index])
                return true;
            }
            return false;
        });
        return [list.length, words]
    }
    getN(results: string[][][]): [number, string[]] {
        return this.getWords('n', results)
    }
    getA(results: string[][][]): [number, string[]] {
        return this.getWords('a', results)
    }
    getV(results: string[][][]): [number, string[]] {
        return this.getWords('v', results)

    }
    getD(results: string[][][]): [number, string[]] {
        return this.getWords('v', results)
    }
    getSentiment(results: string[][]): [number, number, number, string[], string[], string[]] {
        const list = results[0];
        const types = results[1];
        // negative 负面
        // neutral 中立
        // positive 正面
        let negative = 0;
        let negativeWords: string[] = []
        let neutral = 0;
        let neutralWords: string[] = []
        let positive = 0;
        let positiveWords: string[] = []
        types.map((type, index) => {
            if (type === 'positive') {
                positive += 1;
                positiveWords.push(list[index])
            } else if (type === 'negative') {
                negative += 1;
                negativeWords.push(list[index])
            } else {
                neutral += 1;
                neutralWords.push(list[index])
            }
        });
        return [
            negative,
            neutral,
            positive,
            negativeWords,
            neutralWords,
            positiveWords
        ]
    }
    async handleComment(data: any) {
        const count = new CountArticleComment();
        count.eid = data.eid;
        count.postAt = new Date(data.postAt);
        count.cid = data.id;
        const [negative, neutral, positive, negativeWords, neutralWords, positiveWords] = this.getSentiment(data.sentiment_word)

        count.negative = negative;
        count.neutral = neutral;
        count.positive = positive;
        count.negativeWords = negativeWords;
        count.neutralWords = neutralWords;
        count.positiveWords = positiveWords;
        count.readability = Number(data.readability);
        const [acount, awords] = this.getA(data.results)
        count.a = acount;
        count.aWords = awords;
        const [vcount, vwords] = this.getV(data.results);
        count.v = vcount;
        count.vWords = vwords;
        const [dcount, dwords] = this.getD(data.results);
        count.d = dcount;
        count.dWords = dwords;
        const [ncount, nwords] = this.getN(data.results);
        count.n = ncount;
        count.nWords = nwords;
        count.l = data.text.length;
        count.isChild = !!data.pid;
        count.platform = data.platform;
        // 词云
        return count;
    }
}
