import { Injector, Next } from "@nger/core";
import { COMPLETE, DATA, FAIL, Task } from "@nger/rabbitmq";
import { Db } from "@nger/typeorm";
export interface Migration {
    name: string;
    entities: any[];
}
export class MigrationTask extends Task {
    constructor() {
        super('@nger/migration-data-task')
    }
    async handle(injector: Injector, next?: Next | undefined) {
        const data = injector.get<Migration>(DATA);
        const complete = injector.get(COMPLETE)
        const fail = injector.get(FAIL)
        try {
            const db = injector.get(Db);
            const manager = db.manager.getRepository(data.name);
            await manager.insert(data.entities);
            await complete();
        } catch (e) {
            console.log((e as any).message);
            fail();
        }
        next && next();
    }
}
