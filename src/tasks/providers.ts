import { <PERSON><PERSON><PERSON>rovider } from "@nger/core";
import { Task } from "@nger/rabbitmq";
import { FenciTask } from "./fenci-task";
import { MigrationTask } from "./migration-task";
// import { RealTimeTask } from "./real-time.task";
import { SpilderAccountTask } from "./spilder-account.task";
import { SpilderScheduleTask } from "./spilder-schedule.task";
import { TopicArticleChildCommentTask } from "./topic-article-child-comment.task";
import { TopicArticleCommentTask } from "./topic-article-comment.task";
import { TopicArticleDetailTask } from "./topic-article-detail.task";
import { TopicArticleLinksTask } from "./topic-article-links.task";
import { TopicArticleMoreChildCommentTask } from "./topic-article-more-child-comment.task";
import { TopicArticleMoreCommentTask } from "./topic-article-more-comment.task";
import { TopicArticlePageLinksTask } from "./topic-article-page-links.task";
import { TopicDetailTask } from "./topic-detail.task";
import { TopicLinksTask } from "./topic-links.task";
import { TopicPageLinksTask } from "./topic-page-links.task";

export const tasksProviders: StaticProvider<any>[] = [
    // {
    //     provide: Task,
    //     useFactory: () => new RealTimeTask(),
    //     multi: true
    // },
    {
        provide: Task,
        useFactory: () => new FenciTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new MigrationTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new TopicPageLinksTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new TopicLinksTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new TopicDetailTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new TopicArticlePageLinksTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new TopicArticleLinksTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new TopicArticleDetailTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new TopicArticleCommentTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new TopicArticleMoreCommentTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new TopicArticleChildCommentTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new SpilderAccountTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new TopicArticleMoreChildCommentTask(),
        multi: true
    },
    {
        provide: Task,
        useFactory: () => new SpilderScheduleTask(),
        multi: true
    }
];
