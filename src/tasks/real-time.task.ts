// import { Injector, Next } from "@nger/core";
// import { COMPLETE, DATA, FAIL, Task, TaskManager } from "@nger/rabbitmq";
// import { RedisService } from "@nger/redis";
// import { SpilderError } from "../adapter/spilder";
// import { SpilderFactory } from "../adapter/spilder-factory";
// import { CryptoService } from '@nger/utils'
// export interface ITopicPageLinksTask {
//     keyword: string;
//     eid: number;
//     type: string;
// }
// export class RealTimeTask extends Task {
//     constructor() {
//         super(`@nger/weibo/real-time-task`)
//     }
//     async handle(injector: Injector, next?: Next | undefined) {
//         const data = injector.get<ITopicPageLinksTask>(DATA)
//         const complete = injector.get(COMPLETE)
//         const fail = injector.get(FAIL)
//         const { keyword, eid, type } = data;
//         if (!(keyword && eid && type)) {
//             await complete();
//             return next && next();
//         }
//         try {
//             const spilderFactory = injector.get(SpilderFactory);
//             const spilder = await spilderFactory.create(type);
//             const redis = injector.get(RedisService)
//             const cryptoService = injector.get(CryptoService)
//             const manager = injector.get(TaskManager);
//             try {
//                 let page = 1;
//                 let canStop = true
//                 while (page <= 50 && canStop) {
//                     const { links, count } = await spilder.getRealtimeArticleLinks(keyword, page);
//                     let canStops = await Promise.all(links.map(async link => {
//                         const key = cryptoService.md5(link)
//                         const res = await redis.get(key)
//                         return !!res
//                     }))
//                     canStop = !canStops.some(it => it)
//                     // send task
//                     links.map(async link=>{
//                         const key = cryptoService.md5(link)
//                         await manager.send({
//                             topic: '@nger/weibo/topic-article-detail',
//                             data: {
//                                 link: link,
//                                 type: type,
//                                 eid: eid,
//                                 tid: 0
//                             }
//                         });
//                         await redis.set(key, link)
//                     })
//                     page += 1;
//                 }
//                 console.log(`real time ${type} ${eid} ${keyword}`)
//                 await complete()
//                 // reget this keyword
//                 await spilder.delay(1000 * 5);
//                 await manager.send({
//                     topic: '@nger/weibo/real-time-task',
//                     data
//                 })
//                 return next && await next();
//             } catch (e: any) {
//                 throw e;
//             }
//         } catch (e: any) {
//             if (e instanceof SpilderError) {
//                 await complete();
//                 return next && await next();
//             }
//             console.log(e)
//             await fail();
//         }
//         return next && await next();
//     }
// }