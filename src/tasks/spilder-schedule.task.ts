/**
 * 定时任务
 */

import { Injector, Next } from "@nger/core";
import { COMPLETE, DATA, Task, TaskManager } from "@nger/rabbitmq";
import { Db } from "@nger/typeorm";
import { WbTask } from "../entities/task";
import { scheduleJob } from 'node-schedule';
import { WbEvent } from "../entities/event";
import { In } from "typeorm";

export interface ISchedule extends WbTask { }
export class SpilderScheduleTask extends Task {
    constructor() {
        super(`@nger/weibo/schedule-task`)
    }
    async handle(injector: Injector, next?: Next | undefined) {
        // 定时爬取数据快照
        const data = injector.get<ISchedule>(DATA);
        const complete = injector.get(COMPLETE);
        const { id, time, ids } = data;
        const db = injector.get(Db);
        if (!(id && time && ids)) {
            await complete();
            return next && await next();
        }
        const old = await db.manager.findOne(WbTask, { where: { id } });
        if (!old) {
            await complete();
            return next && await next();
        }
        const manager = injector.get(TaskManager)
        const job = scheduleJob(time, async (fireDate: Date) => {
            console.log(`scheduleJob`, fireDate)
            const old = await db.manager.findOne(WbTask, { where: { id } });
            if (!old) {
                job.cancel();
                await complete();
                return next && await next();
            }
            const events = await db.manager.find(WbEvent, { where: { id: In(ids) } });
            events.map(it => {
                const keywords = it.keywords;
                if (keywords) {
                    keywords.map(async key => {
                        await manager.send({
                            topic: '@nger/weibo/topic-page-links-task',
                            data: {
                                type: 'weibo',
                                eid: it.id,
                                keyword: key
                            }
                        });
                        await manager.send({
                            topic: '@nger/weibo/topic-page-links-task',
                            data: {
                                type: 'toutiao',
                                eid: it.id,
                                keyword: key
                            }
                        });
                        await manager.send({
                            topic: '@nger/weibo/topic-page-links-task',
                            data: {
                                type: 'zhihu',
                                eid: it.id,
                                keyword: key
                            }
                        });
                    });
                }
            })
        })
        return next && await next();
    }
}
