import { Injector, Next } from "@nger/core";
import { COMPLETE, DATA, FAIL, Task, TaskManager } from "@nger/rabbitmq";
import { Db } from "@nger/typeorm";
import { SpilderFactory } from "../adapter/spilder-factory";
import { SpilderTopicArticle } from "../entities/spilder-topic-article";
import { SpilderTopicArticleTend } from "../entities/spilder-topic-article-tend";

export interface TopicArticleDetail {
    link: string;
    type: string;
    eid: number;
    tid: number;
}
export class TopicArticleDetailTask extends Task {
    constructor() {
        super(`@nger/weibo/topic-article-detail`)
        this.count = 2;
    }
    async handle(injector: Injector, next?: Next | undefined) {
        const db = injector.get(Db)
        const data = injector.get<TopicArticleDetail>(DATA)
        const complete = injector.get(COMPLETE)
        const fail = injector.get(FAIL)
        const { link, tid, eid, type } = data;
        if (!(link) && !type) {
            await complete();
            return next && next();
        }
        try {
            const spilderFactory = injector.get(SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                // 获取数据
                const detail = await spilder.createTopicArticleDetail(link)
                if (!detail) {
                    await complete();
                    return next && next();
                }
                // 数据清洗
                const manager = injector.get(TaskManager)
                let article = new SpilderTopicArticle();
                article.eid = eid;
                article.tid = tid;
                article.platform = type;
                if (!detail.mid) {
                    await complete();
                    return next && next();
                }
                article.mid = detail.mid;
                article.mblogid = detail.mblogid;
                const { user, pic_ids, page_info } = detail;
                if (pic_ids && pic_ids.length > 0) {
                    article.hasImage = true;
                }
                if (page_info) {
                    const object_type = page_info.object_type;
                    if (object_type === 'video') {
                        article.hasVideo = true;
                    }
                    // 非原创
                    if (object_type === 'article') {
                        article.hasLink = true;
                    }
                }
                if (user) {
                    article.uid = user.idstr;
                    article.nickname = user.screen_name;
                    await manager.send({
                        topic: '@nger/weibo/spilder-account-task',
                        data: {
                            uid: article.uid,
                            type,
                        }
                    });
                }
                article.attitudes_count = detail.attitudes_count;
                article.comments_count = detail.comments_count;
                article.reposts_count = detail.reposts_count;
                article.source = detail.source;
                if (detail.isLongText) {
                    // 长文本 获取长文本
                    const longText = await spilder.createTopicArticleLongTextDetail(article.mblogid);
                    article.text_raw = longText.data?.longTextContent || detail.text_raw;
                } else {
                    article.text_raw = detail.text_raw || ''
                }
                article.pic_bg_new = detail.pic_bg_new;
                article.textLength = detail.textLength;
                if (detail.created_at) {
                    article.create_at = new Date(detail.created_at);
                }
                const { topic_struct } = detail;
                if (topic_struct) {
                    const structs = topic_struct.map((s: any) => s.topic_title);
                    article.structs = structs;
                } else {
                    article.structs = [];
                }
                const { title } = detail;
                if (title) {
                    article.title = title.text;
                }
                article.region_name = detail.region_name;
                const exisit = await db.manager.findOne(SpilderTopicArticle, { where: { mid: article.mid } })
                if (exisit) {
                    article.id = exisit.id;
                }
                article = await db.manager.save(SpilderTopicArticle, article);
                console.log('get a article', article.id)
                // 文章详情
                await manager.send({
                    topic: '@nger/weibo/wait_nlp',
                    data: { article, type: 'article' }
                });
                try {
                    const st = new SpilderTopicArticleTend();
                    st.aid = article.id;
                    st.attitudes_count = article.attitudes_count;
                    st.comments_count = article.comments_count;
                    st.reposts_count = article.reposts_count;
                    await db.manager.save(SpilderTopicArticleTend, st);
                } catch (e: any) {
                    console.error(e.message)
                }
                // 获取评论总数
                await manager.send({
                    topic: '@nger/weibo/topic-article-comment',
                    data: {
                        type,
                        eid,
                        tid,
                        aid: article.id,
                        id: article.mid,
                        uid: article.uid,
                        count: 200
                    }
                })
                await manager.send({
                    topic: '@nger/weibo/fenci',
                    data: {
                        id: article.id,
                        type: 'article',
                        text: article.text_raw
                    }
                });
                await complete();
            } catch (e: any) {
                console.error(e.message)
                await fail();
            }
        } catch (e: any) {
            console.error(e.message)
            await fail();
        }
        return next && await next();
    }
}