import { Injector, Next } from "@nger/core";
import { COMPLETE, DATA, FAIL, Task, TaskManager } from "@nger/rabbitmq";
import { Db } from "@nger/typeorm";
import { SpilderFactory } from "../adapter/spilder-factory";
export interface TopicArticleLinks {
    link: string;
    type: string;
    eid: number;
    tid: number;
}
export class TopicArticleLinksTask extends Task {
    constructor() {
        super(`@nger/weibo/topic-article-links`)
        this.count = 1;
    }
    async handle(injector: Injector, next?: Next | undefined) {
        const db = injector.get(Db)
        const data = injector.get<TopicArticleLinks>(DATA)
        const complete = injector.get(COMPLETE)
        const fail = injector.get(FAIL)
        const { link, tid, eid, type } = data;
        if (!(link && tid && eid && type)) {
            await complete();
            return next && next();
        }
        try {
            const spilderFactory = injector.get(SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                // console.info('get a link ---> ', link)
                const links = await spilder.createTopicArticleLinks(link).catch((e: any)=>{
                    console.error(e.message)
                    return []
                })
                const manager = injector.get(TaskManager)
                await Promise.all(links.map(link => {
                    manager.send({
                        topic: '@nger/weibo/topic-article-detail',
                        data: {
                            link,
                            eid,
                            tid,
                            type
                        }
                    })
                }))
                await complete();
            } catch (e) {
                console.error((e as any).message)
                await fail();
            }
        } catch (e) {
            console.error((e as any).message)
            await fail();
        }
        return next && await next();
    }
}