import { Injector, Next } from "@nger/core";
import { COMPLETE, DATA, FAIL, Task, TaskManager } from "@nger/rabbitmq";
import { Db } from "@nger/typeorm";
import { SpilderFactory } from "../adapter/spilder-factory";

export interface TopicArticlePageLinks {
    link: string;
    type: string;
    eid: number;
    tid: number;
}

export class TopicArticlePageLinksTask extends Task {
    constructor() {
        super(`@nger/weibo/topic-article-page-links-task`)
        this.count = 100;
    }
    async handle(injector: Injector, next?: Next | undefined) {
        const db = injector.get(Db)
        const data = injector.get<TopicArticlePageLinks>(DATA)
        const complete = injector.get(COMPLETE)
        const fail = injector.get(FAIL)
        const { link, tid, eid, type } = data;
        if (!(link && tid && eid && type)) {
            await complete();
            return next && next();
        }
        try {
            const spilderFactory = injector.get(SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                const links = await spilder.createTopicArticlePageLinks(link)
                if (!(links && links.length > 0)) {
                    await complete();
                    return next && next();
                }
                const manager = injector.get(TaskManager)
                await Promise.all(links.map(link => {
                    manager.send({
                        topic: '@nger/weibo/topic-article-links',
                        data: {
                            link,
                            eid,
                            tid,
                            type
                        }
                    })
                }))
                await complete();
            } catch (e) {
                await fail();
            }
        } catch (e) {
            await fail();
        }
        return next && await next();
    }
}