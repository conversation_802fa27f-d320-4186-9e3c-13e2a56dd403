import { Injector, Next } from "@nger/core";
import { COMPLETE, DATA, FAIL, Task, TaskManager } from "@nger/rabbitmq";
import { Db } from "@nger/typeorm";
import { SpilderFactory } from "../adapter/spilder-factory";
import { WbEvent } from "../entities/event";
import { SpilderTopic } from "../entities/spilder-topic";
import { SpilderTopicTend } from "../entities/spilder-topic-tend";

export interface ITopicDetail {
    topic: { list: string, detail: string };
    type: string;
    eid: number;
}
export class TopicDetailTask extends Task {
    constructor() {
        super(`@nger/weibo/topic-detail`)
        this.count = 100;
    }
    async handle(injector: Injector, next?: Next | undefined) {
        const db = injector.get(Db)
        const data = injector.get<ITopicDetail>(DATA)
        const complete = injector.get(COMPLETE)
        const fail = injector.get(FAIL)
        const { topic, eid, type } = data;
        if (!(topic && eid && type)) {
            console.log({ topic, eid, type })
            await complete();
            return next && next();
        }
        const event = db.manager.findOne(WbEvent, { where: { id: eid } })
        if (!event) {
            console.error('event not found')
            await complete();
            return next && next();
        }
        try {
            const spilderFactory = injector.get(SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                const detail = await spilder.createTopicDetail(topic.detail)
                const manager = injector.get(TaskManager)
                // 保存topic入库
                const { baseInfo } = detail;
                const { object, claim_info, count } = baseInfo;
                let t = new SpilderTopic();
                t.eid = eid;
                if (object) {
                    t.cate_id = object.cate_id;
                    t.category_str = object.category_str;
                    t.title = object.display_name;
                    t.summary = object.summary;
                    t.create_at = object.create_at;
                    const { creator } = object;
                    if (creator) {
                        t.uid = creator.uid || '';
                        await manager.send({
                            topic: '@nger/weibo/spilder-account-task',
                            data: {
                                uid: t.uid,
                                type,
                            }
                        });
                    }
                    t.target_url = object.target_url;
                    t.topic_tag = object.topic_tag;
                    t.location = object.location;
                } else {
                    await complete();
                    return next && next();
                }
                if (claim_info) {
                    t.claim_id = claim_info.id;
                    t.claim_name = claim_info.name;
                }
                if (count) {
                    t.read_count = count.read;
                    t.ori_uv_Count = count.ori_uv;
                    t.mention_count = count.mention;
                }
                t.platform = type;
                const exisit = await db.manager.findOne(SpilderTopic, { where: { title: t.title } })
                if (exisit) {
                    t.id = exisit.id;
                }
                /**
                 * todo
                 */
                t = await db.manager.save(SpilderTopic, t);
                await manager.send({
                    topic: '@nger/weibo/topic-article-page-links-task',
                    data: {
                        eid,
                        type,
                        tid: t.id,
                        link: topic.list
                    }
                });
                await manager.send({
                    topic: '@nger/weibo/fenci',
                    data: {
                        id: t.id,
                        type: 'topic',
                        text: t.summary
                    }
                });
                try {
                    const st = new SpilderTopicTend();
                    st.tid = t.id;
                    st.mention_count = t.mention_count;
                    st.ori_uv_Count = t.ori_uv_Count;
                    st.read_count = t.read_count;
                    await db.manager.save(SpilderTopicTend, st);
                } catch (e: any) {
                    console.log(e.message)
                }
                await complete();
            } catch (e: any) {
                await fail();
            }
        } catch (e: any) {
            await fail();
        }
        return next && await next();
    }
}