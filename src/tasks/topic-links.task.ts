import { Injector, Next } from "@nger/core";
import { COMPLETE, DATA, FAIL, Task, TaskManager } from "@nger/rabbitmq";
import { SpilderError } from "../adapter/spilder";
import { SpilderFactory } from "../adapter/spilder-factory";

export interface ITopicLinksTask {
    link: string;
    type: string;
    eid: number;
}
export class TopicLinksTask extends Task {
    constructor() {
        super(`@nger/weibo/topic-links-task`)
        this.count = 100;
    }
    async handle(injector: Injector, next?: Next | undefined) {
        const data = injector.get<ITopicLinksTask>(DATA)
        const complete = injector.get(COMPLETE)
        const fail = injector.get(FAIL)
        const { link, eid, type } = data;
        if (!(link && eid && type)) {
            await complete();
            return next && next();
        }
        try {
            const spilderFactory = injector.get(SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                const topics = await spilder.createTopicLinks(link)
                const manager = injector.get(TaskManager)
                await Promise.all(topics.map((topic) => {
                    manager.send({
                        topic: '@nger/weibo/topic-detail',
                        data: {
                            topic,
                            eid,
                            type
                        }
                    })
                }));
                await complete();
            } catch (e: any) {
                if (e instanceof SpilderError) {
                    await complete();
                    return next && await next();
                }
                console.log(e.message)
                await spilder.delay(1000)
                await fail();
            }
        } catch (e) {
            await fail();
        }
        return next && await next();
    }
}