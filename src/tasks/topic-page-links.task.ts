import { Injector, Next } from "@nger/core";
import { COMPLETE, DATA, FAIL, Task, TaskManager } from "@nger/rabbitmq";
import { SpilderError } from "../adapter/spilder";
import { SpilderFactory } from "../adapter/spilder-factory";

export interface ITopicPageLinksTask {
    keyword: string;
    eid: number;
    type: string;
}
export class TopicPageLinksTask extends Task {
    constructor() {
        super(`@nger/weibo/topic-page-links-task`)
        this.count = 100;
    }
    async handle(injector: Injector, next?: Next | undefined) {
        const data = injector.get<ITopicPageLinksTask>(DATA)
        const complete = injector.get(COMPLETE)
        const fail = injector.get(FAIL)
        const { keyword, eid, type } = data;
        if (!(keyword && eid && type)) {
            await complete();
            return next && next();
        }
        try {
            const spilderFactory = injector.get(SpilderFactory);
            const spilder = await spilderFactory.create(type);
            try {
                const { links, count } = await spilder.createTopicPageLinks(keyword);
                const manager = injector.get(TaskManager);
                await Promise.all(links.map(link => {
                    manager.send({
                        topic: '@nger/weibo/topic-links-task',
                        data: {
                            link,
                            eid,
                            type
                        }
                    })
                }));
                await complete();
            } catch (e: any) {
                await spilder.delay(10);
                throw e;
            }
        } catch (e: any) {
            if (e instanceof SpilderError) {
                await complete();
                return next && await next();
            }
            console.log(e.message)
            await fail();
        }
        return next && await next();
    }
}