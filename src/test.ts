#!/usr/bin/env node
import "reflect-metadata";
process.env.TZ = 'Asia/Shanghai';
import { Module, platformCore, APP_ROOT, INSTALL_LOCK } from '@nger/core';
import { ZookeeperModule } from '@nger/zookeeper';
import { TypeormModule } from '@nger/typeorm';
import { UtilsModule } from '@nger/utils';
import { RabbitMqModule, RabbitmqStarter } from '@nger/rabbitmq';
import { WeiboNodeModule } from "./weibo-node.module";
import { existsSync } from 'fs'
import { join } from "path";
@Module({
    imports: [
        TypeormModule.forRoot(),
        ZookeeperModule.forRoot(),
        RabbitMqModule.forRoot(),
        UtilsModule,
        WeiboNodeModule,
    ]
})
export class AppModule { }

platformCore([{
    provide: APP_ROOT,
    useValue: process.cwd()
}]).bootstrap(AppModule).then(async injector => {
    try {
        const root = injector.get(APP_ROOT)
        const isInstall = existsSync(join(root, 'config/config.json'))
        if (isInstall) {
            // const rabbitmqStarter = injector.get(RabbitmqStarter)
            // await rabbitmqStarter.start(true);
            // console.log('恭喜您，爬虫节点启动成功')
            // const redis = injector.get(RedisService)
            // await redis.set('version','1.0.0')
            // const version = await redis.get('version')
            // console.log(version)
        } else {
            console.info('please to install')
            process.exit(0)
        }
    } catch (e: any) {
        console.log(e.message)
    }
});


process.on('uncaughtException', (error: Error, origin: any) => {
    console.error(error.message)
})

process.on('unhandledRejection', (reason: unknown, promise: Promise<unknown>) => {
    console.error(reason)
    promise.catch(e => console.error(e.message))
})
