import { InjectionToken, Injector, StaticProvider } from '@nger/core'
import { createPuppeteerPool, <PERSON><PERSON><PERSON>, <PERSON> } from '@nger/puppeteer'
import { DataSource } from 'typeorm'
import { SpiderUser } from './entities/spider_user'
import { createUserPool } from './userPool'
import { SystemMessage } from './v2/message'
export const PUPPETEER_POOL = new InjectionToken<Pool<Browser>>(`BROWSER_POOL`)
export const WEIBO_USER_POOL = new InjectionToken<Pool<SpiderUser>>(`WEIBO_USER_POOL`)
export const ZHIHU_USER_POOL = new InjectionToken<Pool<SpiderUser>>(`ZHIHU_USER_POOL`)

export const coreProviders: StaticProvider<any>[] = [{
    provide: PUPPETEER_POOL,
    useFactory: () => createPuppeteerPool()
}, {
    provide: WEIBO_USER_POOL,
    useFactory: (injector: Injector) => {
        const ds = injector.get(DataSource)
        const r = ds.getRepository(SpiderUser)
        const systemMessage = injector.get(SystemMessage)
        return createUserPool('weibo', r, systemMessage)
    },
    deps: [Injector]
}, {
    provide: ZHIHU_USER_POOL,
    useFactory: (injector: Injector) => {
        const ds = injector.get(DataSource)
        const r = ds.getRepository(SpiderUser)
        const systemMessage = injector.get(SystemMessage)
        return createUserPool('zhihu', r, systemMessage)
    },
    deps: [Injector]
}];