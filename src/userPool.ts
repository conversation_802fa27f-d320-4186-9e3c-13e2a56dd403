import { Pool } from '@nger/puppeteer';
import { Repository } from 'typeorm';
import { SpiderUser } from './entities/spider_user';
import { UserLogoutError } from './framework/tasks/error';
import { SystemMessage } from './v2/message';

class UserPool {
    constructor(private repository: Repository<SpiderUser>, private platform: string) { }
    async acquire() {
        let users = await this.repository.findOne({
            where: { status: 2, platform: this.platform },
            order: { update_date: 'desc' },
        }).catch(e => {
            throw new UserLogoutError();
        })
        if(!users){
            users = await this.repository.findOne({
                where: { status: 1, platform: this.platform },
                order: { update_date: 'desc' },
            }).catch(e => {
                throw new UserLogoutError();
            })
        }
        return users;
    }
    release(resource: SpiderUser) { }
    async destroy(u: SpiderUser): Promise<void> {
        await this.repository.update(u.id, { status: 1 })
    }
}
export function createUserPool(platform: string, repository: Repository<SpiderUser>, systemMessage: SystemMessage) {
    return new UserPool(repository, platform);
}

export function createUserPoolOptions(
    platform: string,
    max: number = 100,
    repository: Repository<SpiderUser>,
    systemMessage: SystemMessage
) {
    return {
        name: 'spider-user-pool',
        create: async () => {
            const users = await repository.findOne({
                where: { status: 2, platform },
                order: { update_date: 'desc' },
            }).catch(e => {
                throw new UserLogoutError()
            })
            if (users) {
                return users;
            }
        },
        destroy: async (u: SpiderUser) => {
            systemMessage.sendText('logout', {
                title: `${u.platform}-${u.username}登陆失效`,
                content: `用户登陆失效，请扫码重新登陆`
            });
            await repository.update(u.id, { status: 1 })
        },
        validate: (u: SpiderUser) => u.status === 2,
        max: max,
        min: 1,
    };
}
