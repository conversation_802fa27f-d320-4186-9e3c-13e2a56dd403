import { Injector } from "@nger/core"
import { MqFactory } from "./framework"


export function createMq(injector: Injector) {
    const mqFactory = injector.get(MqFactory)
    return mqFactory.create({
        exchange: 'task.exchange',
        deadExchange: 'dead.task.exchange',
        routingKey: 'task.routingKey',
        deadRouting<PERSON>ey: 'dead.task.routingKey',
        queue: 'task.queue',
        deadQueue: 'dead.task.queue',
        prefetch: 1
    })
}