import { from, Observable, switchMap } from 'rxjs';
import { connect, Options, Connection, ConfirmChannel } from 'amqplib';
/**
 * rxjs amqp 
 */
export interface ConsumeMessage<T> {
    data: T;
    ack: () => void;
    nack: () => void;
    reject: () => void;
    publish: <T>(exchange: string, routingKey: string, ...contents: T[]) => Observable<void>
}
export class Amqp {
    constructor(private url: string | Options.Connect, private type: 'direct' | 'topic' | 'headers' | 'fanout' | 'match' = 'direct') { }
    private connect(): Observable<Connection> {
        return from(connect(this.url))
    }
    private createConfirmChannel(): Observable<ConfirmChannel> {
        return this.connect().pipe(
            switchMap(c => {
                return new Observable<ConfirmChannel>((sub) => {
                    const channel = c.createConfirmChannel()
                    channel.then(c => sub.next(c)).catch(e => sub.error(e));
                    return async () => {
                        await channel.then(c => c.close())
                        await c.close();
                    }
                })
            })
        )
    }
    public consume<T>(queue: string, count: number = 1): Observable<ConsumeMessage<T>> {
        return this.createConfirmChannel().pipe(switchMap(c => {
            return new Observable<ConsumeMessage<T>>((sub) => {
                c.prefetch(count).then(() => {
                    c.consume(queue, (msg) => {
                        if (msg) {
                            const content = msg.content.toString('utf-8');
                            const data = JSON.parse(content);
                            sub.next({
                                data,
                                ack: () => c.ack(msg),
                                nack: () => c.nack(msg, true, true),
                                reject: () => {
                                    c.reject(msg, false);
                                },
                                publish: <T>(exchange: string, routingKey: string, ...contents: T[]) => from(this.beforePublish(c, exchange, routingKey, ...contents))
                            })
                        }
                    }, { noAck: false });
                })
                return async () => { }
            })
        }))
    }
    public publish<T>(exchange: string, routingKey: string, ...contents: T[]): Observable<void> {
        return this.createConfirmChannel().pipe(
            switchMap(c => {
                return from(this.beforePublish<T>(c, exchange, routingKey, ...contents))
            })
        )
    }
    private async beforePublish<T>(channel: ConfirmChannel, exchange: string, routingKey: string, ...contents: T[]): Promise<void> {
        await channel.assertExchange(exchange, this.type, {
            durable: true
        });
        contents.map(content => channel.publish(exchange, routingKey, Buffer.from(JSON.stringify(content))));
        return channel.waitForConfirms()
    }
}
