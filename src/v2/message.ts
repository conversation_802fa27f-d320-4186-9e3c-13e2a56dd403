import { Db } from "@nger/typeorm";
import { createHash } from "crypto";
import { Observable, switchMap } from "rxjs";
import { Amqp, ConsumeMessage } from "./amqp";
import { User } from '@nger/entities';
import { SysMessageEntity } from "../entities/message";

export interface MessageContent {
    from: string;
    to: string;
    type: string;
    data: object;
    create_date: Date;
}
export interface TextMessage {
    title: string;
    content: string;
}
export class SystemMessage {
    constructor(private amqp: Amqp, private db: Db) { }
    send(message: Partial<MessageContent>) {
        message.from = 'system';
        message.create_date = new Date();
        return this.amqp.publish<Partial<MessageContent>>('message', 'system', message)
    }
    sendText(type: string, data: TextMessage) {
        const message: MessageContent = {
            from: 'system',
            to: '',
            type: type,
            data,
            create_date: new Date()
        }
        return this.amqp.publish<Partial<MessageContent>>('message', 'system', message);
    }
    receive() {
        return this.amqp.consume<MessageContent>('message.system', 1).pipe(
            switchMap(msg => {
                return this.handler(msg)
            })
        )
    }
    private handler(msg: ConsumeMessage<MessageContent>): Observable<ConsumeMessage<MessageContent>> {
        return new Observable((sub) => {
            this.saveMsg(msg.data).then(() => {
                msg.ack()
            }).catch(e => {
                console.error(e);
                msg.reject()
            }).finally(() => {
                sub.next(msg)
            })
        })
    }
    private async saveMsg(msg: MessageContent) {
        // 查找所有用户
        const users = await this.db.manager.find(User, {});
        const msgList = users.map(user => {
            return { ...msg, to: `${user.uid}` };
        }).flat();
        const allMessage = msgList.map(m => {
            const msg = new SysMessageEntity();
            msg.to = m.to;
            msg.from = m.from;
            msg.sn = this.createSn(m)
            msg.data = JSON.stringify(m.data);
            msg.send_date = m.create_date;
            msg.type = m.type;
            return msg;
        });
        const toInserts: SysMessageEntity[] = [];
        allMessage.map(it => {
            const isExisit = toInserts.find(t => {
                return t.sn === it.sn;
            });
            if (!isExisit) {
                toInserts.push(it);
            }
        });
        if (toInserts.length > 0) await this.db.manager.save(SysMessageEntity, toInserts);
    }
    private createSn(msg: MessageContent): string {
        return createHash('md5').update(JSON.stringify(msg)).digest('base64')
    }
}
