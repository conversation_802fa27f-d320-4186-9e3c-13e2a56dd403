import { Observable, of, switchMap, queueScheduler } from "rxjs";
import { Amqp, ConsumeMessage } from "./amqp";

const amqp = new Amqp({
    hostname: '*************',
    port: 15672,
    username: 'imeep<PERSON>',
    password: '123qwe'
});

export interface Request {
    headers: object;
}
export interface Response {
}
export function isResponse(val: any): val is Response {
    return false;
}

export class CrawlingTask {
    id!: number;
    // 类型 search position
    type!: string;
    // 关键字
    condiction!: string;
    // 平台
    platform!: string;
    // 开始时间
    start!: Date;
    // 结束时间
    end!: Date;
    // 步长
    step!: number;
    // 定时
    schedule!: string;
}

// 引擎负责控制数据流在系统中所有组件中流动, 并在相应动作发生时触发事件
export class Engine {
    spider!: Spider;
    start() {
        amqp.consume<Request>('request', 1).pipe(
            switchMap((request: ConsumeMessage<Request>) => {
                return this.spider.request(request.data)
            })
        ).subscribe()
    }
}

// 调度器从引擎接受request并将他们入队, 以便之后引擎请求他们时提供给引擎
export class Scheduler { }

// 下载器负责获取页面数据并提供给引擎，而后提供给spider
export class Downloader {
    start() {
        // req to res
        amqp.consume<Request>('request', 1).pipe(
            switchMap((msg: ConsumeMessage<Request>) => {
                return this.getHeaders().pipe(
                    switchMap(headers => {
                        const req = msg.data;
                        req.headers = headers;
                        return this.download(req).pipe(
                            switchMap(res => {
                                if (Array.isArray(res)) {
                                    const responses = res.filter(it => isResponse(it))
                                    const datas = res.filter(it => !isResponse(it))
                                    return this.multiSave(datas).pipe(switchMap(() => {
                                        return msg.publish('nger', 'response', ...responses);
                                    }))
                                } else {
                                    if (isResponse(res)) {
                                        return msg.publish('nger', 'response', res)
                                    } else {
                                        return this.save(res)
                                    }
                                }
                            })
                        )
                    })
                )
            })
        ).subscribe();
    }
    // req to res
    download(request: Request): Observable<Response | Response[] | object | object[]> {
        return of([])
    }
    // get headers
    getHeaders(): Observable<object> {
        return of({})
    }
    // save single data
    save(data: object): Observable<void> {
        return of();
    }
    // save multi data
    multiSave(data: object[]): Observable<void> {
        return of();
    }
}

export class Spider {
    platform!: string;
    type!: string;
    request(req: Request): Observable<Response> {
        // 发现新的 req 
        throw new Error();
    }
    pushRequest(req: Request) {
        return amqp.publish('nger', 'request', req);
    }
}

// 典型的处理有清理、 验证及持久化
export class Pipeline { }

// 下载器中间件是在引擎及下载器之间的特定钩子
export class DownloaderMiddleware { }

// Spider中间件是在引擎及Spider之间的特定钩子
export class SpiderMiddleware { }

// 