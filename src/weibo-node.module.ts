import { Module } from '@nger/core'
import { TypeormModule } from '@nger/typeorm';
import { SpilderFactory } from './adapter/spilder-factory';
import { WbCity } from './entities/city';
import { WbEvent } from './entities/event';
import { WbEventCategory } from './entities/event-category';
import { WbEventType } from './entities/event-type';
import { SpilderExportLog } from './entities/export-log';
import { WbPlatform } from './entities/platform';
import { SpilderAccount } from './entities/spilder-account';
import { SpilderTopic } from './entities/spilder-topic';
import { SpilderTopicArticle } from './entities/spilder-topic-article';
import { SpilderTopicArticleComment } from './entities/spilder-topic-article-comment';
import { SpilderTopicArticleCommentTend } from './entities/spilder-topic-article-comment-tend';
import { SpilderTopicArticleTend } from './entities/spilder-topic-article-tend';
import { SpilderTopicTend } from './entities/spilder-topic-tend';
import { WbTask } from './entities/task';
import { tasksProviders } from './tasks/providers';
import { SpilderTopicArticleCommentUnique } from './entities/spilder-topic-article-comment-unique';
import { CommentService } from './services/comment.service';
import { CountArticle, CountArticleComment } from './entities/count';

@Module({
    providers: [
        SpilderFactory,
        ...tasksProviders,
        CommentService
    ],
    imports: [
        TypeormModule.forFeature([
            WbCity,
            WbEvent,
            WbEventType,
            WbEventCategory,
            WbPlatform,
            SpilderTopic,
            SpilderTopicArticle,
            SpilderTopicArticleComment,
            SpilderAccount,
            SpilderExportLog,
            SpilderTopicTend,
            SpilderTopicArticleTend,
            SpilderTopicArticleCommentTend,
            WbTask,
            SpilderTopicArticleCommentUnique,
            CountArticle,
            CountArticleComment,
        ]),
    ],
    controllers: []
})
export class WeiboNodeModule { }
