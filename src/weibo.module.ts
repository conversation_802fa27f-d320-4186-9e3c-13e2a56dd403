import { Injector, Module } from '@nger/core'
import { createMigration, Db, TypeormModule } from '@nger/typeorm';
import { SpilderFactory } from './adapter/spilder-factory';
import { FenciController } from './controllers/fenci.controller';
import { LoginController } from './controllers/login.controller';
import { SystemInfo } from './controllers/system-info';
import { WbCity } from './entities/city';
import { WbEvent } from './entities/event';
import { WbEventCategory } from './entities/event-category';
import { WbEventType } from './entities/event-type';
import { SpilderExportLog } from './entities/export-log';
import { WbPlatform } from './entities/platform';
import { SpilderAccount } from './entities/spilder-account';
import { SpilderTopic } from './entities/spilder-topic';
import { SpilderTopicArticle } from './entities/spilder-topic-article';
import { SpilderTopicArticleComment } from './entities/spilder-topic-article-comment';
import { SpilderTopicArticleCommentTend } from './entities/spilder-topic-article-comment-tend';
import { SpilderTopicArticleTend } from './entities/spilder-topic-article-tend';
import { SpilderTopicTend } from './entities/spilder-topic-tend';
import { WbTask } from './entities/task';
import { city1657731735130 } from './migrations/city.migration';
import { WbEventCategoryData1658900661284 } from './migrations/event-category-data.migration';
import { WbEventCategory1658900342571 } from './migrations/event-category.migration';
import { WbEventTypeData1658900374793 } from './migrations/event-type-data.migration';
import { WbEventType1658900303914 } from './migrations/event-type.migration';
import { event1657763465436 } from './migrations/event.migration';
import { platform1658554213740 } from './migrations/platform-data.migration';
import { platform1658554174060 } from './migrations/platform.migration';
import { SpilderAccount1658935369563 } from './migrations/spilder-account.migration';
import { SpilderExportLog1659426673921 } from './migrations/spilder-export-log.migration';
import { SpilderTopicArticleCommentTend1659433214868 } from './migrations/spilder-topic-article-comment-tend.migration';
import { spilderTopicArticleComment1658671163802 } from './migrations/spilder-topic-article-comment.migration';
import { SpilderTopicArticleTend1659433177432 } from './migrations/spilder-topic-article-tend.migration';
import { spilderTopicArticle1658666059979 } from './migrations/spilder-topic-article.migration';
import { SpilderTopicTend1659433128281 } from './migrations/spilder-topic-tend.migration';
import { spilderTopic1658645005229 } from './migrations/spilder-topic.migration';
import { WbTask1659433453188 } from './migrations/task.migration';
import { tasksProviders } from './tasks/providers';
import { SpilderTopicArticleCommentUnique } from './entities/spilder-topic-article-comment-unique';
import { spilderTopicArticleCommentUnique1661934452670 } from './migrations/spilder-topic-article-comment-unique.migration';
import { count1662172062070 } from './migrations/count.migration';
import { CountArticle, CountArticleComment } from './entities/count';
import { Account1662542482429 } from './migrations/account-data.migration';
import { SpiderOnline1663750157077 } from './migrations/spider-online.migration';
import { SpiderOnline } from './entities/spider-online';
import { SpiderUrl1663773389836 } from './migrations/spider-url.migration';
import { SpiderUrl } from './entities/spider_url';
import { Keyword } from './entities/keyword';
import { Keyword1664352047120 } from './migrations/keyword.migration';
import { SpiderUser } from './entities/spider_user';
import { SpiderUser1664358244796 } from './migrations/spider_user.migration';
import { BindUserController } from './controllers/bind-user.controller';
import { ZhihuLoginService } from './services/zhihu.login';
import { ToutiaoLoginService } from './services/toutiao.login';
import { Gonggao } from './entities/gonggao';
import { Gonggao1665026397032 } from './migrations/gonggao.migration';
import { QqNewLoginService } from './services/qq-new.login';
import { SpiderNews } from './entities/spider_news';
import { SpiderNews1665754851939 } from './migrations/spider-news.migration';
import { Setting1665820822756 } from './migrations/setting.migration';
import { News1665820797372 } from './migrations/news.migration';
import { Setting } from './entities/setting';
import { News } from './entities/news';
import { SysMessageEntity } from './entities/message';
import { MessageController } from './controllers/message';
import { SystemMessage } from './v2/message';
import { Amqp } from './v2/amqp';
import { CrawlingUrl1675300939131 } from './migrations/crawling_url.migration';
import { CrawlingUrl } from './entities/crawling_url';
import { searchProvicers } from './framework/tasks/search';
import { CrawlingController } from './controllers/crawling.controller';
import { coreProviders } from './tokens';
import { RabbitmqModule, RedisModule, TasksModule } from './framework';
import { SysMessage1676478574476 } from './migrations/message.migration';

@Module({
    providers: [
        ...createMigration(city1657731735130),
        ...createMigration(event1657763465436),
        ...createMigration(platform1658554174060),
        ...createMigration(platform1658554213740),
        ...createMigration(spilderTopic1658645005229),
        ...createMigration(spilderTopicArticle1658666059979),
        ...createMigration(spilderTopicArticleComment1658671163802),
        ...createMigration(WbEventType1658900303914),
        ...createMigration(WbEventTypeData1658900374793),
        ...createMigration(WbEventCategory1658900342571),
        ...createMigration(WbEventCategoryData1658900661284),
        ...createMigration(SpilderAccount1658935369563),
        ...createMigration(SpilderExportLog1659426673921),
        ...createMigration(SpilderTopicTend1659433128281),
        ...createMigration(SpilderTopicArticleTend1659433177432),
        ...createMigration(SpilderTopicArticleCommentTend1659433214868),
        ...createMigration(spilderTopicArticleCommentUnique1661934452670),
        ...createMigration(WbTask1659433453188),
        ...createMigration(count1662172062070),
        ...createMigration(Account1662542482429),
        ...createMigration(SpiderOnline1663750157077),
        ...createMigration(SpiderUrl1663773389836),
        ...createMigration(Keyword1664352047120),
        ...createMigration(SpiderUser1664358244796),
        ...createMigration(Gonggao1665026397032),
        ...createMigration(SpiderNews1665754851939),
        ...createMigration(Setting1665820822756),
        ...createMigration(News1665820797372),
        ...createMigration(CrawlingUrl1675300939131),
        ...createMigration(SysMessage1676478574476),
        ZhihuLoginService,
        ToutiaoLoginService,
        SpilderFactory,
        QqNewLoginService,
        ...coreProviders,
        {
            provide: Amqp,
            useFactory: () => {
                const options = {
                    hostname: process.env['MQ_HOST'] || '*************',
                    port: parseInt(process.env['MQ_PORT'] || '15672'),
                    username: process.env['MQ_USER'] || 'imeepos',
                    password: process.env['MQ_PASS'] || '123qwe',
                    protocol: 'amqp'
                }
                return new Amqp(options)
            },
            deps: []
        },
        {
            provide: SystemMessage,
            useFactory: (injector: Injector) => {
                const db = injector.get(Db)
                const amqp = injector.get(Amqp)
                return new SystemMessage(amqp, db)
            },
            deps: [Injector]
        }
    ],
    imports: [
        RabbitmqModule.forRoot(),
        TypeormModule.forFeature([
            WbCity,
            WbEvent,
            WbEventType,
            WbEventCategory,
            WbPlatform,
            SpilderTopic,
            SpilderTopicArticle,
            SpilderTopicArticleComment,
            SpilderAccount,
            SpilderExportLog,
            SpilderTopicTend,
            SpilderTopicArticleTend,
            SpilderTopicArticleCommentTend,
            WbTask,
            SpilderTopicArticleCommentUnique,
            CountArticle,
            CountArticleComment,
            SpiderOnline,
            SpiderUrl,
            Keyword,
            SpiderUser,
            Gonggao,
            SpiderNews,
            Setting,
            News,
            SysMessageEntity,
            CrawlingUrl
        ]),
        RabbitmqModule.forRoot(),
        TasksModule.forRoot(),
        RedisModule,
    ],
    controllers: [
        LoginController,
        SystemInfo,
        FenciController,
        BindUserController,
        MessageController,
        CrawlingController
    ]
})
export class WeiboModule { }
