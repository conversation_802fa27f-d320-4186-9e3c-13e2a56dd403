"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[286],{8286:(T,r,e)=>{e.r(r),e.d(r,{TrendTopicModule:()=>l});var p=e(5708),c=e(485);let d=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275cmp=c.Xpm({type:n,selectors:[["trend-topic"]],decls:0,vars:0,template:function(t,u){}}),n})(),l=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=c.oAB({type:n}),n.\u0275inj=c.cJS({imports:[p.Bz.forChild([{path:"",component:d}])]}),n})()}}]);