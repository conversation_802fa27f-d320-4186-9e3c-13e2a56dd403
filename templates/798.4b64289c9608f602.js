"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[798],{798:(r,s,t)=>{t.r(s),t.d(s,{TaskPageModule:()=>u});var c=t(5708),o=t(485);let l=(()=>{class e{}return e.\u0275fac=function(n){return new(n||e)},e.\u0275cmp=o.Xpm({type:e,selectors:[["task-page"]],decls:0,vars:0,template:function(n,p){}}),e})(),u=(()=>{class e{}return e.\u0275fac=function(n){return new(n||e)},e.\u0275mod=o.oAB({type:e}),e.\u0275inj=o.cJS({imports:[c.Bz.forChild([{path:"",component:l}])]}),e})()}}]);