"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[961],{6961:(r,n,e)=>{e.r(n),e.d(n,{BigDetailModule:()=>u});var c=e(5708),a=e(485);let o=(()=>{class t{}return t.\u0275fac=function(i){return new(i||t)},t.\u0275cmp=a.Xpm({type:t,selectors:[["big-detail"]],decls:0,vars:0,template:function(i,s){}}),t})(),u=(()=>{class t{}return t.\u0275fac=function(i){return new(i||t)},t.\u0275mod=a.oAB({type:t}),t.\u0275inj=a.cJS({imports:[c.Bz.forChild([{path:"",component:o}])]}),t})()}}]);