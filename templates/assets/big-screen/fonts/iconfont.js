(function(window){var svgSprite='<svg><symbol id="icon-xin" viewBox="0 0 1024 1024"><path d="M714.07688 108.265796c-78.336136 0-152.170748 35.124985-201.437825 92.215238-49.267077-57.091276-123.099642-92.215238-201.437825-92.215238-138.79409 0-246.201331 105.373932-246.201331 241.502309 0 166.860309 152.171772 300.780391 382.722658 507.156692l64.916499 57.092299 64.916499-57.092299C808.108486 650.548496 960.279235 516.628414 960.279235 349.768105 960.279235 213.639728 852.871993 108.265796 714.07688 108.265796L714.07688 108.265796zM540.268322 799.156092l-9.093099 8.131191-18.535145 16.305361-18.534122-16.305361-9.049097-8.131191c-108.544135-97.111758-202.224748-180.978864-264.211521-255.971859-60.151985-72.851215-86.993307-132.500756-86.993307-193.417151 0-47.977711 18.142196-92.172259 51.058886-124.455523 32.786731-32.17377 77.638241-49.901527 126.292358-49.901527 56.260351 0 111.86681 25.530466 148.763139 68.284199l52.67571 61.112869 52.677756-61.112869c36.895305-42.753733 92.499717-68.284199 148.760069-68.284199 48.65514 0 93.50665 17.727757 126.292358 49.901527 32.917714 32.283264 51.058886 76.477812 51.058886 124.455523 0 60.915371-26.841322 120.564913-86.992283 193.417151C742.449068 618.177228 648.767432 702.044334 540.268322 799.156092L540.268322 799.156092zM540.268322 799.156092"  ></path></symbol><symbol id="icon-pinglun" viewBox="0 0 1024 1024"><path d="M511.999488 847.882863c-28.734592 0-56.729303-2.604314-83.969807-7.099698L231.232673 960.185602 231.232673 761.40735C128.618486 689.355337 62.772174 578.889433 62.772174 454.825836c0-217.07906 201.129864-393.058051 449.228338-393.058051 248.084146 0 449.228338 175.980014 449.228338 393.058051C961.227826 671.917176 760.083635 847.882863 511.999488 847.882863zM511.999488 117.91762c-217.086932 0-393.074156 150.851707-393.074156 336.907193 0 114.166179 66.421434 214.898395 167.761552 275.820929l-1.768346 130.234133 132.171551-79.455633c30.4487 6.497994 62.117231 10.308787 94.910422 10.308787 217.101258 0 393.073132-150.825101 393.073132-336.907193C905.073644 268.769326 729.10177 117.91762 511.999488 117.91762zM736.614169 510.976694c-31.011542 0-56.154182-25.128307-56.154182-56.150858 0-31.010271 25.14264-56.151881 56.154182-56.151881s56.154182 25.14161 56.154182 56.151881C792.768351 485.848387 767.624687 510.976694 736.614169 510.976694zM511.999488 510.976694c-31.010518 0-56.153158-25.128307-56.153158-56.150858 0-31.010271 25.14264-56.151881 56.153158-56.151881 31.011542 0 56.154182 25.14161 56.154182 56.151881C568.15367 485.848387 543.01103 510.976694 511.999488 510.976694zM287.385831 510.976694c-31.010518 0-56.153158-25.128307-56.153158-56.150858 0-31.010271 25.14264-56.151881 56.153158-56.151881s56.153158 25.14161 56.153158 56.151881C343.53899 485.848387 318.39635 510.976694 287.385831 510.976694z"  ></path></symbol></svg>';var script=function(){var scripts=document.getElementsByTagName("script");return scripts[scripts.length-1]}();var shouldInjectCss=script.getAttribute("data-injectcss");var ready=function(fn){if(document.addEventListener){if(~["complete","loaded","interactive"].indexOf(document.readyState)){setTimeout(fn,0)}else{var loadFn=function(){document.removeEventListener("DOMContentLoaded",loadFn,false);fn()};document.addEventListener("DOMContentLoaded",loadFn,false)}}else if(document.attachEvent){IEContentLoaded(window,fn)}function IEContentLoaded(w,fn){var d=w.document,done=false,init=function(){if(!done){done=true;fn()}};var polling=function(){try{d.documentElement.doScroll("left")}catch(e){setTimeout(polling,50);return}init()};polling();d.onreadystatechange=function(){if(d.readyState=="complete"){d.onreadystatechange=null;init()}}}};var before=function(el,target){target.parentNode.insertBefore(el,target)};var prepend=function(el,target){if(target.firstChild){before(el,target.firstChild)}else{target.appendChild(el)}};function appendSvg(){var div,svg;div=document.createElement("div");div.innerHTML=svgSprite;svgSprite=null;svg=div.getElementsByTagName("svg")[0];if(svg){svg.setAttribute("aria-hidden","true");svg.style.position="absolute";svg.style.width=0;svg.style.height=0;svg.style.overflow="hidden";prepend(svg,document.body)}}if(shouldInjectCss&&!window.__iconfont__svg__cssinject__){window.__iconfont__svg__cssinject__=true;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(e){console&&console.log(e)}}ready(appendSvg)})(window)