# 基于 Node.js 的 lts镜像
FROM node:18.12.0-alpine3.15 

# 定义环境变量
ENV WORKDIR=/data/node/app
ENV NODE_ENV=production
ENV NODE_PORT=8081
ENV NPM_CONFIG_LOGLEVEL warn

ENV PM2=pm2-runtime
ENV LANG=C.UTF-8

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories

# Installs latest Chromium (100) package.
RUN apk add --no-cache chromium chromium-chromedriver nss freetype harfbuzz ttf-freefont yarn
RUN apk --no-cache add openssl ca-certificates g++ gcc libgcc libstdc++ linux-headers make python3 git 
RUN apk add --no-cache patch

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true 
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# # 创建应用程序文件夹并分配权限给 node 用户
RUN mkdir -p ${WORKDIR}
RUN mkdir -p ${WORKDIR}/config
RUN mkdir -p ${WORKDIR}/logs

# 设置工作目录
WORKDIR ${WORKDIR}

# # 设置活动用户
# USER node

# # 复制 package.json 到工作目录
COPY package.json ${WORKDIR}/
COPY dist ${WORKDIR}/dist
COPY templates ${WORKDIR}/templates
COPY static ${WORKDIR}/static

VOLUME ${WORKDIR}/config
VOLUME ${WORKDIR}/logs
VOLUME ${WORKDIR}/data
# RUN npm set registry http://************:4873/
# RUN npm i -g pnpm
# RUN pnpm set registry http://localhost:4873/
# RUN npm config set registry https://registry.npmmirror.com
# # 安装依赖
RUN npm install
# RUN pnpm upgrade --latest

# # 复制其他文件
# COPY --chown=node:node . .

# # 暴露主机端口
EXPOSE ${NODE_PORT}

# # 应用程序启动命令
CMD [ "./node_modules/pm2/bin/pm2-runtime","start","dist/master.js"]
